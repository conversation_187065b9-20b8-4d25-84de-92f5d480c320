2025-06-17 11:15:36.191 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-06-17 11:15:37.418 [] INFO [main] com.alibaba.boot.nacos.config.util.NacosConfigPropertiesUtils[47] nacosConfigProperties : NacosConfigProperties{serverAddr='http://mse-b81bd222-nacos-ans.mse.aliyuncs.com', contextPath='null', encode='null', endpoint='null', namespace='612ffc01-c1b8-4698-9a3d-2d4f9404694f', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='application,insurance-report.yml', group='safes', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=true}}
2025-06-17 11:15:37.420 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[675] No active profile set, falling back to default profiles: default
2025-06-17 11:15:42.147 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[244] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-17 11:15:42.155 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[126] Bootstrapping Spring Data repositories in DEFAULT mode.
2025-06-17 11:15:42.272 [] INFO [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate[182] Finished Spring Data repository scanning in 72ms. Found 0 repository interfaces.
2025-06-17 11:15:43.060 [] INFO [main] org.springframework.cloud.context.scope.GenericScope[295] BeanFactory id=339647d2-d3a8-3976-8a86-2242fbc3a647
2025-06-17 11:15:43.115 [] INFO [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor[48] Post-processing PropertySource instances
2025-06-17 11:15:43.223 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-06-17 11:15:43.223 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-06-17 11:15:43.224 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-06-17 11:15:43.224 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-17 11:15:43.225 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-17 11:15:43.225 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-06-17 11:15:43.225 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-17 11:15:43.226 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-17 11:15:43.226 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource application|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-17 11:15:43.226 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource insurance-report.yml|safes|612ffc01-c1b8-4698-9a3d-2d4f9404694f||http://mse-b81bd222-nacos-ans.mse.aliyuncs.com||||||| [com.alibaba.nacos.spring.core.env.NacosPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-17 11:15:43.227 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource applicationConfig: [classpath:/bootstrap.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-17 11:15:43.227 [] INFO [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter[38] Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-17 11:15:43.823 [] INFO [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[330] Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$e8e90b0d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-17 11:15:44.421 [] INFO [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver[34] Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-06-17 11:15:44.426 [] INFO [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector[31] Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-06-17 11:15:45.076 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[90] Tomcat initialized with port(s): 10120 (http)
2025-06-17 11:15:45.106 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Initializing ProtocolHandler ["http-nio-10120"]
2025-06-17 11:15:45.121 [] INFO [main] org.apache.catalina.core.StandardService[173] Starting service [Tomcat]
2025-06-17 11:15:45.121 [] INFO [main] org.apache.catalina.core.StandardEngine[173] Starting Servlet engine: [Apache Tomcat/9.0.16]
2025-06-17 11:15:45.140 [] INFO [main] org.apache.catalina.core.AprLifecycleListener[173] The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
2025-06-17 11:15:45.388 [] INFO [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/][173] Initializing Spring embedded WebApplicationContext
2025-06-17 11:15:45.389 [] INFO [main] org.springframework.web.context.ContextLoader[296] Root WebApplicationContext: initialization completed in 7884 ms
2025-06-17 11:15:47.011 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[62] 自定义RedisCacheManager加载完成
2025-06-17 11:15:47.109 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceDwConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceDwConfig$$EnhancerBySpringCGLIB$$9c97a535(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-06-17 11:15:47.492 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceOdpsConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceOdpsConfig$$EnhancerBySpringCGLIB$$f973cf9a(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-06-17 11:15:47.568 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesConfig$$EnhancerBySpringCGLIB$$b79db2ac(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-06-17 11:15:47.627 [] INFO [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor[342] Inconsistent constructor declaration on bean with name 'dataSourceSafesPgConfig': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.cfpamf.ms.insur.report.config.DataSourceSafesPgConfig$$EnhancerBySpringCGLIB$$62a8c963(org.mybatis.spring.boot.autoconfigure.MybatisProperties)
2025-06-17 11:15:50.401 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] classicInsuranceAmountSummaryService支持的诊断类型为CLASSIC_INSURANCE_AMOUNT_SUMMARY,加载成功！
2025-06-17 11:15:50.402 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] diagnosisAndConclusionService支持的诊断类型为DIAGNOSIS_AND_CONCLUSION,加载成功！
2025-06-17 11:15:50.402 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] insuranceAmountRateSummaryService支持的诊断类型为INSURANCE_AMOUNT_RATE_SUMMARY,加载成功！
2025-06-17 11:15:50.402 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] loanNormInsuranceAmtDiagnosisService支持的诊断类型为LOAN_NORM_INSURANCE_AMT_DIAGNOSIS,加载成功！
2025-06-17 11:15:50.402 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] loanNormInsuranceAmtSummaryService支持的诊断类型为LOAN_NORM_INSURANCE_AMT_SUMMARY,加载成功！
2025-06-17 11:15:50.402 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] loanRpNormInsuranceAmtSummaryService支持的诊断类型为LOAN_RP_NORM_INSURANCE_AMT_SUMMARY,加载成功！
2025-06-17 11:15:50.403 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] retentionRateSummaryService支持的诊断类型为RETENTION_RATE_SUMMARY,加载成功！
2025-06-17 11:15:50.403 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] rpNormInsuranceAmtAssistantService支持的诊断类型为RP_NORM_INSURANCE_AMT_ASSISTANT,加载成功！
2025-06-17 11:15:50.403 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] unloanNcNormInsuranceAmtAssistantService支持的诊断类型为UNLOAN_NC_NORM_INSURANCE_AMT_ASSISTANT,加载成功！
2025-06-17 11:15:50.403 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] unloanNcNormInsuranceAmtSummaryService支持的诊断类型为UNLOAN_NC_NORM_INSURANCE_AMT_SUMMARY,加载成功！
2025-06-17 11:15:50.403 [] INFO [main] com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisServiceAdaptorImpl[48] unloanRpNormInsuranceAmtSummaryService支持的诊断类型为UNLOAN_RP_NORM_INSURANCE_AMT_SUMMARY,加载成功！
2025-06-17 11:15:50.875 [] DEBUG [main] com.cfpamf.ms.insur.report.config.RedisConfig[74] 自定义RedisTemplate加载完成
2025-06-17 11:15:51.701 [] INFO [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver[59] Exposing 2 endpoint(s) beneath base path '/actuator'
2025-06-17 11:15:52.446 [] INFO [main] io.lettuce.core.EpollProvider[68] Starting without optional epoll library
2025-06-17 11:15:52.450 [] INFO [main] io.lettuce.core.KqueueProvider[70] Starting without optional kqueue library
2025-06-17 11:15:52.984 [] INFO [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping[69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-06-17 11:15:53.099 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-17 11:15:53.112 [] INFO [main] com.netflix.config.sources.URLConfigurationSource[122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-17 11:15:53.614 [] INFO [main] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[171] Initializing ExecutorService 'applicationTaskExecutor'
2025-06-17 11:15:54.547 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.util.MsUtil CLASS_CACHE cache.
2025-06-17 11:15:54.548 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.genid.GenIdUtil CACHE cache.
2025-06-17 11:15:54.549 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[60] Clear tk.mybatis.mapper.version.VersionUtil CACHE cache.
2025-06-17 11:15:54.552 [] INFO [main] tk.mybatis.mapper.autoconfigure.MapperCacheDisabler[83] Clear EntityHelper entityTableMap cache.
2025-06-17 11:15:55.916 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[160] Context refreshed
2025-06-17 11:15:55.959 [] INFO [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper[163] Found 1 custom documentation plugin(s)
2025-06-17 11:15:56.007 [] INFO [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner[41] Scanning for api listing references
2025-06-17 11:15:57.528 [] INFO [main] org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor[295] No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-06-17 11:15:57.558 [] INFO [main] org.apache.coyote.http11.Http11NioProtocol[173] Starting ProtocolHandler ["http-nio-10120"]
2025-06-17 11:15:57.584 [] INFO [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer[204] Tomcat started on port(s): 10120 (http) with context path ''
2025-06-17 11:15:57.591 [] INFO [main] com.cfpamf.ms.insur.report.InsuranceReportBizApplication[59] Started InsuranceReportBizApplication in 23.975 seconds (JVM running for 25.616)
2025-06-17 11:16:29.849 [] INFO [http-nio-10120-exec-1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/][173] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 11:16:29.858 [] INFO [http-nio-10120-exec-1] org.springframework.web.servlet.DispatcherServlet[524] Initializing Servlet 'dispatcherServlet'
2025-06-17 11:16:29.940 [] INFO [http-nio-10120-exec-1] org.springframework.web.servlet.DispatcherServlet[546] Completed initialization in 82 ms
2025-06-17 11:17:34.470 [] INFO [http-nio-10120-exec-7] com.cfpamf.ms.insur.report.advice.GlobalExceptionHandler[37] 业务正常校验异常
com.cfpamf.common.ms.exception.MSBizNormalException: 无法获取网关token
	at com.cfpamf.ms.insur.report.service.BmsService.getToken(BmsService.java:78)
	at com.cfpamf.ms.insur.report.service.BmsService.getContextUserDetail(BmsService.java:67)
	at com.cfpamf.ms.insur.report.web.AssistantSalerController.empRankMonth(AssistantSalerController.java:78)
	at com.cfpamf.ms.insur.report.web.AssistantSalerController$$FastClassBySpringCGLIB$$310476c6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.cfpamf.ms.insur.report.web.AssistantSalerController$$EnhancerBySpringCGLIB$$270093da.empRankMonth(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:800)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:897)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:645)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.cfpamf.ms.insur.report.advice.RequestFilter.doFilter(RequestFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:151)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:81)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-06-17 11:19:19.736 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] ---> GET http://bms-service.tsg.cfpamf.com/user/detail/safes HTTP/1.1
2025-06-17 11:19:19.741 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] authorization: eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.zhohKxH7kxztrnocyGugX3I9rKBdUxrjYtWMcqFWOiqOodmHMnPMXt_7MtniFL_FMML4_V7knFPa_2CA4-VSDA
2025-06-17 11:19:19.743 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] ---> END HTTP (0-byte body)
2025-06-17 11:19:19.964 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] <--- HTTP/1.1 200 OK (217ms)
2025-06-17 11:19:19.965 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] content-length: 13889
2025-06-17 11:19:19.965 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] content-type: application/json
2025-06-17 11:19:19.972 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] date: Tue, 17 Jun 2025 03:19:20 GMT
2025-06-17 11:19:19.975 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] server: istio-envoy
2025-06-17 11:19:19.976 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] x-application-context: bms-service-biz:test:10027
2025-06-17 11:19:19.976 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] x-envoy-upstream-service-time: 39
2025-06-17 11:19:19.977 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] 
2025-06-17 11:19:19.981 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] {
	"code":"",
	"data":{
		"areaOrgCode":"",
		"batchNo":"20220714005",
		"birthday":-209606400000,
		"createMethod":"hr",
		"description":"",
		"deviceId":"",
		"employeeId":14,
		"employeeName":"何国强",
		"employeeStatus":3,
		"employeeType":0,
		"entryDate":1651161600000,
		"entrySystemDate":1524196438000,
		"gray":0,
		"hrOrgId":447942,
		"hrOrgTreePath":"*********/282717/360674/447942",
		"hrPostId":156932,
		"hrUserId":107369318,
		"idCard":"220881196305120848",
		"isHeadOrg":true,
		"jianzhiJobNumber":"",
		"jobCode":"**********",
		"jobNumber":"CNBJ0394",
		"lastWorkDate":null,
		"mobile":"***********",
		"officeTel":"",
		"orgCode":"204",
		"orgId":669,
		"orgName":"财务部",
		"passwordLevel":2,
		"postId":221,
		"postList":[],
		"postName":"数据开发岗",
		"purpose":"",
		"roleList":[
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0012",
				"roleId":10044,
				"roleName":"信息技术部",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10031,
				"roleName":"总部员工",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10111,
				"roleName":"全量数据查看",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":10033,
				"roleName":"中/高级管理层",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0003",
				"roleId":3,
				"roleName":"总部职能部门负责人",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10160,
				"roleName":"系统管理员",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10160,
				"roleName":"系统管理员",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R1000",
				"roleId":10002,
				"roleName":"保险|管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R1000",
				"roleId":10002,
				"roleName":"保险|管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10381,
				"roleName":"保险|管理员新",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10381,
				"roleName":"保险|管理员新",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10125,
				"roleName":"系统超级管理员",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0066",
				"roleId":10422,
				"roleName":"人力总监",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0067",
				"roleId":10436,
				"roleName":"凤媛01",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0049",
				"roleId":10387,
				"roleName":"保险|渠道pco",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0005",
				"roleId":10154,
				"roleName":"技术组",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10398,
				"roleName":"超级管理员",
				"systemId":"35",
				"systemShortName":"电商平台运营系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0004",
				"roleId":10005,
				"roleName":"保险|理赔管理",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0022",
				"roleId":10190,
				"roleName":"保险|子管理员",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10001,
				"roleName":"系统管理员",
				"systemId":"1",
				"systemShortName":"基础管理平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0001",
				"roleId":10335,
				"roleName":"系统管理员",
				"systemId":"19",
				"systemShortName":"营销平台",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10911,
				"roleName":"总部员工",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":10919,
				"roleName":"中/高级管理层",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0012",
				"roleId":10914,
				"roleName":"信息技术部",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10938,
				"roleName":"全量数据查看",
				"systemId":"70",
				"systemShortName":"掌上中和",
				"userRoleType":1
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0004",
				"roleId":4,
				"roleName":"总部普通员工",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0017",
				"roleId":17,
				"roleName":"数字技术中心",
				"systemId":"2",
				"systemShortName":"运营管理平台",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0009",
				"roleId":10031,
				"roleName":"总部员工",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0038",
				"roleId":10111,
				"roleName":"全量数据查看",
				"systemId":"6",
				"systemShortName":"信贷管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0014",
				"roleId":10138,
				"roleName":"普通员工",
				"systemId":"8",
				"systemShortName":"HR系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0002",
				"roleId":10003,
				"roleName":"保险|业务管理",
				"systemId":"3",
				"systemShortName":"保险管理系统",
				"userRoleType":2
			},
			{
				"hrOrgId":447942,
				"hrOrgTreePath":"*********/282717/360674/447942",
				"hrPostId":156932,
				"jobCode":"**********",
				"orgId":669,
				"orgName":"财务部",
				"postId":221,
				"postName":"数据开发岗",
				"roleCode":"R0046",
				"roleId":10627,
				"roleName":"ERP-库存管理",
				"systemId":"11",
				"systemShortName":"电商支撑运营系统",
				"userRoleType":2
			}
		],
		"serviceType":0,
		"updateTime":*************,
		"userAccount":"***********",
		"userAdminId":"**********",
		"userAdminName":"吴玲",
		"userEmail":"",
		"userId":1939,
		"userMasterId":"0",
		"userMasterName":"",
		"userPartTimerList":null,
		"userSex":1,
		"userStatus":1
	},
	"errorCode":"",
	"errorContext":null,
	"errorMsg":"",
	"message":"",
	"success":true
}
2025-06-17 11:19:19.984 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.BmsFacade[72] [BmsFacade#getContextUserDetailForSafes] <--- END HTTP (13889-byte body)
2025-06-17 11:19:20.264 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] ---> GET http://insurance-admin.tsg.cfpamf.com/back/data/auth/dataAuth?authorization=eyJhbGciOiJIUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.zhohKxH7kxztrnocyGugX3I9rKBdUxrjYtWMcqFWOiqOodmHMnPMXt_7MtniFL_FMML4_V7knFPa_2CA4-VSDA HTTP/1.1
2025-06-17 11:19:20.265 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] ---> END HTTP (0-byte body)
2025-06-17 11:19:20.510 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] <--- HTTP/1.1 200 OK (243ms)
2025-06-17 11:19:20.511 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] content-type: application/json;charset=UTF-8
2025-06-17 11:19:20.512 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] date: Tue, 17 Jun 2025 03:19:20 GMT
2025-06-17 11:19:20.512 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] server: istio-envoy
2025-06-17 11:19:20.513 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] transfer-encoding: chunked
2025-06-17 11:19:20.514 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] x-envoy-upstream-service-time: 75
2025-06-17 11:19:20.515 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] 
2025-06-17 11:19:20.517 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] {"code":"0","traceId":"","message":"success","data":{"userId":null,"agentId":null,"regionName":null,"regionCode":null,"orgName":null,"orgCode":null,"orgAdmin":null,"channel":null,"picRole":null,"safeCenter":null,"branchBuName":null,"branchBuCode":null,"zoneName":null,"zoneCode":null,"orgPath":null,"branch":false},"success":true,"exception":null}
2025-06-17 11:19:20.518 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.feign.DataAuthFacade[72] [DataAuthFacade#dataAuth] <--- END HTTP (347-byte body)
2025-06-17 11:19:22.030 [] INFO [http-nio-10120-exec-2] com.alibaba.druid.pool.DruidDataSource[930] {dataSource-1} inited
2025-06-17 11:19:22.039 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.queryRankBch[159] ==>  Preparing: with amt_rank as ( select emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code, sm_assess_convert_insurance_amt, RANK() over (partition by bch_code order by sm_assess_convert_insurance_amt desc) rank_in_bch from report.ads_insurance_emp_marketing_progress_dfp where pt=? and bch_code = ? and (leave_date is null or TO_DATE(leave_date, 'YYYY-MM-DD') > TO_DATE(?, 'YYYYMMDD')) ) select emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code, sm_assess_convert_insurance_amt, rank_in_bch from amt_rank where emp_code = ? and bch_code = ? 
2025-06-17 11:19:22.063 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.queryRankBch[159] ==> Parameters: 20250616(String), null, 20250616(String), CNBJ0394(String), null
2025-06-17 11:19:22.176 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.queryRankBch[159] <==      Total: 0
2025-06-17 11:19:22.189 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.queryRankArea[159] ==>  Preparing: with amt_rank as ( select emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code, sm_assess_convert_insurance_amt, RANK() over (partition by area_code order by sm_assess_convert_insurance_amt desc) rank_in_area from report.ads_insurance_emp_marketing_progress_dfp where pt=? and area_code = ? and (leave_date is null or TO_DATE(leave_date, 'YYYY-MM-DD') > TO_DATE(?, 'YYYYMMDD')) ) select emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code, rank_in_area, sm_assess_convert_insurance_amt from amt_rank where emp_code = ? and area_code = ? 
2025-06-17 11:19:22.190 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.queryRankArea[159] ==> Parameters: 20250616(String), null, 20250616(String), CNBJ0394(String), null
2025-06-17 11:19:22.227 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.queryRankArea[159] <==      Total: 0
2025-06-17 11:19:22.229 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.queryRankCountry[159] ==>  Preparing: with amt_rank as ( select emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code, sm_assess_convert_insurance_amt, RANK() over (order by sm_assess_convert_insurance_amt desc) rank_in_country from report.ads_insurance_emp_marketing_progress_dfp where pt=? and (leave_date is null or TO_DATE(leave_date, 'YYYY-MM-DD') > TO_DATE(?, 'YYYYMMDD')) ) select emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code, sm_assess_convert_insurance_amt, rank_in_country from amt_rank where emp_code = ? 
2025-06-17 11:19:22.230 [] DEBUG [http-nio-10120-exec-2] com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper.queryRankCountry[159] ==> Parameters: 20250616(String), 20250616(String), CNBJ0394(String)
2025-06-17 14:19:39.973 [] INFO [Thread-17] org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor[208] Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-17 14:19:40.128 [] INFO [Thread-17] com.alibaba.druid.pool.DruidDataSource[1825] {dataSource-1} closed
2025-06-17 14:19:41.367 [] INFO [Thread-17] com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor[288] class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-06-17 14:19:41.372 [] INFO [Thread-17] com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor[288] class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
