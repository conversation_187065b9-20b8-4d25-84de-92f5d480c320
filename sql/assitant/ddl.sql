-- 全国
CREATE TABLE IF NOT EXISTS ads_insurance_cfpamf_marketing_progress_dfp
(
    sm_assess_convert_insurance_amt              DECIMAL(18, 4),
    sy_assess_convert_insurance_amt              DECIMAL(18, 4),
    sm_assess_convert_insurance_amt_target       DECIMAL(18, 4),
    sy_assess_convert_insurance_amt_target       DECIMAL(18, 4),
    sm_assess_convert_insurance_amt_achieve_rate DECIMAL(18, 4),
    sy_assess_convert_insurance_amt_achieve_rate DECIMAL(18, 4),
    sm_offline_loan_insurance_rate               DECIMAL(18, 4),
    sy_offline_loan_insurance_rate               DECIMAL(18, 4),
    sm_offline_loan_insurance_rate_target        DECIMAL(18, 4),
    sy_offline_loan_insurance_rate_target        DECIMAL(18, 4),
    sm_insurance_retention_rate                  DECIMAL(18, 4),
    sy_insurance_retention_rate                  DECIMAL(18, 4),
    sm_insurance_retention_rate_target           DECIMAL(18, 4),
    sy_insurance_retention_rate_target           DECIMAL(18, 4),
    pt                                           VARCHAR(100)
);

COMMENT ON TABLE ads_insurance_cfpamf_marketing_progress_dfp IS '保险营销整体粒度集市';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sm_assess_convert_insurance_amt IS '当月标准保费';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sy_assess_convert_insurance_amt IS '当年标准保费';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sm_assess_convert_insurance_amt_target IS '当月标准保费目标';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sy_assess_convert_insurance_amt_target IS '当年标准保费目标';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sm_assess_convert_insurance_amt_achieve_rate IS '当月标准保费达成率';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sy_assess_convert_insurance_amt_achieve_rate IS '当年标准保费达成率';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sm_offline_loan_insurance_rate IS '当月异业保费配比';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sy_offline_loan_insurance_rate IS '当年异业保费配比';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sm_offline_loan_insurance_rate_target IS '当月异业保费配比目标';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sy_offline_loan_insurance_rate_target IS '当年异业保费配比目标';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sm_insurance_retention_rate IS '当月留存率';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sy_insurance_retention_rate IS '当年留存率';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sm_insurance_retention_rate_target IS '当月留存率目标';
COMMENT ON COLUMN ads_insurance_cfpamf_marketing_progress_dfp.sy_insurance_retention_rate_target IS '当年留存率目标';

-- 区域
CREATE TABLE IF NOT EXISTS ads_insurance_area_marketing_progress_dfp
(
    area_name                                    varchar(100),
    area_code                                    varchar(100),
    sm_assess_convert_insurance_amt              DECIMAL(18, 4),
    sy_assess_convert_insurance_amt              DECIMAL(18, 4),
    sm_assess_convert_insurance_amt_target       DECIMAL(18, 4),
    sy_assess_convert_insurance_amt_target       DECIMAL(18, 4),
    sm_assess_convert_insurance_amt_achieve_rate DECIMAL(18, 4),
    sy_assess_convert_insurance_amt_achieve_rate DECIMAL(18, 4),
    sm_offline_loan_insurance_rate               DECIMAL(18, 4),
    sy_offline_loan_insurance_rate               DECIMAL(18, 4),
    sm_offline_loan_insurance_rate_target        DECIMAL(18, 4),
    sy_offline_loan_insurance_rate_target        DECIMAL(18, 4),
    sm_insurance_retention_rate                  DECIMAL(18, 4),
    sy_insurance_retention_rate                  DECIMAL(18, 4),
    sm_insurance_retention_rate_target           DECIMAL(18, 4),
    sy_insurance_retention_rate_target           DECIMAL(18, 4),
    sm_loan_insurance_retention_rate             DECIMAL(18, 4),
    sy_loan_insurance_retention_rate             DECIMAL(18, 4),
    sm_unloan_insurance_retention_rate           DECIMAL(18, 4),
    sy_unloan_insurance_retention_rate           DECIMAL(18, 4),
    sm_loan_cust_trans_rate                      DECIMAL(18, 4),
    sy_loan_cust_trans_rate                      DECIMAL(18, 4),
    pt                                           varchar(20)
);

COMMENT ON TABLE ads_insurance_area_marketing_progress_dfp IS '保险营销区域粒度集市';

COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.area_name IS '区域';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.area_code IS '区域编码';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sm_assess_convert_insurance_amt IS '当月标准保费';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sy_assess_convert_insurance_amt IS '当年标准保费';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sm_assess_convert_insurance_amt_target IS '当月标准保费目标';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sy_assess_convert_insurance_amt_target IS '当年标准保费目标';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sm_assess_convert_insurance_amt_achieve_rate IS '当月标准保费达成率';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sy_assess_convert_insurance_amt_achieve_rate IS '当年标准保费达成率';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sm_offline_loan_insurance_rate IS '当月异业保费配比';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sy_offline_loan_insurance_rate IS '当年异业保费配比';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sm_offline_loan_insurance_rate_target IS '当月异业保费配比目标';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sy_offline_loan_insurance_rate_target IS '当年异业保费配比目标';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sm_insurance_retention_rate IS '当月留存率';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sy_insurance_retention_rate IS '当年留存率';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sm_insurance_retention_rate_target IS '当月留存率目标';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sy_insurance_retention_rate_target IS '当年留存率目标';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sm_loan_insurance_retention_rate IS '当月信贷客户留存率';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sy_loan_insurance_retention_rate IS '当年信贷客户留存率';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sm_unloan_insurance_retention_rate IS '当月非信贷客户留存率';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sy_unloan_insurance_retention_rate IS '当年非信贷客户留存率';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sm_loan_cust_trans_rate IS '当月信贷客户转化率';
COMMENT ON COLUMN ads_insurance_area_marketing_progress_dfp.sy_loan_cust_trans_rate IS '当年信贷客户转化率';

-- 数仓 片区
drop table ads_insurance_district_marketing_progress_dfp;
CREATE TABLE IF NOT EXISTS ads_insurance_district_marketing_progress_dfp
(
    district_name                                varchar(100),
    district_code                                varchar(100),
    area_name                                    varchar(100),
    area_code                                    varchar(100),
    sm_assess_convert_insurance_amt              DECIMAL(18, 4),
    sy_assess_convert_insurance_amt              DECIMAL(18, 4),
    sm_assess_convert_insurance_amt_target       DECIMAL(18, 4),
    sy_assess_convert_insurance_amt_target       DECIMAL(18, 4),
    sm_assess_convert_insurance_amt_achieve_rate DECIMAL(18, 4),
    sy_assess_convert_insurance_amt_achieve_rate DECIMAL(18, 4),
    sm_offline_loan_insurance_rate               DECIMAL(18, 4),
    sy_offline_loan_insurance_rate               DECIMAL(18, 4),
    sm_offline_loan_insurance_rate_target        DECIMAL(18, 4),
    sy_offline_loan_insurance_rate_target        DECIMAL(18, 4),
    sm_insurance_retention_rate                  DECIMAL(18, 4),
    sy_insurance_retention_rate                  DECIMAL(18, 4),
    sm_insurance_retention_rate_target           DECIMAL(18, 4),
    sy_insurance_retention_rate_target           DECIMAL(18, 4),
    sm_loan_insurance_amt                        DECIMAL(18, 4),
    sy_loan_insurance_amt                        DECIMAL(18, 4),
    sm_loan_offline_amt                          DECIMAL(18, 4),
    sy_loan_offline_amt                          DECIMAL(18, 4),
    sm_insurance_expire_cust_cnt                 DECIMAL(18, 4),
    sy_insurance_expire_cust_cnt                 DECIMAL(18, 4),
    sm_insurance_expire_retention_cust_cnt       DECIMAL(18, 4),
    sy_insurance_expire_retention_cust_cnt       DECIMAL(18, 4),
    pt varchar(20)
);

COMMENT ON TABLE ads_insurance_district_marketing_progress_dfp IS '保险营销片区粒度集市';

COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.district_name IS '片区';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.district_code IS '片区编码';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.area_name IS '区域';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.area_code IS '区域编码';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sm_assess_convert_insurance_amt IS '当月标准保费';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sy_assess_convert_insurance_amt IS '当年标准保费';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sm_assess_convert_insurance_amt_target IS '当月标准保费目标';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sy_assess_convert_insurance_amt_target IS '当年标准保费目标';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sm_assess_convert_insurance_amt_achieve_rate IS '当月标准保费达成率';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sy_assess_convert_insurance_amt_achieve_rate IS '当年标准保费达成率';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sm_offline_loan_insurance_rate IS '当月异业保费配比';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sy_offline_loan_insurance_rate IS '当年异业保费配比';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sm_offline_loan_insurance_rate_target IS '当月异业保费配比目标';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sy_offline_loan_insurance_rate_target IS '当年异业保费配比目标';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sm_insurance_retention_rate IS '当月留存率';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sy_insurance_retention_rate IS '当年留存率';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sm_insurance_retention_rate_target IS '当月留存率目标';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sy_insurance_retention_rate_target IS '当年留存率目标';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sm_loan_insurance_amt IS '当月信贷规模保费';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sy_loan_insurance_amt IS '当年信贷规模保费';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sm_loan_offline_amt IS '当月信贷线下放款金额';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sy_loan_offline_amt IS '当年信贷线下放款金额';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sm_insurance_expire_cust_cnt IS '当月到期客户数';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sy_insurance_expire_cust_cnt IS '当年到期客户数';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sm_insurance_expire_retention_cust_cnt IS '当月到期留存客户数';
COMMENT ON COLUMN ads_insurance_district_marketing_progress_dfp.sy_insurance_expire_retention_cust_cnt IS '当年到期留存客户数';
