FROM registry.cn-hangzhou.aliyuncs.com/shinyn/openjdk:8
MAINTAINER zhangqin <<EMAIL>>
ARG ENV_MY_JAR
ARG ENV_MY_SER
ARG ENV_KEY
ARG ENV_PORT
ENV ENV_MY_JAR ${ENV_MY_JAR}
ENV ENV_MY_SER ${ENV_MY_SER}
ENV ENV_KEY ${ENV_KEY}
ENV ENV_PORT ${ENV_PORT}
RUN mkdir -p ${ENV_MY_SER}/logs \
	&& curl http://all.cfpamf.org.cn/ApmAgent/pinpoint-agent.tar -o ApmAgent.tar \
	&& tar xvf ApmAgent.tar -C /${ENV_MY_SER} \
	&& rm -rf ApmAgent.tar \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone
WORKDIR ${ENV_MY_SER}
ADD ${ENV_MY_SER}.tgz /${ENV_MY_SER}
RUN chmod 777 ${ENV_MY_JAR}
EXPOSE ${ENV_PORT}
CMD AGENTID=`date +%y%m%d%H%M%S` && java -Dahas.sentinel.enable=false -javaagent:/${ENV_MY_SER}/ApmAgent/pinpoint-bootstrap-1.8.4.jar -Dpinpoint.agentId=${AGENTID} -Dpinpoint.applicationName=${ENV_MY_SER} -jar -Xms1024M -Xmx2048M -Xmn880m -Xss1m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m ./${ENV_MY_JAR} --spring.profiles.active=${ENV_KEY} --spring.cloud.config.profile=${ENV_KEY} --spring.cloud.config.label=${ENV_KEY}
