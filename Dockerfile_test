FROM registry.cn-hangzhou.aliyuncs.com/shinyn/openjdk:8
MAINTAINER zhangqin <<EMAIL>>
ARG ENV_MY_JAR
ARG ENV_MY_SER
ARG ENV_KEY
ARG ENV_PORT
ENV ENV_MY_JAR ${ENV_MY_JAR}
ENV ENV_MY_SER ${ENV_MY_SER}
ENV ENV_KEY ${ENV_KEY}
ENV ENV_PORT ${ENV_PORT}
RUN mkdir -p ${ENV_MY_SER}/logs \
    && curl https://cfpamf-apk-prod.oss-cn-beijing.aliyuncs.com/test_mock/mock-agent-1.0.1-SNAPSHOT.jar -o mock-agent-1.0.1-SNAPSHOT.jar \
    && mv ./mock-agent-1.0.1-SNAPSHOT.jar /${ENV_MY_SER}/mock-agent-1.0.1-SNAPSHOT.jar \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone
WORKDIR ${ENV_MY_SER}
ADD ${ENV_MY_SER}.tgz /${ENV_MY_SER}
RUN chmod 777 ${ENV_MY_JAR}
EXPOSE ${ENV_PORT}
CMD java -Dmock.appName=insurance -Dahas.sentinel.enable=false -jar -Xms1024M -Xmx2048M -Xmn880m -Xss1m -XX:MetaspaceSize=256m -XX:+UseParallelGC -XX:MaxMetaspaceSize=256m ./${ENV_MY_JAR} --spring.profiles.active=${ENV_KEY} --spring.cloud.config.profile=${ENV_KEY} --spring.cloud.config.label=${ENV_KEY}
