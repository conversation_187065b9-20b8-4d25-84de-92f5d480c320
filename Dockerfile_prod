FROM registry.cn-hangzhou.aliyuncs.com/shinyn/openjdk:8
MAINTAINER zhangqin <<EMAIL>>
ARG ENV_MY_JAR
ARG ENV_MY_SER
ARG ENV_KEY
ARG ENV_PORT
ENV ENV_MY_JAR ${ENV_MY_JAR}
ENV ENV_MY_SER ${ENV_MY_SER}
ENV ENV_KEY ${ENV_KEY}
ENV ENV_PORT ${ENV_PORT}
RUN mkdir -p ${ENV_MY_SER}/logs \
	&& curl http://arms-apm-beijing.oss-cn-beijing.aliyuncs.com/ArmsAgent.zip -o ArmsAgent.zip \
	&& unzip ArmsAgent.zip -d /${ENV_MY_SER} \
	&& rm -rf ArmsAgent.zip \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone
WORKDIR ${ENV_MY_SER}
ADD ${ENV_MY_SER}.tgz /${ENV_MY_SER}
RUN chmod 777 ${ENV_MY_JAR}
EXPOSE ${ENV_PORT}
CMD java -javaagent:/${ENV_MY_SER}/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Dahas.namespace=prod -Darms.licenseKey=ejorpmk72q@2def507b055b3c4 -Darms.appName=${ENV_MY_SER} -jar -Xms2048M -Xmx2048M -Xmn1024m -Xss2m -XX:MetaspaceSize=256m -XX:+UseParallelGC -XX:+PrintGCDetails -XX:MaxMetaspaceSize=256m ./${ENV_MY_JAR} --spring.profiles.active=${ENV_KEY}  --spring.cloud.config.profile=${ENV_KEY} --spring.cloud.config.label=${ENV_KEY}
