# 请参考 https://help.aliyun.com/document_detail/59293.html 了解更多关于release文件的编写方式 

# 构建源码语言类型
code.language=oracle-jdk1.8

# 构建打包使用的打包文件
build.command=rm -rf /home/<USER>/.m2/repository/com/cfpamf &&mvn clean -U package -Dmaven.test.skip=true -P${PACKAGE_LABEL}
#build.command=mvn clean package -Dmaven.test.skip=true -P${PACKAGE_LABEL}
build.output=insurance-report-biz/target/${ENV_MY_JAR}

# Docker镜像构建之后push的仓库地址
docker.file=Dockerfile_${PACKAGE_LABEL}
docker.repo=registry.cn-beijing.aliyuncs.com/cfpamf_${PACKAGE_LABEL}/insurance-report

docker.tag=${TIMESTAMP}
build.tools.docker.args=--build-arg ENV_MY_JAR=${ENV_MY_JAR} --build-arg ENV_MY_SER=${ENV_MY_SER} --build-arg ENV_KEY=${ENV_KEY} --build-arg ENV_PORT=${ENV_PORT}