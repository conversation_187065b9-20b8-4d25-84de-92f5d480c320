package com.cfpamf.ms.insur.report.service;

import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.dao.safepg.AssistantAdminMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/22 16:29
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class AssistantAdminServiceTest {

    @Mock
    AssistantAdminMapper assistantAdminMapper;

    @InjectMocks
    AssistantAdminService assistantAdminService;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void getPt() {
    }

    @Test
    public void listYearPt() {
        List<String> strings = assistantAdminService.getEndDatesOfMonthsInCurrentYearAndFmt(LocalDate.now().minusDays(1L).format(BaseConstants.FMT_YYYYMMDD));
        System.err.println(strings);
    }

    @Test
    public void getAreaData() {
    }

    @Test
    public void getDistrictData() {
    }
}