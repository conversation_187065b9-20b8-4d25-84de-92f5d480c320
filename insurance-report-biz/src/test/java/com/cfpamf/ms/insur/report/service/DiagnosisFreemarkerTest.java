package com.cfpamf.ms.insur.report.service;

import com.cfpamf.ms.insur.report.config.FreemarkerConfig;
import com.cfpamf.ms.insur.report.service.diagnosis.model.ClassicInsuranceAmountSummaryModel;
import com.cfpamf.ms.insur.report.service.diagnosis.model.DiagnosisAndConclusionModel;
import com.cfpamf.ms.insur.report.service.diagnosis.model.InsuranceAmountRateSummaryModel;
import com.cfpamf.ms.insur.report.service.diagnosis.model.RetentionRateSummaryModel;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.junit.Test;

import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Arrays;

public class DiagnosisFreemarkerTest {

    @Test
    public void bizDiagnosisConclusionTest() throws IOException {
        Configuration configuration = FreemarkerConfig.getConfiguration();
        Template template = configuration.getTemplate("biz_diagnosis_conclusion.ftl");
        DiagnosisAndConclusionModel model = new DiagnosisAndConclusionModel();
        DiagnosisAndConclusionModel.Conclusion conclusion = new DiagnosisAndConclusionModel.Conclusion();
        model.setConclusion(conclusion);
        conclusion.setType("BRANCH");
        conclusion.setFinished(false);
        conclusion.setRankNames(new ArrayList<>());
        conclusion.setClassicInsuranceAmountYearRate("12.34");
        conclusion.setClassicInsuranceAmountMonthRate("1.23");
        conclusion.setClassicInsuranceAmountMonthOnMonth("0.12");
        conclusion.setClassicInsuranceAmountMonthOnMonthHasValue(true);
        conclusion.setClassicInsuranceAmountMonthOnMonthTrend(false);
        conclusion.setClassicInsuranceAmountYearAreaRank(1);
        conclusion.setClassicInsuranceAmountYearCountryRank(1);
        DiagnosisAndConclusionModel.MajorItem majorItem0 = new DiagnosisAndConclusionModel.MajorItem();
        model.addMajorItem(majorItem0);
        majorItem0.setType("INSURANCE_AMOUNT_RATE");
        majorItem0.setRankNames(new ArrayList<>());
        majorItem0.setInsuranceAmountRate("1.23");
        majorItem0.setInsuranceAmountRateTarget("12.34");
        majorItem0.setRetentionRate("1.23");
        majorItem0.setRetentionRateTarget("12.34");
        DiagnosisAndConclusionModel.MajorItem majorItem1 = new DiagnosisAndConclusionModel.MajorItem();
        model.addMajorItem(majorItem1);
        majorItem1.setType("RETENTION_RATE");
        majorItem1.setRankNames(new ArrayList<>());
        majorItem1.setInsuranceAmountRate("1.23");
        majorItem1.setInsuranceAmountRateTarget("12.34");
        majorItem1.setRetentionRate("1.23");
        majorItem1.setRetentionRateTarget("12.34");
        String result = null;
        try (StringWriter stringWriter = new StringWriter()) {
            template.process(model, stringWriter);
            result = stringWriter.toString();
        } catch (IOException | TemplateException e) {
            throw new RuntimeException(e);
        }
        System.out.println(result);
    }

    @Test
    public void classicInsuranceAmountSummaryTest() throws IOException {
        Configuration configuration = FreemarkerConfig.getConfiguration();
        Template template = configuration.getTemplate("classic_insurance_amount_summary.ftl");
        ClassicInsuranceAmountSummaryModel model = new ClassicInsuranceAmountSummaryModel();
        ClassicInsuranceAmountSummaryModel.Summary summary = new ClassicInsuranceAmountSummaryModel.Summary();
        model.setSummary(summary);
        summary.setType("BRANCH");
        summary.setFinished(false);
        summary.setClassicInsuranceAmountYearRankNames(Arrays.asList("河北区域","河南区域","内蒙区域"));
        //summary.setClassicInsuranceAmountMonthRankNames(Arrays.asList("河北区域","河南区域","内蒙区域"));
        summary.setClassicInsuranceAmountMonthRankNames(Arrays.asList("河北区域","河南区域","内蒙区域"));
        String result = null;
        try (StringWriter stringWriter = new StringWriter()) {
            template.process(model, stringWriter);
            result = stringWriter.toString();
        } catch (IOException | TemplateException e) {
            throw new RuntimeException(e);
        }
        System.out.println(result);
    }

    @Test
    public void insuranceAmountRateSummaryTest() throws IOException {
        Configuration configuration = FreemarkerConfig.getConfiguration();
        Template template = configuration.getTemplate("insurance_amount_rate_summary.ftl");
        InsuranceAmountRateSummaryModel model = new InsuranceAmountRateSummaryModel();
        InsuranceAmountRateSummaryModel.Summary summary = new InsuranceAmountRateSummaryModel.Summary();
        model.setSummary(summary);
        summary.setType("BRANCH");
        summary.setFinished(false);
        summary.setInsuranceAmountYearRate("12.34");
        ArrayList<InsuranceAmountRateSummaryModel.Summary.RankInfo> rankInfos = new ArrayList<>(3);
        summary.setRankInfos(rankInfos);
        InsuranceAmountRateSummaryModel.Summary.RankInfo rankInfo0 = new InsuranceAmountRateSummaryModel.Summary.RankInfo();
        rankInfos.add(rankInfo0);
        rankInfo0.setName("河北区域");
        rankInfo0.setInsuranceAmountYearRate("1.23");
        InsuranceAmountRateSummaryModel.Summary.RankInfo rankInfo1 = new InsuranceAmountRateSummaryModel.Summary.RankInfo();
        rankInfos.add(rankInfo1);
        rankInfo1.setName("河南区域");
        rankInfo1.setInsuranceAmountYearRate("1.23");
        InsuranceAmountRateSummaryModel.Summary.RankInfo rankInfo2 = new InsuranceAmountRateSummaryModel.Summary.RankInfo();
        rankInfos.add(rankInfo2);
        rankInfo2.setName("内蒙区域");
        rankInfo2.setInsuranceAmountYearRate("1.23");
        String result = null;
        try (StringWriter stringWriter = new StringWriter()) {
            template.process(model, stringWriter);
            result = stringWriter.toString();
        } catch (IOException | TemplateException e) {
            throw new RuntimeException(e);
        }
        System.out.println(result);
    }

    @Test
    public void retentionRateSummaryTest() throws IOException {
        Configuration configuration = FreemarkerConfig.getConfiguration();
        Template template = configuration.getTemplate("retention_rate_summary.ftl");
        RetentionRateSummaryModel model = new RetentionRateSummaryModel();
        RetentionRateSummaryModel.Summary summary = new RetentionRateSummaryModel.Summary();
        model.setSummary(summary);
        summary.setType("BRANCH");
        summary.setFinished(false);
        summary.setRetentionYearRate("12.34");
        ArrayList<RetentionRateSummaryModel.Summary.RankInfo> rankInfos = new ArrayList<>(3);
        summary.setRankInfos(rankInfos);
        RetentionRateSummaryModel.Summary.RankInfo rankInfo0 = new RetentionRateSummaryModel.Summary.RankInfo();
        rankInfos.add(rankInfo0);
        rankInfo0.setName("河北区域");
        rankInfo0.setRetentionYearRate("1.23");
        RetentionRateSummaryModel.Summary.RankInfo rankInfo1 = new RetentionRateSummaryModel.Summary.RankInfo();
        rankInfos.add(rankInfo1);
        rankInfo1.setName("河南区域");
        rankInfo1.setRetentionYearRate("1.23");
        RetentionRateSummaryModel.Summary.RankInfo rankInfo2 = new RetentionRateSummaryModel.Summary.RankInfo();
        rankInfos.add(rankInfo2);
        rankInfo2.setName("内蒙区域");
        rankInfo2.setRetentionYearRate("1.23");
        String result = null;
        try (StringWriter stringWriter = new StringWriter()) {
            template.process(model, stringWriter);
            result = stringWriter.toString();
        } catch (IOException | TemplateException e) {
            throw new RuntimeException(e);
        }
        System.out.println(result);
    }
}
