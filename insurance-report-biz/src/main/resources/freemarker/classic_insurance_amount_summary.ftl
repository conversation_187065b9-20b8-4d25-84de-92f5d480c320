<#--
{
    "summary":{
        "type":"区域类型：COUNTRY,AREA,DISTRICT,BRANCH",
        "finished":false,
        "classicInsuranceAmountMonthRankNames":["rankName"],
        "classicInsuranceAmountYearRankNames":["rankName"]
    }
}
-->
<#--标准保费业务完成情况小结-->
<#if summary.type == 'COUNTRY' && (summary.classicInsuranceAmountMonthRankNames?size gt 0 || summary.classicInsuranceAmountYearRankNames?size gt 0)>
<#--全国-->
<div style="padding: 13px 12px;font-size: 15px;;color: #666;border-radius: 8px;background: rgba(47, 104, 254, 0.05);">
    <#if summary.classicInsuranceAmountMonthRankNames?size gt 0>
        本月区域业绩完成排名靠
        <#if summary.finished>
            前TOP${summary.classicInsuranceAmountMonthRankNames?size}：<span style="font-size: 16px;color: #2F68FE;"><#list summary.classicInsuranceAmountMonthRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.classicInsuranceAmountMonthRankNames?size}：<span style="color: #FF0000;"><#list summary.classicInsuranceAmountMonthRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        </#if>
    </#if>
    <#if summary.classicInsuranceAmountMonthRankNames?size gt 0 && summary.classicInsuranceAmountYearRankNames?size gt 0>，</#if>
    <#if summary.classicInsuranceAmountYearRankNames?size gt 0>
        本年区域业绩完成排名靠
        <#if summary.finished>
            前TOP${summary.classicInsuranceAmountYearRankNames?size}： <span style="font-size: 16px;color: #2F68FE;"><#list summary.classicInsuranceAmountYearRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.classicInsuranceAmountYearRankNames?size}： <span style="color: #FF0000;"><#list summary.classicInsuranceAmountYearRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>，需重点跟进
        </#if>
    </#if>。
</div>
<#elseif (summary.type == 'DISTRICT') && (summary.classicInsuranceAmountMonthRankNames?size gt 0 || summary.classicInsuranceAmountYearRankNames?size gt 0)>
<#--片区-->
<div style="padding: 13px 12px;font-size: 15px;;color: #666;border-radius: 8px;background: rgba(47, 104, 254, 0.05);">
    <#if summary.classicInsuranceAmountMonthRankNames?size gt 0>
        本月分支业绩完成排名靠
        <#if summary.finished>
            前TOP${summary.classicInsuranceAmountMonthRankNames?size}：<span style="font-size: 16px;color: #2F68FE;"><#list summary.classicInsuranceAmountMonthRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.classicInsuranceAmountMonthRankNames?size}：<span style="color: #FF0000;"><#list summary.classicInsuranceAmountMonthRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        </#if>
    </#if>
    <#if summary.classicInsuranceAmountMonthRankNames?size gt 0 && summary.classicInsuranceAmountYearRankNames?size gt 0>，</#if>
    <#if summary.classicInsuranceAmountYearRankNames?size gt 0>
        本年分支业绩完成排名靠
        <#if summary.finished>
            前TOP${summary.classicInsuranceAmountYearRankNames?size}： <span style="font-size: 16px;color: #2F68FE;"><#list summary.classicInsuranceAmountYearRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.classicInsuranceAmountYearRankNames?size}： <span style="color: #FF0000;"><#list summary.classicInsuranceAmountYearRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>，需重点跟进
        </#if>
    </#if>。
</div>
<#elseif (summary.type == 'AREA') && existDistrict && (summary.classicInsuranceAmountMonthRankNames?size gt 0 || summary.classicInsuranceAmountYearRankNames?size gt 0)>
<#--区域-->
<div style="padding: 13px 12px;font-size: 15px;;color: #666;border-radius: 8px;background: rgba(47, 104, 254, 0.05);">
    <#if summary.classicInsuranceAmountMonthRankNames?size gt 0>
        本月片区业绩完成排名靠
        <#if summary.finished>
            前TOP${summary.classicInsuranceAmountMonthRankNames?size}：<span style="font-size: 16px;color: #2F68FE;"><#list summary.classicInsuranceAmountMonthRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.classicInsuranceAmountMonthRankNames?size}：<span style="color: #FF0000;"><#list summary.classicInsuranceAmountMonthRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        </#if>
    </#if>
    <#if summary.classicInsuranceAmountMonthRankNames?size gt 0 && summary.classicInsuranceAmountYearRankNames?size gt 0>，</#if>
    <#if summary.classicInsuranceAmountYearRankNames?size gt 0>
        本年片区业绩完成排名靠
        <#if summary.finished>
            前TOP${summary.classicInsuranceAmountYearRankNames?size}： <span style="font-size: 16px;color: #2F68FE;"><#list summary.classicInsuranceAmountYearRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.classicInsuranceAmountYearRankNames?size}： <span style="color: #FF0000;"><#list summary.classicInsuranceAmountYearRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>，需重点跟进
        </#if>
    </#if>。
</div>
<#elseif (summary.type == 'AREA') && !existDistrict && (summary.classicInsuranceAmountMonthRankNames?size gt 0 || summary.classicInsuranceAmountYearRankNames?size gt 0)>
<#--区域下没有片区-->
<div style="padding: 13px 12px;font-size: 15px;;color: #666;border-radius: 8px;background: rgba(47, 104, 254, 0.05);">
    <#if summary.classicInsuranceAmountMonthRankNames?size gt 0>
        本月分支业绩完成排名靠
        <#if summary.finished>
            前TOP${summary.classicInsuranceAmountMonthRankNames?size}：<span style="font-size: 16px;color: #2F68FE;"><#list summary.classicInsuranceAmountMonthRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.classicInsuranceAmountMonthRankNames?size}：<span style="color: #FF0000;"><#list summary.classicInsuranceAmountMonthRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        </#if>
    </#if>
    <#if summary.classicInsuranceAmountMonthRankNames?size gt 0 && summary.classicInsuranceAmountYearRankNames?size gt 0>，</#if>
    <#if summary.classicInsuranceAmountYearRankNames?size gt 0>
        本年分支业绩完成排名靠
        <#if summary.finished>
            前TOP${summary.classicInsuranceAmountYearRankNames?size}： <span style="font-size: 16px;color: #2F68FE;"><#list summary.classicInsuranceAmountYearRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.classicInsuranceAmountYearRankNames?size}： <span style="color: #FF0000;"><#list summary.classicInsuranceAmountYearRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>，需重点跟进
        </#if>
    </#if>。
</div>
<#elseif summary.type == 'BRANCH' && (summary.classicInsuranceAmountMonthRankNames?size gt 0 || summary.classicInsuranceAmountYearRankNames?size gt 0)>
<#--分支-->
<div style="padding: 13px 12px;font-size: 15px;;color: #666;border-radius: 8px;background: rgba(47, 104, 254, 0.05);">
    <#if summary.classicInsuranceAmountMonthRankNames?size gt 0>
        本月个人业绩完成排名靠
        <#if summary.finished>
            前TOP${summary.classicInsuranceAmountMonthRankNames?size}：<span style="font-size: 16px;color: #2F68FE;"><#list summary.classicInsuranceAmountMonthRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.classicInsuranceAmountMonthRankNames?size}：<span style="color: #FF0000;"><#list summary.classicInsuranceAmountMonthRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        </#if>
    </#if>
    <#if summary.classicInsuranceAmountMonthRankNames?size gt 0 && summary.classicInsuranceAmountYearRankNames?size gt 0>，</#if>
    <#if summary.classicInsuranceAmountYearRankNames?size gt 0>
        本年个人业绩完成排名靠
        <#if summary.finished>
            前TOP${summary.classicInsuranceAmountYearRankNames?size}： <span style="font-size: 16px;color: #2F68FE;"><#list summary.classicInsuranceAmountYearRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>
        <#else>
            后TOP${summary.classicInsuranceAmountYearRankNames?size}： <span style="color: #FF0000;"><#list summary.classicInsuranceAmountYearRankNames as rankName>${rankName}<#sep>、</#sep></#list></span>，需重点跟进
        </#if>
    </#if>。
</div>
</#if>