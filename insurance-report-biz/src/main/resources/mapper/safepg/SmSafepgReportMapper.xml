<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.report.dao.safepg.SmSafepgReportMapper">

    <select id="listOrgCommission" resultType="com.cfpamf.ms.insur.report.pojo.vo.SmOrgCommissionReportVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        bch_name as orgName,
        sum(COALESCE(qty,0)) as insuredCnt,
        sum(COALESCE(amount,0)) as insuredAmt,
        sum(COALESCE(commission_amount,0)) as cmsnExpe,
        sum(COALESCE(settlement_amount,0)) as cmsnIncome
        from phoenix.rpt_insurance_performance_bch_mip
        where 1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE
            CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>

        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>

        <if test='startDate != null'>
            AND rpt_date>=#{startDate}
        </if>


        <if test='endDate != null'>
            <![CDATA[  AND rpt_date<= #{endDate} ]]>
        </if>

        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by area_name ,bch_name
    </select>
    <select id="getInsuranceOrderAllStat"
            resultType="com.cfpamf.ms.insur.report.pojo.dto.InsuranceOrderAllStatDTO">
        select rpt_date                        as rptDate,
               COALESCE(insured_cust_day, 0)   as insuredCustDay,
               COALESCE(insured_cnt_day, 0)    as insuredCntDay,
               COALESCE(insured_amt_day, 0)    as insuredAmtDay,
               COALESCE(insured_cust_month, 0) as insuredCustMonth,
               COALESCE(insured_cnt_month, 0)  as insuredCntMonth,
               COALESCE(insured_amt_month, 0)  as insuredAmtMonth,
               COALESCE(insured_cust_year, 0)  as insuredCustYear,
               COALESCE(insured_cnt_year, 0)   as insuredCntYear,
               COALESCE(insured_amt_year, 0)   as insuredAmtYear,
               COALESCE(insured_cust_all, 0)   as insuredCustAll,
               COALESCE(insured_cnt_all, 0)    as insuredCntAll,
               COALESCE(insured_amt_all, 0)    as insuredAmtAll
        from phoenix.rpt_insurance_order_all_stat_dfp
        order by rpt_date desc limit 1;
    </select>

    <!-- 获取当前能用的最大PT值
            一般为所有需要应用的表中最小的那个pt值，代表当前所需表的完整数据是在哪个pt分区
    -->
    <select id = "getMaxAvailablePtForTables" resultType="java.lang.String">
        SELECT min(pt) max_pt FROM report.datawork_pt_sync_mark
        <if test="tableNameList != null and tableNameList.size() > 0">
            where table_name in
            <foreach collection="tableNameList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
            </foreach>
        </if>
    </select>
</mapper>