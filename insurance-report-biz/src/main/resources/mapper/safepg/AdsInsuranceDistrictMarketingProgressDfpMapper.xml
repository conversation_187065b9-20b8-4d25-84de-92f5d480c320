<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceDistrictMarketingProgressDfpMapper">
    <resultMap id="AdsInsuranceDistrictMarketingProgressDfpMap"
               type="com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceDistrictMarketingProgressDfp">

    </resultMap>
    <select id="queryDiagnosisData"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.DiagnosisAndConclusionService$DiagnosisAndConclusionVo">
        select
        sum(aidmpd.sy_assess_convert_insurance_amt)/sum(aidmpd.sy_assess_convert_insurance_amt_target) as
        sy_assess_convert_insurance_amt_achieve_rate,
        sum(aidmpd.sm_assess_convert_insurance_amt)/sum(aidmpd.sm_assess_convert_insurance_amt_target) as
        sm_assess_convert_insurance_amt_achieve_rate,
        case
        when sum(aidmpd.lm_assess_convert_insurance_amt) = 0 then null
        else (sum(aidmpd.sm_assess_convert_insurance_amt)-sum(aidmpd.lm_assess_convert_insurance_amt))/
        sum(aidmpd.lm_assess_convert_insurance_amt)
        end as sm_assess_convert_insurance_amt_month_on_month_rate,
        sum(aidmpd.sy_loan_insurance_amt)/sum(aidmpd.sy_loan_offline_amt)*10000 as sy_offline_loan_insurance_rate ,
        avg(aidmpd.sm_offline_loan_insurance_rate_target) as sm_offline_loan_insurance_rate_target,
        sum(aidmpd.sy_insurance_expire_retention_cust_cnt)/sum(sy_insurance_expire_cust_cnt) as
        sy_insurance_retention_rate ,
        avg(aidmpd.sm_insurance_retention_rate_target) as sm_insurance_retention_rate_target
        from
        report.ads_insurance_district_marketing_progress_dfp aidmpd
        where
        aidmpd.pt = #{pt}
        and aidmpd.district_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        group by aidmpd.pt;
    </select>
    <select id="queryAssessConvertInsuranceAmtSummary" resultType="java.lang.Boolean">
        select
        case
        when sum(aidmpd.sm_assess_convert_insurance_amt) >= sum(aidmpd.sm_assess_convert_insurance_amt_target) then true
        else false
        end as check_result
        from
        report.ads_insurance_district_marketing_progress_dfp aidmpd
        where
        aidmpd.pt = #{pt}
        and aidmpd.district_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        group by aidmpd.pt;
    </select>
    <select id="queryInsuranceAmountRateSummary"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.InsuranceAmountRateSummaryService$InsuranceAmountRateSummaryVo">
        select
        avg(aidmpd.sm_offline_loan_insurance_rate_target) as sm_offline_loan_insurance_rate_target ,
        sum(aidmpd.sy_loan_insurance_amt)/sum(aidmpd.sy_loan_offline_amt)*10000 as sy_offline_loan_insurance_rate
        from
        report.ads_insurance_district_marketing_progress_dfp aidmpd
        where
        aidmpd.pt = #{pt}
        and aidmpd.district_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        group by aidmpd.pt;
    </select>
    <select id="queryRetentionRateSummary"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.RetentionRateSummaryService$RetentionRateSummaryVo">
        select
        avg(aidmpd.sm_insurance_retention_rate_target) as sm_insurance_retention_rate_target ,
        sum(aidmpd.sy_insurance_expire_retention_cust_cnt)/sum(aidmpd.sy_insurance_expire_cust_cnt) as
        sy_insurance_retention_rate
        from
        report.ads_insurance_district_marketing_progress_dfp aidmpd
        where
        aidmpd.pt = #{pt}
        and aidmpd.district_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        group by aidmpd.pt;
    </select>

    <select id="queryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        rank,
        string_agg(sm_assess_convert_insurance_amt_achieve_rate_rank_name,',') as
        sm_assess_convert_insurance_amt_achieve_rate_rank_name,
        string_agg(sy_offline_loan_insurance_rate_rank_name,',') as sy_offline_loan_insurance_rate_rank_name,
        string_agg(sy_insurance_retention_rate_rank_name,',') as sy_insurance_retention_rate_rank_name
        from
        (
        select
        aibmpd.district_name as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
        null as sy_offline_loan_insurance_rate_rank_name,
        null as sy_insurance_retention_rate_rank_name,
        row_number() over(
        partition by aibmpd.pt
        order by
        aibmpd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aibmpd.district_code asc
        ) as rank
        from
        report.ads_insurance_district_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        <if test="orgLevel == 'AREA'">
            and aibmpd.area_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                #{orgCode}
            </foreach>
        </if>
        and aibmpd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">&gt;=</if>
        <if test="!assessConvertInsuranceAmtTag">&lt;</if>
        1
        <if test="!offlineLoanInsuranceRateTag">
            union
            select
            null as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
            aibmpd.district_name as sy_offline_loan_insurance_rate_rank_name,
            null as sy_insurance_retention_rate_rank_name,
            row_number() over(
            partition by aibmpd.pt
            order by
            aibmpd.sy_offline_loan_insurance_rate asc nulls first,
            aibmpd.district_code asc
            ) as rank
            from
            report.ads_insurance_district_marketing_progress_dfp aibmpd
            where
            aibmpd.pt = #{pt}
            <if test="orgLevel == 'AREA'">
                and aibmpd.area_code in
                <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                    #{orgCode}
                </foreach>
            </if>
            and aibmpd.sm_assess_convert_insurance_amt_achieve_rate &lt; 1
            and aibmpd.sy_offline_loan_insurance_rate &lt; aibmpd.sm_offline_loan_insurance_rate_target
        </if>
        <if test="!insuranceRetentionRateTag">
            union
            select
            null as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
            null as sy_offline_loan_insurance_rate_rank_name,
            aibmpd.district_name as sy_insurance_retention_rate_rank_name,
            row_number() over(
            partition by aibmpd.pt
            order by
            aibmpd.sy_insurance_retention_rate asc nulls first,
            aibmpd.district_code asc
            ) as rank
            from
            report.ads_insurance_district_marketing_progress_dfp aibmpd
            where
            aibmpd.pt = #{pt}
            <if test="orgLevel == 'AREA'">
                and aibmpd.area_code in
                <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                    #{orgCode}
                </foreach>
            </if>
            and aibmpd.sm_assess_convert_insurance_amt_achieve_rate &lt; 1
            and aibmpd.sy_insurance_retention_rate &lt; aibmpd.sm_insurance_retention_rate_target
        </if>
        ) as tmp0
        group by
        rank
        order by
        rank asc
        limit 3;
    </select>

    <select id="queryAssessConvertInsuranceAmtSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        t0.rank,
        string_agg(t0.sy_assess_convert_insurance_amt_achieve_rate_rank_name, ',') as
        sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        string_agg(t0.sm_assess_convert_insurance_amt_achieve_rate_rank_name, ',') as
        sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        (
        select
        row_number() over(
        partition by aibmpd.pt
        order by
        aibmpd.sy_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aibmpd.district_code asc
        ) as rank,
        aibmpd.district_name as sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        null as sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        report.ads_insurance_district_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        <if test="orgLevel== 'AREA'">
            and aibmpd.area_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">#{orgCode}</foreach>
        </if>
        and aibmpd.sy_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">&gt;=</if>
        <if test="!assessConvertInsuranceAmtTag">&lt;</if>
        1
        union
        select
        row_number() over(
        partition by aibmpd.pt
        order by
        aibmpd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aibmpd.district_code asc
        ) as rank,
        null as sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        aibmpd.district_name as sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        report.ads_insurance_district_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        <if test="orgLevel== 'AREA'">
            and aibmpd.area_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">#{orgCode}</foreach>
        </if>
        and aibmpd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">&gt;=</if>
        <if test="!assessConvertInsuranceAmtTag">&lt;</if>
        1
        ) as t0
        group by
        t0.rank
        order by
        t0.rank asc
        limit 3;
    </select>

    <select id="queryInsuranceAmountRateSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        aibmpd.district_name as sy_offline_loan_insurance_rate_rank_name,
        aibmpd.sy_offline_loan_insurance_rate,
        row_number() over(
        partition by aibmpd.pt
        order by
        aibmpd.sy_offline_loan_insurance_rate
        <if test="insuranceAmountRateTag">desc nulls last</if>
        <if test="!insuranceAmountRateTag">asc nulls first</if>,
        aibmpd.district_code asc
        ) as rank
        from
        report.ads_insurance_district_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        and aibmpd.sy_offline_loan_insurance_rate
        <if test="insuranceAmountRateTag">&gt;=</if>
        <if test="!insuranceAmountRateTag">&lt;</if>
        aibmpd.sm_offline_loan_insurance_rate_target
        <if test="orgLevel== 'AREA'">
            and aibmpd.area_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">#{orgCode}</foreach>
        </if>
        order by
        rank asc
        limit 3;
    </select>

    <select id="queryRetentionRateSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        aibmpd.district_name as sy_insurance_retention_rate_rank_name,
        aibmpd.sy_insurance_retention_rate,
        row_number() over(
        partition by aibmpd.pt
        order by
        aibmpd.sy_insurance_retention_rate
        <if test="retentionRateTag">desc nulls last</if>
        <if test="!retentionRateTag">asc nulls first</if>,
        aibmpd.district_code asc
        ) as rank
        from
        report.ads_insurance_district_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        and aibmpd.sy_insurance_retention_rate
        <if test="retentionRateTag">&gt;=</if>
        <if test="!retentionRateTag">&lt;</if>
        aibmpd.sm_insurance_retention_rate_target
        <if test="orgLevel== 'AREA'">
            and aibmpd.area_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                #{orgCode}
            </foreach>
        </if>
        order by
        rank asc
        limit 3;
    </select>
</mapper>