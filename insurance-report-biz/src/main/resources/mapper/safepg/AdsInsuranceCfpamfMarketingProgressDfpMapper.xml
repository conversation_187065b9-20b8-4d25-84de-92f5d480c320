<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceCfpamfMarketingProgressDfpMapper">
    <resultMap id="AdsInsuranceCfpamfMarketingProgressDfpMap"
               type="com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceCfpamfMarketingProgressDfp">

    </resultMap>
    <select id="queryDiagnosisData"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.DiagnosisAndConclusionService$DiagnosisAndConclusionVo">
        select
        aicmpd.sy_assess_convert_insurance_amt_achieve_rate,
        aicmpd.sm_assess_convert_insurance_amt_achieve_rate,
        case
        when aicmpd.lm_assess_convert_insurance_amt = 0 then null
        else (aicmpd.sm_assess_convert_insurance_amt -
        aicmpd.lm_assess_convert_insurance_amt)/aicmpd.lm_assess_convert_insurance_amt
        end as sm_assess_convert_insurance_amt_month_on_month_rate,
        aicmpd.sy_offline_loan_insurance_rate ,
        aicmpd.sm_offline_loan_insurance_rate_target ,
        aicmpd.sy_insurance_retention_rate ,
        aicmpd.sm_insurance_retention_rate_target
        from
        report.ads_insurance_cfpamf_marketing_progress_dfp aicmpd
        where
        aicmpd.pt = #{pt};
    </select>
    <select id="queryAssessConvertInsuranceAmtSummary" resultType="java.lang.Boolean">
        select
        case
        when aicmpd.sm_assess_convert_insurance_amt >= aicmpd.sm_assess_convert_insurance_amt_target then true
        else false
        end as check_result
        from
        report.ads_insurance_cfpamf_marketing_progress_dfp aicmpd
        where
        aicmpd.pt = #{pt};
    </select>
    <select id="queryInsuranceAmountRateSummary"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.InsuranceAmountRateSummaryService$InsuranceAmountRateSummaryVo">
        select
        aicmpd.sm_offline_loan_insurance_rate_target ,
        aicmpd.sy_offline_loan_insurance_rate
        from
        report.ads_insurance_cfpamf_marketing_progress_dfp aicmpd
        where
        aicmpd.pt = #{pt};
    </select>
    <select id="queryRetentionRateSummary"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.RetentionRateSummaryService$RetentionRateSummaryVo">
        select
        aicmpd.sm_insurance_retention_rate_target ,
        aicmpd.sy_insurance_retention_rate
        from
        report.ads_insurance_cfpamf_marketing_progress_dfp aicmpd
        where
        aicmpd.pt = #{pt};
    </select>

</mapper>