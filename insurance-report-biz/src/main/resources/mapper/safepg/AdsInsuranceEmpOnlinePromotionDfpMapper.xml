<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpOnlinePromotionDfpMapper">

    <select id="empSummary" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
            p.sm_effective_visit_user_cnt as smVisitCnt,
            p.sm_effective_share_cnt as smShareCnt,
            p.sd_effective_visit_user_cnt as sdVisitCnt,
            p.sd_effective_share_cnt as sdShareCnt,
            p.sm_transform_customer_cnt as smTransformCustomerCnt,
            p.sm_transform_policy_cnt as smTransformPolicyCnt,
            p.area_code as areaCode,
            p.area_name as areaName,
            p.district_code as districtCode,
            p.bch_code as bchCode,
            p.bch_name as bchName,
            p.emp_wx_img_url as empWxImgUrl,
            p.sm_effective_visit_user_cnt_rank as smEffectiveVisitUserCntRank,
            p.sm_effective_share_cnt_rank as smEffectiveShareCntRank,
            p.sm_effective_visit_user_cnt_area_rank as smEffectiveVisitUserCntAreaRank,
            p.sm_effective_share_cnt_area_rank as smEffectiveShareCntAreaRank,
            p.emp_id as empId
        from report.ads_insurance_emp_online_promotion_dfp p
        WHERE 1 = 1
        AND p.pt = #{pt}
        AND p.emp_id = #{empId}
    </select>

    <select id="empRankList" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
            p.sm_effective_visit_user_cnt as smVisitCnt,
            p.sm_effective_share_cnt as smShareCnt,
            p.emp_name as empName,
            p.emp_wx_img_url as empWxImgUrl,
            p.sm_effective_visit_user_cnt_rank as smEffectiveVisitUserCntRank,
            p.sm_effective_share_cnt_rank as smEffectiveShareCntRank,
            p.sm_effective_visit_user_cnt_area_rank as smEffectiveVisitUserCntAreaRank,
            p.sm_effective_share_cnt_area_rank as smEffectiveShareCntAreaRank,
            p.emp_id as empId
        from report.ads_insurance_emp_online_promotion_dfp p
        <include refid="Base_empRankList"/>
        <if test="rankType != null and rankType == 'area'">
            and p.area_code = #{areaCode}
        </if>
        <if test="dataType != null and dataType == 'share'">
            <if test="rankType != null and rankType == 'nation'">
                order by p.sm_effective_share_cnt_rank asc, p.emp_name COLLATE "zh_CN" asc nulls last
            </if>
            <if test="rankType != null and rankType == 'area'">
                order by p.sm_effective_share_cnt_area_rank asc, p.emp_name COLLATE "zh_CN" asc nulls last
            </if>
        </if>
        <if test="dataType != null and dataType == 'visit'">
            <if test="rankType != null and rankType == 'nation'">
                order by p.sm_effective_visit_user_cnt_rank asc, p.emp_name COLLATE "zh_CN" asc nulls last
            </if>
            <if test="rankType != null and rankType == 'area'">
                order by p.sm_effective_visit_user_cnt_area_rank asc, p.emp_name COLLATE "zh_CN" asc nulls last
            </if>
        </if>
        limit #{size}
    </select>

    <select id="empRankCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        from report.ads_insurance_emp_online_promotion_dfp p
        <include refid="Base_empRankList"/>
    </select>

    <sql id="Base_empRankList">
        WHERE 1 = 1
            AND p.pt = #{pt}
    </sql>

    <select id="areaRankList" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
        p.sm_effective_visit_user_cnt as smVisitCnt,
        p.sm_effective_share_cnt as smShareCnt,
        p.sm_transform_customer_cnt as smTransformCustomerCnt,
        p.sm_transform_policy_cnt as smTransformPolicyCnt,
        p.area_name as name,
        round(p.sm_effective_share_avg_cnt,2) as smShareCntAvg,
        round(p.sm_effective_visit_user_avg_cnt,2) as smVisitCntAvg
        from report.ads_insurance_area_online_promotion_dfp p
        <include refid="Base_areaRankList"/>
        <if test="dataType != null and dataType == 'share'">
            order by sm_effective_share_cnt desc
        </if>
        <if test="dataType != null and dataType == 'visit'">
            order by sm_effective_visit_user_cnt desc
        </if>
        <if test="orderType != null">
            <if test="orderType.colName != null and orderType.colName == 'smShareCnt'">
                order by sm_effective_share_cnt
            </if>
            <if test="orderType.colName != null and orderType.colName == 'smVisitCnt'">
                order by sm_effective_visit_user_cnt
            </if>
            <if test="orderType.clause != null and orderType.clause == 'desc'">desc</if> <if test="orderType.clause != null and orderType.clause == 'asc'">asc</if> <if test="orderType.clause == null and orderType.clause == ''">desc</if>
        </if>
        <if test="page != -1">
            limit #{size} offset #{page}
        </if>
    </select>

    <select id="districtRankList" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
        p.sm_effective_visit_user_cnt as smVisitCnt,
        p.sm_effective_share_cnt as smShareCnt,
        p.sm_transform_customer_cnt as smTransformCustomerCnt,
        p.sm_transform_policy_cnt as smTransformPolicyCnt,
        p.district_name as name
        from report.ads_insurance_district_online_promotion_dfp p
        <include refid="Base_districtRankList"/>
        order by p.district_code desc
        limit #{size} offset #{page}
    </select>

    <select id="bchRankList" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
        p.sm_effective_visit_user_cnt as smVisitCnt,
        p.sm_effective_share_cnt as smShareCnt,
        p.sm_transform_customer_cnt as smTransformCustomerCnt,
        p.sm_transform_policy_cnt as smTransformPolicyCnt,
        p.bch_name as name,
        round(p.sm_effective_share_avg_cnt,2) as smShareCntAvg,
        round(p.sm_effective_visit_user_avg_cnt,2) as smVisitCntAvg
        from report.ads_insurance_bch_online_promotion_dfp p
        <include refid="Base_bchRankList"/>
        <if test="dataType != null and dataType == 'share'">
            order by sm_effective_share_cnt desc
        </if>
        <if test="dataType != null and dataType == 'visit'">
            order by sm_effective_visit_user_cnt desc
        </if>
        <if test="page != -1">
            limit #{size} offset #{page}
        </if>
    </select>

    <select id="areaRankCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        from report.ads_insurance_area_online_promotion_dfp p
        <include refid="Base_areaRankList"/>
    </select>

    <select id="districtCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        from report.ads_insurance_district_online_promotion_dfp p
        <include refid="Base_districtRankList"/>
    </select>

    <select id="bchRankCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        from report.ads_insurance_bch_online_promotion_dfp p
        <include refid="Base_bchRankList"/>
    </select>

    <sql id="Base_areaRankList">
        WHERE 1 = 1
        AND p.pt = #{pt}
        <if test="areaCode != null and areaCode != ''">
            AND p.area_code = #{areaCode}
        </if>

    </sql>

    <sql id="Base_districtRankList">
        WHERE 1 = 1
        AND p.pt = #{pt}
    </sql>

    <sql id="Base_bchRankList">
        WHERE 1 = 1
        AND p.pt = #{pt}
        <if test="districtCodeList!= null and districtCodeList.size() > 0">
            AND p.district_code in
            <foreach collection="districtCodeList" item="districtCode" open="(" close=")" separator=",">
                #{districtCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="areaCode != null and areaCode != ''">
            AND p.area_code = #{areaCode}
        </if>
        <if test="bchCode != null and bchCode != ''">
            AND p.bch_code = #{bchCode}
        </if>
    </sql>

    <select id="empNewRankList" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
        p.sm_effective_visit_user_cnt as smVisitCnt,
        p.sm_effective_share_cnt as smShareCnt,
        p.sm_transform_customer_cnt as smTransformCustomerCnt,
        p.sm_transform_policy_cnt as smTransformPolicyCnt,
        p.emp_name as name,
        p.emp_id as empId
        from report.ads_insurance_emp_online_promotion_dfp p
        <include refid="Base_empNewRankList"/>
        <if test="orderType != null">
            <if test="orderType.colName != null and orderType.colName == 'smShareCnt'">
                order by sm_effective_share_cnt
            </if>
            <if test="orderType.colName != null and orderType.colName == 'smVisitCnt'">
                order by sm_effective_visit_user_cnt
            </if>
            <if test="orderType.clause != null and orderType.clause == 'desc'">desc</if> <if test="orderType.clause != null and orderType.clause == 'asc'">asc</if> <if test="orderType.clause == null and orderType.clause == ''">desc</if>
        </if>
        <if test="orderType == null">
            order by sm_effective_share_cnt desc
        </if>
        limit #{size} offset #{page}
    </select>

    <select id="empNewRankCount" resultType="java.lang.Integer">
        SELECT
        count(*)
        from report.ads_insurance_emp_online_promotion_dfp p
        <include refid="Base_empNewRankList"/>
    </select>

    <sql id="Base_empNewRankList">
        WHERE 1 = 1
        AND p.pt = #{pt}
        <if test="districtCodeList!= null and districtCodeList.size() > 0">
            AND p.district_code in
            <foreach collection="districtCodeList" item="districtCode" open="(" close=")" separator=",">
                #{districtCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="areaCode != null and areaCode != ''">
            AND p.area_code = #{areaCode}
        </if>
        AND p.bch_code = #{bchCode}
    </sql>

    <select id="bchRankListForLevel" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
        p.sm_effective_visit_user_cnt as smVisitCnt,
        p.sm_effective_share_cnt as smShareCnt,
        p.sm_transform_customer_cnt as smTransformCustomerCnt,
        p.sm_transform_policy_cnt as smTransformPolicyCnt,
        p.bch_name as name,
        p.cd_effective_visit_user_cnt as cdVisitCnt,
        p.cd_effective_share_cnt as cdShareCnt
        from report.ads_insurance_bch_online_promotion_dfp p
        <include refid="Base_bchRankList"/>
        <if test="orderType != null">
            <if test="orderType.colName != null and orderType.colName == 'smShareCnt'">
                order by sm_effective_share_cnt
            </if>
            <if test="orderType.colName != null and orderType.colName == 'smVisitCnt'">
                order by sm_effective_visit_user_cnt
            </if>
            <if test="orderType.colName != null and orderType.colName == 'cdShareCnt'">
                order by cd_effective_share_cnt
            </if>
            <if test="orderType.colName != null and orderType.colName == 'cdVisitCnt'">
                order by cd_effective_visit_user_cnt
            </if>
            <if test="orderType.clause != null and orderType.clause == 'desc'">desc</if> <if test="orderType.clause != null and orderType.clause == 'asc'">asc</if> <if test="orderType.clause == null and orderType.clause == ''">desc</if>
        </if>
        <if test="orderType == null">
            order by sm_effective_share_cnt desc
        </if>
        <if test="page != -1">
            limit #{size} offset #{page}
        </if>
    </select>

    <select id="mineEmpRankCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        from report.ads_insurance_emp_online_promotion_dfp p
        where 1=1
        AND p.pt = #{pt}
        <if test="dataType != null and dataType == 'share'">
            and p.sm_effective_share_cnt >= #{smShareCnt}
        </if>
        <if test="dataType != null and dataType == 'visit'">
            and p.sm_effective_visit_user_cnt >= #{smVisitCnt}
        </if>
        <if test="rankType != null and rankType == 'area'">
            and p.area_code = #{areaCode}
        </if>
    </select>



    <select id="manageNationSummary" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
        sum(p.sm_effective_visit_user_cnt) as smVisitCnt,
        sum(p.sm_effective_share_cnt) as smShareCnt,
        sum(p.cd_effective_visit_user_cnt) as cdVisitCnt,
        sum(p.cd_effective_share_cnt) as cdShareCnt
        from report.ads_insurance_area_online_promotion_dfp p
        WHERE 1 = 1
        AND p.pt = #{pt}
    </select>

    <select id="manageAreaSummary" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
            p.sm_effective_visit_user_cnt as smVisitCnt,
            p.sm_effective_share_cnt as smShareCnt,
            p.area_name as areaName,
            p.cd_effective_visit_user_cnt as cdVisitCnt,
            p.cd_effective_share_cnt as cdShareCnt
        from report.ads_insurance_area_online_promotion_dfp p
        WHERE 1 = 1
            AND p.pt = #{pt}
            AND p.area_code = #{areaCode}
    </select>

    <select id="manageDistrictSummary" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
        sum(p.sm_effective_visit_user_cnt) as smVisitCnt,
        sum(p.sm_effective_share_cnt) as smShareCnt,
        sum(p.cd_effective_visit_user_cnt) as cdVisitCnt,
        sum(p.cd_effective_share_cnt) as cdShareCnt
        from report.ads_insurance_district_online_promotion_dfp p
        WHERE 1 = 1
        AND p.pt = #{pt}
        AND p.area_code = #{areaCode}
        <if test="districtList!= null and districtList.size() > 0">
            AND p.district_code in
            <foreach collection="districtList" item="districtCode" open="(" close=")" separator=",">
                #{districtCode,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="manageBchSummary" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
        p.sm_effective_visit_user_cnt as smVisitCnt,
        p.sm_effective_share_cnt as smShareCnt,
        p.bch_name as bchName
        from report.ads_insurance_bch_online_promotion_dfp p
        WHERE 1 = 1
        AND p.pt = #{pt}
        AND p.area_code = #{areaCode}
        <if test="districtList!= null and districtList.size() > 0">
            AND p.district_code in
            <foreach collection="districtList" item="districtCode" open="(" close=")" separator=",">
                #{districtCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        AND p.bch_code = #{bchCode}
    </select>

    <select id="mineManageAreaRankCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        from report.ads_insurance_area_online_promotion_dfp p
        where 1=1
        AND p.pt = #{pt}
        <if test="dataType != null and dataType == 'share'">
            and p.sm_effective_share_cnt >= #{smShareCnt}
        </if>
        <if test="dataType != null and dataType == 'visit'">
            and p.sm_effective_visit_user_cnt >= #{smVisitCnt}
        </if>
        <if test="rankType != null and rankType == 'area'">
            and p.area_code = #{areaCode}
        </if>
    </select>

    <select id="mineManageBckRankCount" resultType="java.lang.Integer">
        SELECT
        count(*)
        from report.ads_insurance_bch_online_promotion_dfp p
        where 1=1
        AND p.pt = #{pt}
        <if test="dataType != null and dataType == 'share'">
            and p.sm_effective_share_cnt >= #{smShareCnt}
        </if>
        <if test="dataType != null and dataType == 'visit'">
            and p.sm_effective_visit_user_cnt >= #{smVisitCnt}
        </if>
        <if test="rankType != null and rankType == 'area'">
            and p.area_code = #{areaCode}
        </if>
    </select>

    <select id="empTopOne" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
            p.sm_effective_visit_user_cnt as smVisitCnt,
            p.sm_effective_share_cnt as smShareCnt,
            p.sd_effective_visit_user_cnt as sdVisitCnt,
            p.sd_effective_share_cnt as sdShareCnt,
            p.sm_transform_customer_cnt as smTransformCustomerCnt,
            p.sm_transform_policy_cnt as smTransformPolicyCnt,
            p.area_code as areaCode,
            p.area_name as areaName,
            p.district_code as districtCode,
            p.bch_code as bchCode,
            p.bch_name as bchName,
            p.emp_name as empName
        from report.ads_insurance_emp_online_promotion_dfp p
        WHERE 1 = 1
            AND p.pt = #{pt}
            <if test="dataType != null and dataType == 'share'">
                order by p.sm_effective_share_cnt_rank asc , p.emp_name COLLATE "zh_CN" asc nulls last
            </if>
            <if test="dataType != null and dataType == 'visit'">
                order by p.sm_effective_visit_user_cnt_rank asc , p.emp_name COLLATE "zh_CN" asc nulls last
            </if>
        limit 1
    </select>

    <select id="empSummaryList" resultType="com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO">
        SELECT
        p.sm_effective_visit_user_cnt as smVisitCnt,
        p.sm_effective_share_cnt as smShareCnt,
        p.sd_effective_visit_user_cnt as sdVisitCnt,
        p.sd_effective_share_cnt as sdShareCnt,
        p.sm_transform_customer_cnt as smTransformCustomerCnt,
        p.sm_transform_policy_cnt as smTransformPolicyCnt,
        p.area_code as areaCode,
        p.area_name as areaName,
        p.district_code as districtCode,
        p.bch_code as bchCode,
        p.bch_name as bchName,
        p.sm_effective_visit_user_cnt_rank as smEffectiveVisitUserCntRank,
        p.sm_effective_share_cnt_rank as smEffectiveShareCntRank,
        p.sm_effective_visit_user_cnt_area_rank as smEffectiveVisitUserCntAreaRank,
        p.sm_effective_share_cnt_area_rank as smEffectiveShareCntAreaRank,
        p.emp_id as empId
        from report.ads_insurance_emp_online_promotion_dfp p
        WHERE 1 = 1
        AND p.pt = #{pt}
        <if test="empIdList!=null and empIdList.size>0">
            AND p.emp_id in
            <foreach collection="empIdList" item="empId" open="(" close=")" separator=",">
                #{empId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="bchCode != null and bchCode != ''">
            and p.bch_code = #{bchCode}
        </if>
    </select>
</mapper>
