<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceBchMarketingProgressDfpMapper">
    <resultMap id="AdsInsuranceBchMarketingProgressDfpMap"
               type="com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceBchMarketingProgressDfp">

    </resultMap>
    <select id="queryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        rank,
        string_agg(sm_assess_convert_insurance_amt_achieve_rate_rank_name,',') as
        sm_assess_convert_insurance_amt_achieve_rate_rank_name,
        string_agg(sy_offline_loan_insurance_rate_rank_name,',') as sy_offline_loan_insurance_rate_rank_name,
        string_agg(sy_insurance_retention_rate_rank_name,',') as sy_insurance_retention_rate_rank_name
        from
        (
        select
        aibmpd.bch_name as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
        null as sy_offline_loan_insurance_rate_rank_name,
        null as sy_insurance_retention_rate_rank_name,
        row_number() over(
        partition by aibmpd.pt
        order by
        aibmpd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aibmpd.bch_code asc
        ) as rank
        from
        report.ads_insurance_bch_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        <if test = "blackBchList != null and !assessConvertInsuranceAmtTag">
            and aibmpd.bch_code not in
            <foreach collection="blackBchList" open="(" close=")" separator="," item="blackBch">
                #{blackBch}
            </foreach>
        </if>
        <if test="orgLevel == 'AREA'">
            and aibmpd.area_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                #{orgCode}
            </foreach>
        </if>
        <if test="orgLevel == 'DISTRICT'">
            and aibmpd.district_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                #{orgCode}
            </foreach>
        </if>
        and aibmpd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">&gt;=</if>
        <if test="!assessConvertInsuranceAmtTag">&lt;</if>
        1
        <if test="!offlineLoanInsuranceRateTag">
            union
            select
            null as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
            aibmpd.bch_name as sy_offline_loan_insurance_rate_rank_name,
            null as sy_insurance_retention_rate_rank_name,
            row_number() over(
            partition by aibmpd.pt
            order by
            aibmpd.sy_offline_loan_insurance_rate asc nulls first,
            aibmpd.bch_code asc
            ) as rank
            from
            report.ads_insurance_bch_marketing_progress_dfp aibmpd
            where
            aibmpd.pt = #{pt}
            <if test = "blackBchList != null">
                and aibmpd.bch_code not in
                <foreach collection="blackBchList" open="(" close=")" separator="," item="blackBch">
                    #{blackBch}
                </foreach>
            </if>
            <if test="orgLevel == 'AREA'">
                and aibmpd.area_code in
                <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                    #{orgCode}
                </foreach>
            </if>
            <if test="orgLevel == 'DISTRICT'">
                and aibmpd.district_code in
                <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                    #{orgCode}
                </foreach>
            </if>
            and aibmpd.sm_assess_convert_insurance_amt_achieve_rate &lt; 1
            and aibmpd.sy_offline_loan_insurance_rate &lt; aibmpd.sm_offline_loan_insurance_rate_target
        </if>
        <if test="!insuranceRetentionRateTag">
            union
            select
            null as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
            null as sy_offline_loan_insurance_rate_rank_name,
            aibmpd.bch_name as sy_insurance_retention_rate_rank_name,
            row_number() over(
            partition by aibmpd.pt
            order by
            aibmpd.sy_insurance_retention_rate asc nulls first,
            aibmpd.bch_code asc
            ) as rank
            from
            report.ads_insurance_bch_marketing_progress_dfp aibmpd
            where
            aibmpd.pt = #{pt}
            <if test="orgLevel == 'AREA'">
                and aibmpd.area_code in
                <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                    #{orgCode}
                </foreach>
            </if>
            <if test="orgLevel == 'DISTRICT'">
                and aibmpd.district_code in
                <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                    #{orgCode}
                </foreach>
            </if>
            and aibmpd.sm_assess_convert_insurance_amt_achieve_rate &lt; 1
            and aibmpd.sy_insurance_retention_rate &lt; aibmpd.sm_insurance_retention_rate_target
        </if>
        ) as tmp0
        group by
        rank
        order by
        rank asc
        limit 3;
    </select>
    <select id="queryDiagnosisData"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.DiagnosisAndConclusionService$DiagnosisAndConclusionVo">
        select
        aibmpd.sy_assess_convert_insurance_amt_achieve_rate,
        aibmpd.sm_assess_convert_insurance_amt_achieve_rate,
        case
        when aibmpd.lm_assess_convert_insurance_amt = 0 then null
        else (aibmpd.sm_assess_convert_insurance_amt - aibmpd.lm_assess_convert_insurance_amt)/
        aibmpd.lm_assess_convert_insurance_amt
        end as sm_assess_convert_insurance_amt_month_on_month_rate,
        aibmpd.sy_offline_loan_insurance_rate ,
        aibmpd.sm_offline_loan_insurance_rate_target ,
        aibmpd.sy_insurance_retention_rate ,
        aibmpd.sm_insurance_retention_rate_target,
        temp0.sy_assess_convert_insurance_amt_achieve_rate_area_rank,
        temp0.sy_assess_convert_insurance_amt_achieve_rate_country_rank
        from
        report.ads_insurance_bch_marketing_progress_dfp aibmpd
        left join (
        select
        bch_code,
        row_number() over(
        partition by aibmpd.pt,
        aibmpd.area_code
        order by
        aibmpd.sy_assess_convert_insurance_amt_achieve_rate desc nulls last
        ) as sy_assess_convert_insurance_amt_achieve_rate_area_rank,
        row_number() over(
        partition by aibmpd.pt
        order by
        aibmpd.sy_assess_convert_insurance_amt_achieve_rate desc nulls last
        ) as sy_assess_convert_insurance_amt_achieve_rate_country_rank
        from
        report.ads_insurance_bch_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}) as temp0 on
        aibmpd.bch_code = temp0.bch_code
        where
        aibmpd.pt = #{pt}
        and aibmpd.bch_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>;
    </select>
    <select id="queryAssessConvertInsuranceAmtSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        t0.rank,
        string_agg(t0.sy_assess_convert_insurance_amt_achieve_rate_rank_name, ',') as
        sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        string_agg(t0.sm_assess_convert_insurance_amt_achieve_rate_rank_name, ',') as
        sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        (
        select
        row_number() over(
        partition by aibmpd.pt
        order by
        aibmpd.sy_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aibmpd.bch_code asc
        ) as rank,
        aibmpd.bch_name as sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        null as sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        report.ads_insurance_bch_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        <if test = "blackBchList !=null and !assessConvertInsuranceAmtTag">
            and aibmpd.bch_code not in
            <foreach collection="blackBchList" open="(" close=")" separator="," item="blackBch">
                #{blackBch}
            </foreach>
        </if>
        <if test="orgLevel== 'AREA'">
            and aibmpd.area_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">#{orgCode}</foreach>
        </if>
        <if test="orgLevel== 'DISTRICT'">
            and aibmpd.district_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">#{orgCode}</foreach>
        </if>
        and aibmpd.sy_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">&gt;=</if>
        <if test="!assessConvertInsuranceAmtTag">&lt;</if>
        1
        union
        select
        row_number() over(
        partition by aibmpd.pt
        order by
        aibmpd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aibmpd.bch_code asc
        ) as rank,
        null as sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        aibmpd.bch_name as sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        report.ads_insurance_bch_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        <if test = "blackBchList !=null and !assessConvertInsuranceAmtTag">
            and aibmpd.bch_code not in
            <foreach collection="blackBchList" open="(" close=")" separator="," item="blackBch">
                #{blackBch}
            </foreach>
        </if>
        <if test="orgLevel== 'AREA'">
            and aibmpd.area_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">#{orgCode}</foreach>
        </if>
        <if test="orgLevel== 'DISTRICT'">
            and aibmpd.district_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">#{orgCode}</foreach>
        </if>
        and aibmpd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">&gt;=</if>
        <if test="!assessConvertInsuranceAmtTag">&lt;</if>
        1
        ) as t0
        group by
        t0.rank
        order by
        t0.rank asc
        limit 3;
    </select>
    <select id="queryAssessConvertInsuranceAmtSummary" resultType="java.lang.Boolean">
        select
        case
        when aibmpd.sm_assess_convert_insurance_amt >= aibmpd.sm_assess_convert_insurance_amt_target then true
        else false
        end as check_result
        from
        report.ads_insurance_bch_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        and aibmpd.bch_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>;
    </select>
    <select id="queryInsuranceAmountRateSummary"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.InsuranceAmountRateSummaryService$InsuranceAmountRateSummaryVo">
        select
        aibmpd.sm_offline_loan_insurance_rate_target ,
        aibmpd.sy_offline_loan_insurance_rate
        from
        report.ads_insurance_bch_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        and aibmpd.bch_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>;
    </select>
    <select id="queryInsuranceAmountRateSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        aibmpd.bch_name as sy_offline_loan_insurance_rate_rank_name,
        aibmpd.sy_offline_loan_insurance_rate,
        row_number() over(
        partition by aibmpd.pt
        order by
        aibmpd.sy_offline_loan_insurance_rate
        <if test="insuranceAmountRateTag">desc nulls last</if>
        <if test="!insuranceAmountRateTag">asc nulls first</if>,
        aibmpd.bch_code asc
        ) as rank
        from
        report.ads_insurance_bch_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        <if test = "blackBchList !=null and !insuranceAmountRateTag">
            and aibmpd.bch_code not in
            <foreach collection="blackBchList" open="(" close=")" separator="," item="blackBch">
                #{blackBch}
            </foreach>
        </if>
        and aibmpd.sy_offline_loan_insurance_rate
        <if test="insuranceAmountRateTag">&gt;=</if>
        <if test="!insuranceAmountRateTag">&lt;</if>
        aibmpd.sm_offline_loan_insurance_rate_target
        <if test="orgLevel== 'AREA'">
            and aibmpd.area_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">#{orgCode}</foreach>
        </if>
        <if test="orgLevel== 'DISTRICT'">
            and aibmpd.district_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">#{orgCode}</foreach>
        </if>
        order by
        rank asc
        limit 3;
    </select>
    <select id="queryRetentionRateSummary"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.RetentionRateSummaryService$RetentionRateSummaryVo">
        select
        aibmpd.sm_insurance_retention_rate_target ,
        aibmpd.sy_insurance_retention_rate
        from
        report.ads_insurance_bch_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        and aibmpd.bch_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        ;
    </select>
    <select id="queryRetentionRateSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        aibmpd.bch_name as sy_insurance_retention_rate_rank_name,
        aibmpd.sy_insurance_retention_rate,
        row_number() over(
        partition by aibmpd.pt
        order by
        aibmpd.sy_insurance_retention_rate
        <if test="retentionRateTag">desc nulls last</if>
        <if test="!retentionRateTag">asc nulls first</if>,
        aibmpd.bch_code asc
        ) as rank
        from
        report.ads_insurance_bch_marketing_progress_dfp aibmpd
        where
        aibmpd.pt = #{pt}
        <if test = "blackBchList !=null and !retentionRateTag">
            and aibmpd.bch_code not in
            <foreach collection="blackBchList" open="(" close=")" separator="," item="blackBch">
                #{blackBch}
            </foreach>
        </if>
        and aibmpd.sy_insurance_retention_rate
        <if test="retentionRateTag">&gt;=</if>
        <if test="!retentionRateTag">&lt;</if>
        aibmpd.sm_insurance_retention_rate_target
        <if test="orgLevel== 'AREA'">
            and aibmpd.area_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                #{orgCode}
            </foreach>
        </if>
        <if test="orgLevel== 'DISTRICT'">
            and aibmpd.district_code in
            <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
                #{orgCode}
            </foreach>
        </if>
        order by
        rank asc
        limit 3;
    </select>

</mapper>