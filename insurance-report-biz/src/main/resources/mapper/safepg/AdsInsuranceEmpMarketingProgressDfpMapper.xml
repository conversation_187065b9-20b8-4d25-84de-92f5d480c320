<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper">
    <resultMap type="com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceEmpMarketingProgressDfp"
               id="AdsInsuranceEmpMarketingProgressDfpMap">
        <!--        <result property="empName" column="emp_name" jdbcType="String"/>-->
        <!--        <result property="empCode" column="emp_code" jdbcType="String"/>-->
        <!--        <result property="bchName" column="bch_name" jdbcType="String"/>-->
        <!--        <result property="bchCode" column="bch_code" jdbcType="String"/>-->
        <!--        <result property="districtName" column="district_name" jdbcType="String"/>-->
        <!--        <result property="districtCode" column="district_code" jdbcType="String"/>-->
        <!--        <result property="areaName" column="area_name" jdbcType="String"/>-->
        <!--        <result property="areaCode" column="area_code" jdbcType="String"/>-->
        <!--        <result property="smAssessConvertInsuranceAmt" column="sm_assess_convert_insurance_amt" jdbcType="Double"/>-->
        <!--        <result property="syAssessConvertInsuranceAmt" column="sy_assess_convert_insurance_amt" jdbcType="Double"/>-->
        <!--        <result property="smAssessConvertInsuranceAmtTarget" column="sm_assess_convert_insurance_amt_target" jdbcType="Double"/>-->
        <!--        <result property="syAssessConvertInsuranceAmtTarget" column="sy_assess_convert_insurance_amt_target" jdbcType="Double"/>-->
        <!--        <result property="smAssessConvertInsuranceAmtAchieveRate" column="sm_assess_convert_insurance_amt_achieve_rate" jdbcType="Double"/>-->
        <!--        <result property="syAssessConvertInsuranceAmtAchieveRate" column="sy_assess_convert_insurance_amt_achieve_rate" jdbcType="Double"/>-->
        <!--        <result property="smOfflineLoanInsuranceRate" column="sm_offline_loan_insurance_rate" jdbcType="Double"/>-->
        <!--        <result property="syOfflineLoanInsuranceRate" column="sy_offline_loan_insurance_rate" jdbcType="Double"/>-->
        <!--        <result property="smOfflineLoanInsuranceRateTarget" column="sm_offline_loan_insurance_rate_target" jdbcType="Double"/>-->
        <!--        <result property="syOfflineLoanInsuranceRateTarget" column="sy_offline_loan_insurance_rate_target" jdbcType="Double"/>-->
        <!--        <result property="smInsuranceRetentionRate" column="sm_insurance_retention_rate" jdbcType="Double"/>-->
        <!--        <result property="syInsuranceRetentionRate" column="sy_insurance_retention_rate" jdbcType="Double"/>-->
        <!--        <result property="smLoanInsuranceRetentionRate" column="sm_loan_insurance_retention_rate" jdbcType="Double"/>-->
        <!--        <result property="syLoanInsuranceRetentionRate" column="sy_loan_insurance_retention_rate" jdbcType="Double"/>-->
        <!--        <result property="smUnloanInsuranceRetentionRate" column="sm_unloan_insurance_retention_rate" jdbcType="Double"/>-->
        <!--        <result property="syUnloanInsuranceRetentionRate" column="sy_unloan_insurance_retention_rate" jdbcType="Double"/>-->
        <!--        <result property="smLoanCustTransRate" column="sm_loan_cust_trans_rate" jdbcType="Double"/>-->
        <!--        <result property="syLoanCustTransRate" column="sy_loan_cust_trans_rate" jdbcType="Double"/>-->
        <!--        <result property="smLoanCustTransRateTarget" column="sm_loan_cust_trans_rate_target" jdbcType="Double"/>-->
        <!--        <result property="syLoanCustTransRateTarget" column="sy_loan_cust_trans_rate_target" jdbcType="Double"/>-->
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        select
            emp_name,emp_code,bch_name,bch_code,district_name,district_code,area_name,area_code,sm_assess_convert_insurance_amt,
            sy_assess_convert_insurance_amt,sm_assess_convert_insurance_amt_target,sy_assess_convert_insurance_amt_target,
            sm_assess_convert_insurance_amt_achieve_rate,sy_assess_convert_insurance_amt_achieve_rate,sm_offline_loan_insurance_rate,
            sy_offline_loan_insurance_rate,sm_offline_loan_insurance_rate_target,sy_offline_loan_insurance_rate_target,
            sm_insurance_retention_rate,sm_insurance_retention_rate_target,sy_insurance_retention_rate,sy_insurance_retention_rate_target,
            sm_loan_insurance_retention_rate,sy_loan_insurance_retention_rate,sm_unloan_insurance_retention_rate,
            sy_unloan_insurance_retention_rate,sm_loan_cust_trans_rate,sy_loan_cust_trans_rate,sm_loan_cust_trans_rate_target,sy_loan_cust_trans_rate_target,pt
        from report.ads_insurance_emp_marketing_progress_dfp
        where emp_code = #{empCode} and pt = #{pt}
    </select>


    <select id="queryRankBch" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        with amt_rank as (
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt,</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt,</if>
        RANK() over (partition by bch_code order by
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt</if>
        desc) rank_in_bch
        from report.ads_insurance_emp_marketing_progress_dfp where pt=#{pt} and bch_code = #{bchCode}
            and (leave_date is null or TO_DATE(leave_date, 'YYYY-MM-DD') &gt; TO_DATE(#{pt}, 'YYYYMMDD'))
        )
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt,</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt,</if>
        rank_in_bch
        from amt_rank where emp_code = #{empCode} and bch_code = #{bchCode}
    </select>

    <select id="queryRankBchPrevious" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        with amt_rank as (
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt,</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt,</if>
        RANK() over (partition by bch_code order by
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt</if>
        desc) rank_in_bch
        from report.ads_insurance_emp_marketing_progress_dfp where pt=#{pt} and bch_code = #{bchCode}
            and (leave_date is null or TO_DATE(leave_date, 'YYYY-MM-DD') &gt; TO_DATE(#{pt}, 'YYYYMMDD'))
        )
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        rank_in_bch,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt</if>
        from amt_rank where bch_code = #{bchCode} and rank_in_bch <![CDATA[ < ]]> #{rankInBch} order by rank_in_bch desc
        limit 1
    </select>

    <select id="queryRankArea" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        with amt_rank as (
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt,</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt,</if>
        RANK() over (partition by area_code order by
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt</if>
        desc) rank_in_area
        from report.ads_insurance_emp_marketing_progress_dfp where pt=#{pt} and area_code = #{areaCode}
            and (leave_date is null or TO_DATE(leave_date, 'YYYY-MM-DD') &gt; TO_DATE(#{pt}, 'YYYYMMDD'))
        )
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        rank_in_area,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt</if>
        from amt_rank where emp_code = #{empCode} and area_code = #{areaCode}
    </select>

    <select id="queryRankAreaPrevious" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        with amt_rank as (
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt,</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt,</if>
        RANK() over (partition by area_code order by
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt</if>
        desc) rank_in_area
        from report.ads_insurance_emp_marketing_progress_dfp where pt=#{pt} and area_code = #{areaCode}
            and (leave_date is null or TO_DATE(leave_date, 'YYYY-MM-DD') &gt; TO_DATE(#{pt}, 'YYYYMMDD'))
        )
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        rank_in_area,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt</if>
        from amt_rank where area_code = #{areaCode} and rank_in_area <![CDATA[ < ]]> #{rankInArea} order by rank_in_area
        desc limit 1
    </select>

    <select id="queryRankCountry" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        with amt_rank as (
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt,</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt,</if>
        RANK() over (order by
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt</if>
        desc) rank_in_country
        from report.ads_insurance_emp_marketing_progress_dfp where pt=#{pt}
            and (leave_date is null or TO_DATE(leave_date, 'YYYY-MM-DD') &gt; TO_DATE(#{pt}, 'YYYYMMDD'))
        )
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt,</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt,</if>
        rank_in_country
        from amt_rank where emp_code = #{empCode}
    </select>

    <select id="queryRankCountryPrevious" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        with amt_rank as (
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt,</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt,</if>
        RANK() over (order by
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt</if>
        desc) rank_in_country
        from report.ads_insurance_emp_marketing_progress_dfp where pt=#{pt}
            and (leave_date is null or TO_DATE(leave_date, 'YYYY-MM-DD') &gt; TO_DATE(#{pt}, 'YYYYMMDD'))
        )
        select
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        <if test="dataType == 'year'">sy_assess_convert_insurance_amt,</if>
        <if test="dataType == 'month'">sm_assess_convert_insurance_amt,</if>
        rank_in_country
        from amt_rank where rank_in_country <![CDATA[ < ]]> #{rankInCountry} order by rank_in_country desc limit 1
    </select>
    <!--查询员工标准保费指标趋势-->
    <select id="queryEmpAmtTrend" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        with pt_array as (
            select UNNEST(ARRAY[<foreach item="item" index="index" collection="ptListCurrentYear" separator="," >#{item}</foreach>]) as pt_value
        )
        select
        pts.pt_value,
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name,
        area_code,
        COALESCE(sm_assess_convert_insurance_amt,0) sm_assess_convert_insurance_amt,
        COALESCE(sm_assess_convert_insurance_amt_target,0) sm_assess_convert_insurance_amt_target
        from pt_array pts left join
        report.ads_insurance_emp_marketing_progress_dfp t1 on pts.pt_value = t1.pt and emp_code = #{empCode}  order by pts.pt_value
    </select>

    <!--查询员工异业客户保费配比趋势-->
    <select id="queryEmpATypeCustInsuranceRateTrend" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        with pt_array as (
        select UNNEST(ARRAY[<foreach item="item" index="index" collection="ptListCurrentYear" separator="," >#{item}</foreach>]) as pt_value
        )
        select
        pts.pt_value,
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name,
        area_code,
        COALESCE(sy_offline_loan_insurance_rate,0) sy_offline_loan_insurance_rate,
        COALESCE(sm_offline_loan_insurance_rate_target,0) sm_offline_loan_insurance_rate_target
        from pt_array pts left join
        report.ads_insurance_emp_marketing_progress_dfp t1 on pts.pt_value = t1.pt and emp_code = #{empCode} order by pts.pt_value
    </select>
    <!--查询员工异业客户转化指标趋势-->
    <select id="queryEmpATypeCustTransRateTrend" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        with pt_array as (
        select UNNEST(ARRAY[<foreach item="item" index="index" collection="ptListCurrentYear" separator="," >#{item}</foreach>]) as pt_value
        )
        select
        pts.pt_value,
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name,
        area_code,
        COALESCE(sy_loan_cust_trans_rate,0) sy_loan_cust_trans_rate,
        COALESCE(sm_loan_cust_trans_rate_target,0) sm_loan_cust_trans_rate_target
        from pt_array pts left join
        report.ads_insurance_emp_marketing_progress_dfp t1 on pts.pt_value = t1.pt and emp_code = #{empCode} order by pts.pt_value
    </select>
    <!--查询员工客户留存率指标趋势-->
    <select id="queryEmpRetentionRate" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        with pt_array as (
        select UNNEST(ARRAY[<foreach item="item" index="index" collection="ptListCurrentYear" separator="," >#{item}</foreach>]) as pt_value
        )
        select
        pts.pt_value,
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name, area_code,
        COALESCE(sy_insurance_retention_rate,0) sy_insurance_retention_rate,
        COALESCE (sm_insurance_retention_rate_target,0) sm_insurance_retention_rate_target
        from pt_array pts left join
        report.ads_insurance_emp_marketing_progress_dfp t1 on pts.pt_value = t1.pt and emp_code = #{empCode} order by pts.pt_value
    </select>


    <!--
        异业客户转化指标-日复盘用
    -->
    <select id="getLoanCustomerConvertMetrics" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        select
        emp_name,emp_code,bch_name,bch_code,district_name,district_code,area_name,area_code,
        COALESCE(cd_offline_loan_amt,0) cd_offline_loan_amt,
        COALESCE(cd_loan_insurance_amt,0) cd_loan_insurance_amt,
        CASE
            WHEN cd_offline_loan_amt = 0 THEN 0
            ELSE COALESCE(cd_loan_insurance_amt/cd_offline_loan_amt,0) * 10000
        END AS cd_offline_loan_insurance_rate,
        COALESCE(sm_offline_loan_amt,0) sm_offline_loan_amt,
        COALESCE(sm_loan_insurance_amt, 0) sm_loan_insurance_amt,
        COALESCE(sm_offline_loan_insurance_rate,0) sm_offline_loan_insurance_rate,
        COALESCE(sm_offline_loan_insurance_rate_target,0) sm_offline_loan_insurance_rate_target,
        pt
        from report.ads_insurance_emp_marketing_progress_dfp
        where emp_code in
        <foreach collection="empCodes" item="empCode" open="(" separator="," close=")">
            #{empCode}
        </foreach>
        and pt = #{pt}
        <if test="bchCode != null">
            and bch_code = #{bchCode}
        </if>

    </select>

    <!--
        信贷与非贷标保情况-日复盘用
    -->
    <select id="getLoanAndUnloanMetrics" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        select
        cd.emp_name,cd.emp_code,cd.bch_name,cd.bch_code,cd.district_name,cd.district_code,cd.area_name,cd.area_code,
        <!--当月信贷相关标准保费指标-->
        COALESCE(sm.sm_loan_assess_convert_insurance_amt, 0) sm_loan_assess_convert_insurance_amt,
        COALESCE(sm.lm_loan_assess_convert_insurance_amt, 0) lm_loan_assess_convert_insurance_amt,
        CASE
            WHEN sm.lm_loan_assess_convert_insurance_amt = 0 THEN 0
            ELSE COALESCE((sm.sm_loan_assess_convert_insurance_amt-sm.lm_loan_assess_convert_insurance_amt)/sm.lm_loan_assess_convert_insurance_amt,0)
        END AS loan_assess_convert_insurance_amt_mom ,
        <!--当日信贷相关标准保费指标-->
        COALESCE(cd.cd_loan_assess_convert_insurance_amt, 0) cd_loan_assess_convert_insurance_amt,
        COALESCE(yd.cd_loan_assess_convert_insurance_amt, 0) yd_loan_assess_convert_insurance_amt,
        CASE
            WHEN yd.cd_loan_assess_convert_insurance_amt = 0 THEN 0
            ELSE COALESCE((cd.cd_loan_assess_convert_insurance_amt-yd.cd_loan_assess_convert_insurance_amt)/yd.cd_loan_assess_convert_insurance_amt,0)
        END AS loan_assess_convert_insurance_amt_dod,
        <!--当月非贷相关标准保费指标-->
        COALESCE(sm.sm_unloan_assess_convert_insurance_amt, 0) sm_unloan_assess_convert_insurance_amt,
        COALESCE(sm.lm_unloan_assess_convert_insurance_amt, 0) lm_unloan_assess_convert_insurance_amt,
        CASE
            WHEN sm.lm_unloan_assess_convert_insurance_amt = 0 THEN 0
            ELSE COALESCE((sm.sm_unloan_assess_convert_insurance_amt-sm.lm_unloan_assess_convert_insurance_amt)/sm.lm_unloan_assess_convert_insurance_amt,0)
        END AS unloan_assess_convert_insurance_amt_mom,
        <!--当日非贷相关标准保费指标-->
        COALESCE(cd.cd_unloan_assess_convert_insurance_amt, 0) cd_unloan_assess_convert_insurance_amt,
        COALESCE(yd.cd_unloan_assess_convert_insurance_amt, 0) yd_unloan_assess_convert_insurance_amt,
        CASE
            WHEN yd.cd_unloan_assess_convert_insurance_amt = 0 THEN 0
            ELSE COALESCE((cd.cd_unloan_assess_convert_insurance_amt-yd.cd_unloan_assess_convert_insurance_amt)/yd.cd_unloan_assess_convert_insurance_amt,0)
        END AS unloan_assess_convert_insurance_amt_dod,
        cd.pt
        from report.ads_insurance_emp_marketing_progress_dfp cd left join report.ads_insurance_emp_marketing_progress_dfp yd
        on cd.emp_code = yd.emp_code and cd.bch_code = yd.bch_code
        and cd.area_code = yd.area_code and yd.pt = #{yesterdayPt}
        left join report.ads_insurance_emp_marketing_progress_dfp sm
        on cd.emp_code = sm.emp_code and cd.bch_code = sm.bch_code
        and cd.area_code = sm.area_code and sm.pt = #{maxPt}
        where cd.emp_code in
        <foreach collection="empCodes" item="empCode" open="(" separator="," close=")">
            #{empCode}
        </foreach>
        and cd.pt = #{pt}
        <if test="bchCode != null">
            and cd.bch_code = #{bchCode}
        </if>

    </select>

    <!--
        客户留存情况-日复盘用
    -->
    <select id="getCustomerRetentionMetrics" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        select
        emp_name,emp_code,bch_name,bch_code,district_name,district_code,area_name,area_code,
        COALESCE(sy_insurance_retention_rate,0) sy_insurance_retention_rate,
        COALESCE(sy_loan_insurance_retention_rate, 0) sy_loan_insurance_retention_rate,
        COALESCE(sy_unloan_insurance_retention_rate, 0) sy_unloan_insurance_retention_rate,
        COALESCE(sm_insurance_retention_rate_target, 0) sm_insurance_retention_rate_target,
        pt
        from report.ads_insurance_emp_marketing_progress_dfp
        where emp_code in
        <foreach collection="empCodes" item="empCode" open="(" separator="," close=")">
            #{empCode}
        </foreach>
        and pt = #{pt}
        <if test="bchCode != null">
            and bch_code = #{bchCode}
        </if>
    </select>

    <select id="getTrendInsuranceRateWithBch" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        with pt_array as (
        select UNNEST(ARRAY[<foreach item="item" index="index" collection="pts" separator="," >#{item}</foreach>]) as pt_value
        )
        select
        pts.pt_value pt,
        emp_name, emp_code, bch_name, bch_code, district_name, district_code, area_name,
        area_code,
        COALESCE(sy_offline_loan_insurance_rate,0) sy_offline_loan_insurance_rate,
        COALESCE(sm_offline_loan_insurance_rate_target,0) sm_offline_loan_insurance_rate_target
        from pt_array pts left join
        report.ads_insurance_emp_marketing_progress_dfp t1 on pts.pt_value = t1.pt
        and emp_code = #{empCode}
        <if test="bchCode != null">
            and bch_code = #{bchCode}
        </if>
        order by pts.pt_value
    </select>

    <!--分页查询指定行数据-->
    <select id="queryAllByLimit" resultMap="AdsInsuranceEmpMarketingProgressDfpMap">
        select
        emp_name,emp_code,bch_name,bch_code,district_name,district_code,area_name,area_code,sm_assess_convert_insurance_amt,sy_assess_convert_insurance_amt,sm_assess_convert_insurance_amt_target,sy_assess_convert_insurance_amt_target,sm_assess_convert_insurance_amt_achieve_rate,sy_assess_convert_insurance_amt_achieve_rate,sm_offline_loan_insurance_rate,sy_offline_loan_insurance_rate,sm_offline_loan_insurance_rate_target,sy_offline_loan_insurance_rate_target,sm_insurance_retention_rate,sy_insurance_retention_rate,sm_loan_insurance_retention_rate,sy_loan_insurance_retention_rate,sm_unloan_insurance_retention_rate,sy_unloan_insurance_retention_rate,sm_loan_cust_trans_rate,sy_loan_cust_trans_rate,sm_loan_cust_trans_rate_target,sy_loan_cust_trans_rate_target,pt
        from ads_insurance_emp_marketing_progress_dfp
        <where>
            <if test="empName != null and empName != ''">
                and emp_name = #{empName}
            </if>
            <if test="empCode != null and empCode != ''">
                and emp_code = #{empCode}
            </if>
            <if test="bchName != null and bchName != ''">
                and bch_name = #{bchName}
            </if>
            <if test="bchCode != null and bchCode != ''">
                and bch_code = #{bchCode}
            </if>
            <if test="districtName != null and districtName != ''">
                and district_name = #{districtName}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and district_code = #{districtCode}
            </if>
            <if test="areaName != null and areaName != ''">
                and area_name = #{areaName}
            </if>
            <if test="areaCode != null and areaCode != ''">
                and area_code = #{areaCode}
            </if>
            <if test="smAssessConvertInsuranceAmt != null and smAssessConvertInsuranceAmt != ''">
                and sm_assess_convert_insurance_amt = #{smAssessConvertInsuranceAmt}
            </if>
            <if test="syAssessConvertInsuranceAmt != null and syAssessConvertInsuranceAmt != ''">
                and sy_assess_convert_insurance_amt = #{syAssessConvertInsuranceAmt}
            </if>
            <if test="smAssessConvertInsuranceAmtTarget != null and smAssessConvertInsuranceAmtTarget != ''">
                and sm_assess_convert_insurance_amt_target = #{smAssessConvertInsuranceAmtTarget}
            </if>
            <if test="syAssessConvertInsuranceAmtTarget != null and syAssessConvertInsuranceAmtTarget != ''">
                and sy_assess_convert_insurance_amt_target = #{syAssessConvertInsuranceAmtTarget}
            </if>
            <if test="smAssessConvertInsuranceAmtAchieveRate != null and smAssessConvertInsuranceAmtAchieveRate != ''">
                and sm_assess_convert_insurance_amt_achieve_rate = #{smAssessConvertInsuranceAmtAchieveRate}
            </if>
            <if test="syAssessConvertInsuranceAmtAchieveRate != null and syAssessConvertInsuranceAmtAchieveRate != ''">
                and sy_assess_convert_insurance_amt_achieve_rate = #{syAssessConvertInsuranceAmtAchieveRate}
            </if>
            <if test="smOfflineLoanInsuranceRate != null and smOfflineLoanInsuranceRate != ''">
                and sm_offline_loan_insurance_rate = #{smOfflineLoanInsuranceRate}
            </if>
            <if test="syOfflineLoanInsuranceRate != null and syOfflineLoanInsuranceRate != ''">
                and sy_offline_loan_insurance_rate = #{syOfflineLoanInsuranceRate}
            </if>
            <if test="smOfflineLoanInsuranceRateTarget != null and smOfflineLoanInsuranceRateTarget != ''">
                and sm_offline_loan_insurance_rate_target = #{smOfflineLoanInsuranceRateTarget}
            </if>
            <if test="syOfflineLoanInsuranceRateTarget != null and syOfflineLoanInsuranceRateTarget != ''">
                and sy_offline_loan_insurance_rate_target = #{syOfflineLoanInsuranceRateTarget}
            </if>
            <if test="smInsuranceRetentionRate != null and smInsuranceRetentionRate != ''">
                and sm_insurance_retention_rate = #{smInsuranceRetentionRate}
            </if>
            <if test="syInsuranceRetentionRate != null and syInsuranceRetentionRate != ''">
                and sy_insurance_retention_rate = #{syInsuranceRetentionRate}
            </if>
            <if test="smLoanInsuranceRetentionRate != null and smLoanInsuranceRetentionRate != ''">
                and sm_loan_insurance_retention_rate = #{smLoanInsuranceRetentionRate}
            </if>
            <if test="syLoanInsuranceRetentionRate != null and syLoanInsuranceRetentionRate != ''">
                and sy_loan_insurance_retention_rate = #{syLoanInsuranceRetentionRate}
            </if>
            <if test="smUnloanInsuranceRetentionRate != null and smUnloanInsuranceRetentionRate != ''">
                and sm_unloan_insurance_retention_rate = #{smUnloanInsuranceRetentionRate}
            </if>
            <if test="syUnloanInsuranceRetentionRate != null and syUnloanInsuranceRetentionRate != ''">
                and sy_unloan_insurance_retention_rate = #{syUnloanInsuranceRetentionRate}
            </if>
            <if test="smLoanCustTransRate != null and smLoanCustTransRate != ''">
                and sm_loan_cust_trans_rate = #{smLoanCustTransRate}
            </if>
            <if test="syLoanCustTransRate != null and syLoanCustTransRate != ''">
                and sy_loan_cust_trans_rate = #{syLoanCustTransRate}
            </if>
            <if test="smLoanCustTransRateTarget != null and smLoanCustTransRateTarget != ''">
                and sm_loan_cust_trans_rate_target = #{smLoanCustTransRateTarget}
            </if>
            <if test="syLoanCustTransRateTarget != null and syLoanCustTransRateTarget != ''">
                and sy_loan_cust_trans_rate_target = #{syLoanCustTransRateTarget}
            </if>
            <if test="pt != null and pt != ''">
                and pt = #{pt}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from ads_insurance_emp_marketing_progress_dfp
        <where>
            <if test="empName != null and empName != ''">
                and emp_name = #{empName}
            </if>
            <if test="empCode != null and empCode != ''">
                and emp_code = #{empCode}
            </if>
            <if test="bchName != null and bchName != ''">
                and bch_name = #{bchName}
            </if>
            <if test="bchCode != null and bchCode != ''">
                and bch_code = #{bchCode}
            </if>
            <if test="districtName != null and districtName != ''">
                and district_name = #{districtName}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and district_code = #{districtCode}
            </if>
            <if test="areaName != null and areaName != ''">
                and area_name = #{areaName}
            </if>
            <if test="areaCode != null and areaCode != ''">
                and area_code = #{areaCode}
            </if>
            <if test="smAssessConvertInsuranceAmt != null and smAssessConvertInsuranceAmt != ''">
                and sm_assess_convert_insurance_amt = #{smAssessConvertInsuranceAmt}
            </if>
            <if test="syAssessConvertInsuranceAmt != null and syAssessConvertInsuranceAmt != ''">
                and sy_assess_convert_insurance_amt = #{syAssessConvertInsuranceAmt}
            </if>
            <if test="smAssessConvertInsuranceAmtTarget != null and smAssessConvertInsuranceAmtTarget != ''">
                and sm_assess_convert_insurance_amt_target = #{smAssessConvertInsuranceAmtTarget}
            </if>
            <if test="syAssessConvertInsuranceAmtTarget != null and syAssessConvertInsuranceAmtTarget != ''">
                and sy_assess_convert_insurance_amt_target = #{syAssessConvertInsuranceAmtTarget}
            </if>
            <if test="smAssessConvertInsuranceAmtAchieveRate != null and smAssessConvertInsuranceAmtAchieveRate != ''">
                and sm_assess_convert_insurance_amt_achieve_rate = #{smAssessConvertInsuranceAmtAchieveRate}
            </if>
            <if test="syAssessConvertInsuranceAmtAchieveRate != null and syAssessConvertInsuranceAmtAchieveRate != ''">
                and sy_assess_convert_insurance_amt_achieve_rate = #{syAssessConvertInsuranceAmtAchieveRate}
            </if>
            <if test="smOfflineLoanInsuranceRate != null and smOfflineLoanInsuranceRate != ''">
                and sm_offline_loan_insurance_rate = #{smOfflineLoanInsuranceRate}
            </if>
            <if test="syOfflineLoanInsuranceRate != null and syOfflineLoanInsuranceRate != ''">
                and sy_offline_loan_insurance_rate = #{syOfflineLoanInsuranceRate}
            </if>
            <if test="smOfflineLoanInsuranceRateTarget != null and smOfflineLoanInsuranceRateTarget != ''">
                and sm_offline_loan_insurance_rate_target = #{smOfflineLoanInsuranceRateTarget}
            </if>
            <if test="syOfflineLoanInsuranceRateTarget != null and syOfflineLoanInsuranceRateTarget != ''">
                and sy_offline_loan_insurance_rate_target = #{syOfflineLoanInsuranceRateTarget}
            </if>
            <if test="smInsuranceRetentionRate != null and smInsuranceRetentionRate != ''">
                and sm_insurance_retention_rate = #{smInsuranceRetentionRate}
            </if>
            <if test="syInsuranceRetentionRate != null and syInsuranceRetentionRate != ''">
                and sy_insurance_retention_rate = #{syInsuranceRetentionRate}
            </if>
            <if test="smLoanInsuranceRetentionRate != null and smLoanInsuranceRetentionRate != ''">
                and sm_loan_insurance_retention_rate = #{smLoanInsuranceRetentionRate}
            </if>
            <if test="syLoanInsuranceRetentionRate != null and syLoanInsuranceRetentionRate != ''">
                and sy_loan_insurance_retention_rate = #{syLoanInsuranceRetentionRate}
            </if>
            <if test="smUnloanInsuranceRetentionRate != null and smUnloanInsuranceRetentionRate != ''">
                and sm_unloan_insurance_retention_rate = #{smUnloanInsuranceRetentionRate}
            </if>
            <if test="syUnloanInsuranceRetentionRate != null and syUnloanInsuranceRetentionRate != ''">
                and sy_unloan_insurance_retention_rate = #{syUnloanInsuranceRetentionRate}
            </if>
            <if test="smLoanCustTransRate != null and smLoanCustTransRate != ''">
                and sm_loan_cust_trans_rate = #{smLoanCustTransRate}
            </if>
            <if test="syLoanCustTransRate != null and syLoanCustTransRate != ''">
                and sy_loan_cust_trans_rate = #{syLoanCustTransRate}
            </if>
            <if test="smLoanCustTransRateTarget != null and smLoanCustTransRateTarget != ''">
                and sm_loan_cust_trans_rate_target = #{smLoanCustTransRateTarget}
            </if>
            <if test="syLoanCustTransRateTarget != null and syLoanCustTransRateTarget != ''">
                and sy_loan_cust_trans_rate_target = #{syLoanCustTransRateTarget}
            </if>
            <if test="pt != null and pt != ''">
                and pt = #{pt}
            </if>
        </where>
    </select>
    <select id="queryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        rank,
        string_agg(sm_assess_convert_insurance_amt_achieve_rate_rank_name,',') as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
        string_agg(sy_offline_loan_insurance_rate_rank_name,',') as sy_offline_loan_insurance_rate_rank_name,
        string_agg(sy_insurance_retention_rate_rank_name,',') as sy_insurance_retention_rate_rank_name
        from
        (
        select
        aiempd.emp_name as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
        null as sy_offline_loan_insurance_rate_rank_name,
        null as sy_insurance_retention_rate_rank_name,
        row_number() over(
        partition by aiempd.pt
        order by
        aiempd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aiempd.sm_assess_convert_insurance_amt
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aiempd.emp_code asc
        ) as rank
        from
        report.ads_insurance_emp_marketing_progress_dfp aiempd
        where
        aiempd.pt = #{pt}
        <if test="!assessConvertInsuranceAmtTag"> <!-- 展示末尾的客户经理时排除外部员工-->
            and aiempd.emp_type in ('内部员工','普通员工', '外派员工', '返聘员工' )
        </if>
        and aiempd.bch_code in
        <foreach collection="orgCodes" open="(" separator="," close=")" item="orgCode">
            #{orgCode}
        </foreach>
        and (aiempd.leave_date = '' or to_date(COALESCE(aiempd.leave_date,'2099-12-31'),'YYYY-MM-DD') &gt; to_date(#{pt},'YYYYMMDD'))
        <if test="!assessConvertInsuranceAmtTag">
            and aiempd.job_name in ('客户经理','客户经理B类','客户经理N类')
        </if>
        <if test="!offlineLoanInsuranceRateTag">
            union
            select
            null as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
            aiempd.emp_name as sy_offline_loan_insurance_rate_rank_name,
            null as sy_insurance_retention_rate_rank_name,
            row_number() over(
            partition by aiempd.pt
            order by
            aiempd.sy_offline_loan_insurance_rate asc nulls first,
            aiempd.emp_code asc
            ) as rank
            from
            report.ads_insurance_emp_marketing_progress_dfp aiempd
            where
            aiempd.pt = #{pt}
            and aiempd.emp_type in ('内部员工','普通员工', '外派员工', '返聘员工' )
            and aiempd.bch_code in
            <foreach collection="orgCodes" open="(" separator="," close=")" item="orgCode">
                #{orgCode}
            </foreach>
            and aiempd.sy_offline_loan_insurance_rate &lt; aiempd.sm_offline_loan_insurance_rate_target
            and (aiempd.leave_date = '' or to_date(COALESCE(aiempd.leave_date,'2099-12-31'),'YYYY-MM-DD') &gt; to_date(#{pt},'YYYYMMDD'))
            and aiempd.job_name in ('客户经理','客户经理B类','客户经理N类')
        </if>
        <if test="!insuranceRetentionRateTag">
            union
            select
            null as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
            null as sy_offline_loan_insurance_rate_rank_name,
            aiempd.emp_name as sy_insurance_retention_rate_rank_name,
            row_number() over(
            partition by aiempd.pt
            order by
            aiempd.sy_insurance_retention_rate asc nulls first,
            aiempd.emp_code asc
            ) as rank
            from
            report.ads_insurance_emp_marketing_progress_dfp aiempd
            where
            aiempd.pt = #{pt}
            and aiempd.emp_type in ('内部员工','普通员工', '外派员工', '返聘员工' )
            and aiempd.bch_code in
            <foreach collection="orgCodes" open="(" separator="," close=")" item="orgCode">
                #{orgCode}
            </foreach>
            and aiempd.sy_insurance_retention_rate &lt;  aiempd.sm_insurance_retention_rate_target
            and (aiempd.leave_date = '' or to_date(COALESCE(aiempd.leave_date,'2099-12-31'),'YYYY-MM-DD') &gt; to_date(#{pt},'YYYYMMDD'))
            and aiempd.job_name in ('客户经理','客户经理B类','客户经理N类')
        </if>
        ) as tmp0
        group by
        rank
        order by
        rank asc
        limit 3;
    </select>

    <select id="queryAssessConvertInsuranceAmtSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        t0.rank,
        string_agg(t0.sy_assess_convert_insurance_amt_achieve_rate_rank_name, ',') as sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        string_agg(t0.sm_assess_convert_insurance_amt_achieve_rate_rank_name, ',') as sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        (
        select
        row_number() over(
        partition by aiempd.pt
        order by
        aiempd.sy_assess_convert_insurance_amt
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aiempd.sy_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aiempd.emp_code asc
        ) as rank,
        aiempd.emp_name as sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        null as sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        report.ads_insurance_emp_marketing_progress_dfp aiempd
        where
        aiempd.pt = #{pt}
        <if test="!assessConvertInsuranceAmtTag"> <!-- 展示末尾的客户经理时排除外部员工-->
            and aiempd.emp_type in ('内部员工','普通员工', '外派员工', '返聘员工' )
        </if>
        and aiempd.bch_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        and (aiempd.leave_date = '' or to_date(COALESCE(aiempd.leave_date,'2099-12-31'),'YYYY-MM-DD') &gt; to_date(#{pt},'YYYYMMDD'))
        <if test="!assessConvertInsuranceAmtTag">
            and aiempd.job_name in ('客户经理','客户经理B类','客户经理N类')
        </if>
        union
        select
        row_number() over(
        partition by aiempd.pt
        order by
        aiempd.sm_assess_convert_insurance_amt
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aiempd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aiempd.emp_code asc
        ) as rank,
        null as sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        aiempd.emp_name as sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        report.ads_insurance_emp_marketing_progress_dfp aiempd
        where
        aiempd.pt = #{pt}
        <if test="!assessConvertInsuranceAmtTag"> <!-- 展示末尾的客户经理时排除外部员工-->
            and aiempd.emp_type in ('内部员工','普通员工', '外派员工', '返聘员工' )
        </if>
        and aiempd.bch_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        and (aiempd.leave_date = '' or to_date(COALESCE(aiempd.leave_date,'2099-12-31'),'YYYY-MM-DD') &gt; to_date(#{pt},'YYYYMMDD'))
        <if test="!assessConvertInsuranceAmtTag">
            and aiempd.job_name in ('客户经理','客户经理B类','客户经理N类')
        </if>
        ) as t0
        group by
        t0.rank
        order by
        t0.rank asc
        limit 3;
    </select>
    <select id="queryInsuranceAmountRateSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        aiempd.emp_name as sy_offline_loan_insurance_rate_rank_name,
        aiempd.sy_offline_loan_insurance_rate,
        row_number() over(
        partition by aiempd.pt
        order by
        aiempd.sy_offline_loan_insurance_rate
        <if test="insuranceAmountRateTag">desc nulls last</if>
        <if test="!insuranceAmountRateTag">asc nulls first</if>,
        aiempd.emp_code asc
        ) as rank
        from
        report.ads_insurance_emp_marketing_progress_dfp aiempd
        where
        aiempd.pt = #{pt}
        <if test="!insuranceAmountRateTag"> <!-- 展示末尾的客户经理时排除外部员工-->
            and aiempd.emp_type in ('内部员工','普通员工', '外派员工', '返聘员工' )
        </if>
        and aiempd.sy_offline_loan_insurance_rate
        <if test="insuranceAmountRateTag">&gt;=</if>
        <if test="!insuranceAmountRateTag">&lt;</if>
        aiempd.sm_offline_loan_insurance_rate_target
        and aiempd.bch_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        and (aiempd.leave_date = '' or to_date(COALESCE(aiempd.leave_date,'2099-12-31'),'YYYY-MM-DD') &gt; to_date(#{pt},'YYYYMMDD'))
        <if test="!insuranceAmountRateTag">
            and aiempd.job_name in ('客户经理','客户经理B类','客户经理N类')
        </if>
        order by
        rank asc
        limit 3;
    </select>
    <select id="queryRetentionRateSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        aiempd.emp_name as sy_insurance_retention_rate_rank_name,
        aiempd.sy_insurance_retention_rate,
        row_number() over(
        partition by aiempd.pt
        order by
        aiempd.sy_insurance_retention_rate
        <if test="retentionRateTag">desc nulls last</if>
        <if test="!retentionRateTag">asc nulls first</if>,
        aiempd.emp_code asc
        ) as rank
        from
        report.ads_insurance_emp_marketing_progress_dfp aiempd
        where
        aiempd.pt = #{pt}
        <if test="!retentionRateTag"> <!-- 展示末尾的客户经理时排除外部员工-->
            and aiempd.emp_type in ('内部员工','普通员工', '外派员工', '返聘员工' )
        </if>
        and aiempd.sy_insurance_retention_rate
        <if test="retentionRateTag">&gt;=</if>
        <if test="!retentionRateTag">&lt;</if>
        aiempd.sm_insurance_retention_rate_target
        and aiempd.bch_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        and (aiempd.leave_date = '' or to_date(COALESCE(aiempd.leave_date,'2099-12-31'),'YYYY-MM-DD') &gt; to_date(#{pt},'YYYYMMDD'))
        <if test="!retentionRateTag">
            and aiempd.job_name in ('客户经理','客户经理B类','客户经理N类')
        </if>
        order by
        rank asc
        limit 3;
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="UNDEFINED_ID" useGeneratedKeys="true">
        insert into
        ads_insurance_emp_marketing_progress_dfp(emp_name,emp_code,bch_name,bch_code,district_name,district_code,area_name,area_code,sm_assess_convert_insurance_amt,sy_assess_convert_insurance_amt,sm_assess_convert_insurance_amt_target,sy_assess_convert_insurance_amt_target,sm_assess_convert_insurance_amt_achieve_rate,sy_assess_convert_insurance_amt_achieve_rate,sm_offline_loan_insurance_rate,sy_offline_loan_insurance_rate,sm_offline_loan_insurance_rate_target,sy_offline_loan_insurance_rate_target,sm_insurance_retention_rate,sy_insurance_retention_rate,sm_loan_insurance_retention_rate,sy_loan_insurance_retention_rate,sm_unloan_insurance_retention_rate,sy_unloan_insurance_retention_rate,sm_loan_cust_trans_rate,sy_loan_cust_trans_rate,sm_loan_cust_trans_rate_target,sy_loan_cust_trans_rate_target,pt)
        values
        (#{empName},#{empCode},#{bchName},#{bchCode},#{districtName},#{districtCode},#{areaName},#{areaCode},#{smAssessConvertInsuranceAmt},#{syAssessConvertInsuranceAmt},#{smAssessConvertInsuranceAmtTarget},#{syAssessConvertInsuranceAmtTarget},#{smAssessConvertInsuranceAmtAchieveRate},#{syAssessConvertInsuranceAmtAchieveRate},#{smOfflineLoanInsuranceRate},#{syOfflineLoanInsuranceRate},#{smOfflineLoanInsuranceRateTarget},#{syOfflineLoanInsuranceRateTarget},#{smInsuranceRetentionRate},#{syInsuranceRetentionRate},#{smLoanInsuranceRetentionRate},#{syLoanInsuranceRetentionRate},#{smUnloanInsuranceRetentionRate},#{syUnloanInsuranceRetentionRate},#{smLoanCustTransRate},#{syLoanCustTransRate},#{smLoanCustTransRateTarget},#{syLoanCustTransRateTarget},#{pt})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="UNDEFINED_ID" useGeneratedKeys="true">
        insert into
        ads_insurance_emp_marketing_progress_dfp(emp_name,emp_code,bch_name,bch_code,district_name,district_code,area_name,area_code,sm_assess_convert_insurance_amt,sy_assess_convert_insurance_amt,sm_assess_convert_insurance_amt_target,sy_assess_convert_insurance_amt_target,sm_assess_convert_insurance_amt_achieve_rate,sy_assess_convert_insurance_amt_achieve_rate,sm_offline_loan_insurance_rate,sy_offline_loan_insurance_rate,sm_offline_loan_insurance_rate_target,sy_offline_loan_insurance_rate_target,sm_insurance_retention_rate,sy_insurance_retention_rate,sm_loan_insurance_retention_rate,sy_loan_insurance_retention_rate,sm_unloan_insurance_retention_rate,sy_unloan_insurance_retention_rate,sm_loan_cust_trans_rate,sy_loan_cust_trans_rate,sm_loan_cust_trans_rate_target,sy_loan_cust_trans_rate_target,pt)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.empName},#{entity.empCode},#{entity.bchName},#{entity.bchCode},#{entity.districtName},#{entity.districtCode},#{entity.areaName},#{entity.areaCode},#{entity.smAssessConvertInsuranceAmt},#{entity.syAssessConvertInsuranceAmt},#{entity.smAssessConvertInsuranceAmtTarget},#{entity.syAssessConvertInsuranceAmtTarget},#{entity.smAssessConvertInsuranceAmtAchieveRate},#{entity.syAssessConvertInsuranceAmtAchieveRate},#{entity.smOfflineLoanInsuranceRate},#{entity.syOfflineLoanInsuranceRate},#{entity.smOfflineLoanInsuranceRateTarget},#{entity.syOfflineLoanInsuranceRateTarget},#{entity.smInsuranceRetentionRate},#{entity.syInsuranceRetentionRate},#{entity.smLoanInsuranceRetentionRate},#{entity.syLoanInsuranceRetentionRate},#{entity.smUnloanInsuranceRetentionRate},#{entity.syUnloanInsuranceRetentionRate},#{entity.smLoanCustTransRate},#{entity.syLoanCustTransRate},#{entity.smLoanCustTransRateTarget},#{entity.syLoanCustTransRateTarget},#{entity.pt})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="UNDEFINED_ID" useGeneratedKeys="true">
        insert into
        ads_insurance_emp_marketing_progress_dfp(emp_name,emp_code,bch_name,bch_code,district_name,district_code,area_name,area_code,sm_assess_convert_insurance_amt,sy_assess_convert_insurance_amt,sm_assess_convert_insurance_amt_target,sy_assess_convert_insurance_amt_target,sm_assess_convert_insurance_amt_achieve_rate,sy_assess_convert_insurance_amt_achieve_rate,sm_offline_loan_insurance_rate,sy_offline_loan_insurance_rate,sm_offline_loan_insurance_rate_target,sy_offline_loan_insurance_rate_target,sm_insurance_retention_rate,sy_insurance_retention_rate,sm_loan_insurance_retention_rate,sy_loan_insurance_retention_rate,sm_unloan_insurance_retention_rate,sy_unloan_insurance_retention_rate,sm_loan_cust_trans_rate,sy_loan_cust_trans_rate,sm_loan_cust_trans_rate_target,sy_loan_cust_trans_rate_target,pt)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.empName},#{entity.empCode},#{entity.bchName},#{entity.bchCode},#{entity.districtName},#{entity.districtCode},#{entity.areaName},#{entity.areaCode},#{entity.smAssessConvertInsuranceAmt},#{entity.syAssessConvertInsuranceAmt},#{entity.smAssessConvertInsuranceAmtTarget},#{entity.syAssessConvertInsuranceAmtTarget},#{entity.smAssessConvertInsuranceAmtAchieveRate},#{entity.syAssessConvertInsuranceAmtAchieveRate},#{entity.smOfflineLoanInsuranceRate},#{entity.syOfflineLoanInsuranceRate},#{entity.smOfflineLoanInsuranceRateTarget},#{entity.syOfflineLoanInsuranceRateTarget},#{entity.smInsuranceRetentionRate},#{entity.syInsuranceRetentionRate},#{entity.smLoanInsuranceRetentionRate},#{entity.syLoanInsuranceRetentionRate},#{entity.smUnloanInsuranceRetentionRate},#{entity.syUnloanInsuranceRetentionRate},#{entity.smLoanCustTransRate},#{entity.syLoanCustTransRate},#{entity.smLoanCustTransRateTarget},#{entity.syLoanCustTransRateTarget},#{entity.pt})
        </foreach>
        on duplicate key update
        emp_name=values(emp_name),
        emp_code=values(emp_code),
        bch_name=values(bch_name),
        bch_code=values(bch_code),
        district_name=values(district_name),
        district_code=values(district_code),
        area_name=values(area_name),
        area_code=values(area_code),
        sm_assess_convert_insurance_amt=values(sm_assess_convert_insurance_amt),
        sy_assess_convert_insurance_amt=values(sy_assess_convert_insurance_amt),
        sm_assess_convert_insurance_amt_target=values(sm_assess_convert_insurance_amt_target),
        sy_assess_convert_insurance_amt_target=values(sy_assess_convert_insurance_amt_target),
        sm_assess_convert_insurance_amt_achieve_rate=values(sm_assess_convert_insurance_amt_achieve_rate),
        sy_assess_convert_insurance_amt_achieve_rate=values(sy_assess_convert_insurance_amt_achieve_rate),
        sm_offline_loan_insurance_rate=values(sm_offline_loan_insurance_rate),
        sy_offline_loan_insurance_rate=values(sy_offline_loan_insurance_rate),
        sm_offline_loan_insurance_rate_target=values(sm_offline_loan_insurance_rate_target),
        sy_offline_loan_insurance_rate_target=values(sy_offline_loan_insurance_rate_target),
        sm_insurance_retention_rate=values(sm_insurance_retention_rate),
        sy_insurance_retention_rate=values(sy_insurance_retention_rate),
        sm_loan_insurance_retention_rate=values(sm_loan_insurance_retention_rate),
        sy_loan_insurance_retention_rate=values(sy_loan_insurance_retention_rate),
        sm_unloan_insurance_retention_rate=values(sm_unloan_insurance_retention_rate),
        sy_unloan_insurance_retention_rate=values(sy_unloan_insurance_retention_rate),
        sm_loan_cust_trans_rate=values(sm_loan_cust_trans_rate),
        sy_loan_cust_trans_rate=values(sy_loan_cust_trans_rate),
        sm_loan_cust_trans_rate_target=values(sm_loan_cust_trans_rate_target),
        sy_loan_cust_trans_rate_target=values(sy_loan_cust_trans_rate_target),
        pt=values(pt)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update ads_insurance_emp_marketing_progress_dfp
        <set>
            <if test="empName != null and empName != ''">
                emp_name = #{empName},
            </if>
            <if test="empCode != null and empCode != ''">
                emp_code = #{empCode},
            </if>
            <if test="bchName != null and bchName != ''">
                bch_name = #{bchName},
            </if>
            <if test="bchCode != null and bchCode != ''">
                bch_code = #{bchCode},
            </if>
            <if test="districtName != null and districtName != ''">
                district_name = #{districtName},
            </if>
            <if test="districtCode != null and districtCode != ''">
                district_code = #{districtCode},
            </if>
            <if test="areaName != null and areaName != ''">
                area_name = #{areaName},
            </if>
            <if test="areaCode != null and areaCode != ''">
                area_code = #{areaCode},
            </if>
            <if test="smAssessConvertInsuranceAmt != null and smAssessConvertInsuranceAmt != ''">
                sm_assess_convert_insurance_amt = #{smAssessConvertInsuranceAmt},
            </if>
            <if test="syAssessConvertInsuranceAmt != null and syAssessConvertInsuranceAmt != ''">
                sy_assess_convert_insurance_amt = #{syAssessConvertInsuranceAmt},
            </if>
            <if test="smAssessConvertInsuranceAmtTarget != null and smAssessConvertInsuranceAmtTarget != ''">
                sm_assess_convert_insurance_amt_target = #{smAssessConvertInsuranceAmtTarget},
            </if>
            <if test="syAssessConvertInsuranceAmtTarget != null and syAssessConvertInsuranceAmtTarget != ''">
                sy_assess_convert_insurance_amt_target = #{syAssessConvertInsuranceAmtTarget},
            </if>
            <if test="smAssessConvertInsuranceAmtAchieveRate != null and smAssessConvertInsuranceAmtAchieveRate != ''">
                sm_assess_convert_insurance_amt_achieve_rate = #{smAssessConvertInsuranceAmtAchieveRate},
            </if>
            <if test="syAssessConvertInsuranceAmtAchieveRate != null and syAssessConvertInsuranceAmtAchieveRate != ''">
                sy_assess_convert_insurance_amt_achieve_rate = #{syAssessConvertInsuranceAmtAchieveRate},
            </if>
            <if test="smOfflineLoanInsuranceRate != null and smOfflineLoanInsuranceRate != ''">
                sm_offline_loan_insurance_rate = #{smOfflineLoanInsuranceRate},
            </if>
            <if test="syOfflineLoanInsuranceRate != null and syOfflineLoanInsuranceRate != ''">
                sy_offline_loan_insurance_rate = #{syOfflineLoanInsuranceRate},
            </if>
            <if test="smOfflineLoanInsuranceRateTarget != null and smOfflineLoanInsuranceRateTarget != ''">
                sm_offline_loan_insurance_rate_target = #{smOfflineLoanInsuranceRateTarget},
            </if>
            <if test="syOfflineLoanInsuranceRateTarget != null and syOfflineLoanInsuranceRateTarget != ''">
                sy_offline_loan_insurance_rate_target = #{syOfflineLoanInsuranceRateTarget},
            </if>
            <if test="smInsuranceRetentionRate != null and smInsuranceRetentionRate != ''">
                sm_insurance_retention_rate = #{smInsuranceRetentionRate},
            </if>
            <if test="syInsuranceRetentionRate != null and syInsuranceRetentionRate != ''">
                sy_insurance_retention_rate = #{syInsuranceRetentionRate},
            </if>
            <if test="smLoanInsuranceRetentionRate != null and smLoanInsuranceRetentionRate != ''">
                sm_loan_insurance_retention_rate = #{smLoanInsuranceRetentionRate},
            </if>
            <if test="syLoanInsuranceRetentionRate != null and syLoanInsuranceRetentionRate != ''">
                sy_loan_insurance_retention_rate = #{syLoanInsuranceRetentionRate},
            </if>
            <if test="smUnloanInsuranceRetentionRate != null and smUnloanInsuranceRetentionRate != ''">
                sm_unloan_insurance_retention_rate = #{smUnloanInsuranceRetentionRate},
            </if>
            <if test="syUnloanInsuranceRetentionRate != null and syUnloanInsuranceRetentionRate != ''">
                sy_unloan_insurance_retention_rate = #{syUnloanInsuranceRetentionRate},
            </if>
            <if test="smLoanCustTransRate != null and smLoanCustTransRate != ''">
                sm_loan_cust_trans_rate = #{smLoanCustTransRate},
            </if>
            <if test="syLoanCustTransRate != null and syLoanCustTransRate != ''">
                sy_loan_cust_trans_rate = #{syLoanCustTransRate},
            </if>
            <if test="smLoanCustTransRateTarget != null and smLoanCustTransRateTarget != ''">
                sm_loan_cust_trans_rate_target = #{smLoanCustTransRateTarget},
            </if>
            <if test="syLoanCustTransRateTarget != null and syLoanCustTransRateTarget != ''">
                sy_loan_cust_trans_rate_target = #{syLoanCustTransRateTarget},
            </if>
            <if test="pt != null and pt != ''">
                pt = #{pt},
            </if>
        </set>
        where UNDEFINED_ID = #{undefinedId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ads_insurance_emp_marketing_progress_dfp where UNDEFINED_ID = #{undefinedId}
    </delete>

    <select id="getCustomerConvertInsuranceMetrics" resultType="com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceEmpMarketingProgressDfp">
        with ads_sw as(
            select sum(cd_assess_convert_insurance_amt) sw_assess_convert_insurance_amt,emp_code,max(pt) pt
            from report.ads_insurance_emp_marketing_progress_dfp
            where to_date(pt, 'YYYYMMDD') between CAST(#{monday} as date)  and to_date(#{pt}, 'YYYYMMDD')
            group by emp_code)
        select
            t.emp_code,emp_name,bch_code,bch_name,
            cd_assess_convert_insurance_amt,sw_assess_convert_insurance_amt,sm_assess_convert_insurance_amt,sy_assess_convert_insurance_amt
        from report.ads_insurance_emp_marketing_progress_dfp t
        left join ads_sw on t.emp_code = ads_sw.emp_code
        where t.emp_code in
        <foreach collection="empCodes" item="empCode" open="(" separator="," close=")">
            #{empCode}
        </foreach>
        and t.pt = #{pt}
        and t.bch_code = #{bchCode}
    </select>
</mapper>