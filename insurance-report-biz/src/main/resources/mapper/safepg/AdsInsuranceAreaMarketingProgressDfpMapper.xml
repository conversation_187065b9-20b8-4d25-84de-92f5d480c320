<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceAreaMarketingProgressDfpMapper">
    <resultMap id="AdsInsuranceAreaMarketingProgressDfpResult"
               type="com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceAreaMarketingProgressDfp">

    </resultMap>
    <select id="queryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        rank,
        string_agg(sm_assess_convert_insurance_amt_achieve_rate_rank_name,',') as
        sm_assess_convert_insurance_amt_achieve_rate_rank_name,
        string_agg(sy_offline_loan_insurance_rate_rank_name,',') as sy_offline_loan_insurance_rate_rank_name,
        string_agg(sy_insurance_retention_rate_rank_name,',') as sy_insurance_retention_rate_rank_name
        from
        (
        select
        aiampd.area_name as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
        null as sy_offline_loan_insurance_rate_rank_name,
        null as sy_insurance_retention_rate_rank_name,
        row_number() over(
        partition by aiampd.pt
        order by
        aiampd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aiampd.area_code asc
        ) as rank
        from
        report.ads_insurance_area_marketing_progress_dfp aiampd
        where
        aiampd.pt = #{pt}
        and aiampd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">&gt;=</if>
        <if test="!assessConvertInsuranceAmtTag">&lt;</if>
        1
        <if test="!offlineLoanInsuranceRateTag">
            union
            select
            null as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
            aiampd.area_name as sy_offline_loan_insurance_rate_rank_name,
            null as sy_insurance_retention_rate_rank_name,
            row_number() over(
            partition by aiampd.pt
            order by
            aiampd.sy_offline_loan_insurance_rate asc nulls first,
            aiampd.area_code asc
            ) as rank
            from
            report.ads_insurance_area_marketing_progress_dfp aiampd
            where
            aiampd.pt = #{pt}
            and aiampd.sm_assess_convert_insurance_amt_achieve_rate &lt; 1
            and aiampd.sy_offline_loan_insurance_rate &lt; aiampd.sm_offline_loan_insurance_rate_target
        </if>
        <if test="!insuranceRetentionRateTag">
            union
            select
            null as sm_assess_convert_insurance_amt_achieve_rate_rank_name,
            null as sy_offline_loan_insurance_rate_rank_name,
            aiampd.area_name as sy_insurance_retention_rate_rank_name,
            row_number() over(
            partition by aiampd.pt
            order by
            aiampd.sy_insurance_retention_rate asc nulls first,
            aiampd.area_code asc
            ) as rank
            from
            report.ads_insurance_area_marketing_progress_dfp aiampd
            where
            aiampd.pt = #{pt}
            and aiampd.sm_assess_convert_insurance_amt_achieve_rate &lt; 1
            and aiampd.sy_insurance_retention_rate &lt; aiampd.sm_insurance_retention_rate_target
        </if>
        ) as tmp0
        group by
        rank
        order by
        rank asc
        limit 3;
    </select>
    <select id="queryDiagnosisData"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.DiagnosisAndConclusionService$DiagnosisAndConclusionVo">
        select
        aiampd.sy_assess_convert_insurance_amt_achieve_rate,
        aiampd.sm_assess_convert_insurance_amt_achieve_rate,
        case
        when aiampd.lm_assess_convert_insurance_amt = 0 then null
        else (aiampd.sm_assess_convert_insurance_amt - aiampd.lm_assess_convert_insurance_amt)/
        aiampd.lm_assess_convert_insurance_amt
        end as sm_assess_convert_insurance_amt_month_on_month_rate,
        aiampd.sy_offline_loan_insurance_rate ,
        aiampd.sm_offline_loan_insurance_rate_target ,
        aiampd.sy_insurance_retention_rate ,
        aiampd.sm_insurance_retention_rate_target,
        tmp0.sy_assess_convert_insurance_amt_achieve_rate_country_rank
        from
        report.ads_insurance_area_marketing_progress_dfp aiampd
        left join (
        select
        area_code,
        row_number() over(
        partition by aiampd.pt
        order by
        aiampd.sy_assess_convert_insurance_amt_achieve_rate desc nulls last,
        aiampd.area_code asc
        ) as sy_assess_convert_insurance_amt_achieve_rate_country_rank
        from
        report.ads_insurance_area_marketing_progress_dfp aiampd
        where
        aiampd.pt = #{pt}
        ) as tmp0 on
        aiampd.area_code = tmp0.area_code
        where
        aiampd.pt = #{pt}
        and aiampd.area_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>;
    </select>
    <select id="queryAssessConvertInsuranceAmtSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        t0.rank,
        string_agg(t0.sy_assess_convert_insurance_amt_achieve_rate_rank_name, ',') as
        sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        string_agg(t0.sm_assess_convert_insurance_amt_achieve_rate_rank_name, ',') as
        sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        (
        select
        row_number() over(
        partition by aiampd.pt
        order by
        aiampd.sy_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aiampd.area_code asc
        ) as rank,
        aiampd.area_name as sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        null as sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        report.ads_insurance_area_marketing_progress_dfp aiampd
        where
        aiampd.pt = #{pt}
        and aiampd.sy_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">&gt;=</if>
        <if test="!assessConvertInsuranceAmtTag">&lt;</if>
        1
        union
        select
        row_number() over(
        partition by aiampd.pt
        order by
        aiampd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">desc nulls last</if>
        <if test="!assessConvertInsuranceAmtTag">asc nulls first</if>
        ,
        aiampd.area_code asc
        ) as rank,
        null as sy_assess_convert_insurance_amt_achieve_rate_rank_name,
        aiampd.area_name as sm_assess_convert_insurance_amt_achieve_rate_rank_name
        from
        report.ads_insurance_area_marketing_progress_dfp aiampd
        where
        aiampd.pt = #{pt}
        and aiampd.sm_assess_convert_insurance_amt_achieve_rate
        <if test="assessConvertInsuranceAmtTag">&gt;=</if>
        <if test="!assessConvertInsuranceAmtTag">&lt;</if>
        1
        ) as t0
        group by
        t0.rank
        order by
        t0.rank asc
        limit 3;
    </select>
    <select id="queryAssessConvertInsuranceAmtSummary" resultType="java.lang.Boolean">
        select
        case
        when aiampd.sm_assess_convert_insurance_amt >= aiampd.sm_assess_convert_insurance_amt_target then true
        else false
        end as check_result
        from
        report.ads_insurance_area_marketing_progress_dfp aiampd
        where
        aiampd.pt = #{pt}
        and aiampd.area_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>;
    </select>
    <select id="queryInsuranceAmountRateSummary"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.InsuranceAmountRateSummaryService$InsuranceAmountRateSummaryVo">
        select
        aiampd.sm_offline_loan_insurance_rate_target ,
        aiampd.sy_offline_loan_insurance_rate
        from
        report.ads_insurance_area_marketing_progress_dfp aiampd
        where
        aiampd.pt = #{pt}
        and aiampd.area_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>;
    </select>
    <select id="queryInsuranceAmountRateSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        aiampd.area_name as sy_offline_loan_insurance_rate_rank_name,
        aiampd.sy_offline_loan_insurance_rate,
        row_number() over(
        partition by aiampd.pt
        order by
        aiampd.sy_offline_loan_insurance_rate
        <if test="insuranceAmountRateTag">desc nulls last</if>
        <if test="!insuranceAmountRateTag">asc nulls first</if>,
        aiampd.area_code asc
        ) as rank
        from
        report.ads_insurance_area_marketing_progress_dfp aiampd
        where
        aiampd.pt = #{pt}
        and aiampd.sy_offline_loan_insurance_rate
        <if test="insuranceAmountRateTag">&gt;=</if>
        <if test="!insuranceAmountRateTag">&lt;</if>
        aiampd.sm_offline_loan_insurance_rate_target
        order by
        rank asc
        limit 3;
    </select>
    <select id="queryRetentionRateSummary"
            resultType="com.cfpamf.ms.insur.report.service.diagnosis.RetentionRateSummaryService$RetentionRateSummaryVo">
        select
        aiampd.sm_insurance_retention_rate_target ,
        aiampd.sy_insurance_retention_rate
        from
        report.ads_insurance_area_marketing_progress_dfp aiampd
        where
        aiampd.pt = #{pt}
        and aiampd.area_code in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>;
    </select>
    <select id="queryRetentionRateSummaryRankInfo"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo">
        select
        aiampd.area_name as sy_insurance_retention_rate_rank_name,
        aiampd.sy_insurance_retention_rate,
        row_number() over(
        partition by aiampd.pt
        order by
        aiampd.sy_insurance_retention_rate
        <if test="retentionRateTag">desc nulls last</if>
        <if test="!retentionRateTag">asc nulls first</if>,
        aiampd.area_code asc
        ) as rank
        from
        report.ads_insurance_area_marketing_progress_dfp aiampd
        where
        aiampd.pt = #{pt}
        and aiampd.sy_insurance_retention_rate
        <if test="retentionRateTag">&gt;=</if>
        <if test="!retentionRateTag">&lt;</if>
        aiampd.sm_insurance_retention_rate_target
        order by
        rank asc
        limit 3;
    </select>
</mapper>