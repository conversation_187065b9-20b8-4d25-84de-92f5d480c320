<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.report.dao.safepg.AssistantAdminMapper">

    <!--      where pt in  <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
        and district_code in
        <foreach collection="districtCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        <if test="areaName != null">
            area_name=#{areaName}
        </if>-->

    <select id="listBchData" resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantBchVO">
        with t_sm_share_cnt as (
        select
        CAST(sum(sm_effective_share_cnt) as numeric(18,2)) as sm_effective_share_cnt
        ,pt
        ,area_name,
        area_code
        ,bch_name
        ,bch_code
        from
        report.ads_insurance_bch_online_promotion_dfp
        where
        pt in <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
        and bch_code = #{query.bchCode}
        <if test="query.districtCodes != null and query.districtCodes.size() > 0">
            and district_code in
            <foreach collection="query.districtCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="query.areaCodeDataAuth != null">
            and  area_code=#{query.areaCodeDataAuth}
        </if>
        <if test="query.bchCodeDataAuth != null">
            and  bch_code=#{query.bchCodeDataAuth}
        </if>
        GROUP BY area_name,
        area_code,
        pt
        ,bch_name
        ,bch_code
        ),
        t_sm_ms_emp_cnt as (
        select
        CAST(sum(ms_emp_cnt) as numeric(18,2)) as ms_emp_cnt
        ,pt
        ,area_name,
        area_code
        ,bch_name
        ,bch_code
        from
        report.ads_insurance_bch_marketing_progress_dfp
        where
        pt in <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
        and bch_code = #{query.bchCode}
        <if test="query.districtCodes != null and query.districtCodes.size() > 0">
            and district_code in
            <foreach collection="query.districtCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="query.areaCodeDataAuth != null">
            and  area_code=#{query.areaCodeDataAuth}
        </if>
        <if test="query.bchCodeDataAuth != null">
            and  bch_code=#{query.bchCodeDataAuth}
        </if>
        GROUP BY area_name,
        area_code,
        pt
        ,bch_name
        ,bch_code
        )
        SELECT a.area_name,a.bch_name,a.bch_code,
        a.area_code,
        a.pt,
        MAX(sm_assess_convert_insurance_amt)              AS sm_assess_convert_insurance_amt,              -- 当月标准保费
        MAX(sy_assess_convert_insurance_amt)              AS sy_assess_convert_insurance_amt,              -- 当年标准保费
        MAX(sm_assess_convert_insurance_amt_target)       AS sm_assess_convert_insurance_amt_target,       -- 当月标准保费目标
        MAX(sy_assess_convert_insurance_amt_target)       AS sy_assess_convert_insurance_amt_target,       -- 当年标准保费目标
        MAX(sm_assess_convert_insurance_amt_achieve_rate) AS sm_assess_convert_insurance_amt_achieve_rate, -- 当月标准保费达成率
        MAX(sy_assess_convert_insurance_amt_achieve_rate) AS sy_assess_convert_insurance_amt_achieve_rate, -- 当年标准保费达成率
        round(MAX(sm_offline_loan_insurance_rate) ,4)            AS sm_offline_loan_insurance_rate,               -- 当月异业保费配比
        round(MAX(sy_offline_loan_insurance_rate) ,4)             AS sy_offline_loan_insurance_rate,               -- 当年异业保费配比
        round(MAX(sm_offline_loan_insurance_rate_target) ,4)      AS sm_offline_loan_insurance_rate_target,        -- 当月异业保费配比目标
        round(MAX(sy_offline_loan_insurance_rate_target),4)       AS sy_offline_loan_insurance_rate_target,        -- 当年异业保费配比目标
        MAX(sm_insurance_retention_rate)                  AS sm_insurance_retention_rate,                  -- 当月留存率
        MAX(sy_insurance_retention_rate)                  AS sy_insurance_retention_rate,                  -- 当年留存率
        MAX(sm_insurance_retention_rate_target)           AS sm_insurance_retention_rate_target,           -- 当月留存率目标
        MAX(sy_insurance_retention_rate_target)           AS sy_insurance_retention_rate_target,           -- 当年留存率目标
        MAX(sm_loan_insurance_retention_rate)             AS sm_loan_insurance_retention_rate,             -- 当月信贷客户留存率
        MAX(sy_loan_insurance_retention_rate)             AS sy_loan_insurance_retention_rate,             -- 当年信贷客户留存率
        MAX(sm_unloan_insurance_retention_rate)           AS sm_unloan_insurance_retention_rate,           -- 当月非信贷客户留存率
        MAX(sy_unloan_insurance_retention_rate)           AS sy_unloan_insurance_retention_rate,           -- 当年非信贷客户留存率
        MAX(sm_loan_cust_trans_rate)                      AS sm_loan_cust_trans_rate,                      -- 当月信贷客户转化率
        MAX(sy_loan_cust_trans_rate)                      AS sy_loan_cust_trans_rate                       -- 当年信贷客户转化率
        ,case when sum(tme.ms_emp_cnt)=0 then 0 else round(sum(tsc.sm_effective_share_cnt) / sum(tme.ms_emp_cnt),2) end sm_share_cnt_rate -- 当月平均有效分享率
        FROM report.ads_insurance_bch_marketing_progress_dfp a
        left join t_sm_share_cnt tsc on tsc.pt = a.pt and tsc.area_code = a.area_code and tsc.area_name = a.area_name and tsc.bch_name = a.bch_name and tsc.bch_code = a.bch_code
        left join t_sm_ms_emp_cnt tme on tme.pt = a.pt and tme.area_code = a.area_code and tme.area_name = a.area_name and tme.bch_name = a.bch_name and tme.bch_code = a.bch_code
        where a.pt in <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
          and a.bch_code = #{query.bchCode}
        <if test="query.districtCodes != null and query.districtCodes.size() > 0">
            and a.district_code in
            <foreach collection="query.districtCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
          <if test="query.areaCodeDataAuth != null">
            and  a.area_code=#{query.areaCodeDataAuth}
        </if>
        <if test="query.bchCodeDataAuth != null">
            and  a.bch_code=#{query.bchCodeDataAuth}
        </if>
        GROUP BY a.area_name, a.area_code, a.pt,a.bch_name,a.bch_code
    </select>
    <select id="listDistrictData" resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantBchVO">
        with t_sm_share_cnt as (
        select
        CAST(sum(sm_effective_share_cnt) as numeric(18,2)) as sm_effective_share_cnt
        ,pt
        ,area_name,
        area_code
        from
        report.ads_insurance_district_online_promotion_dfp
        where
        pt in <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
        <if test="query.districtCodes != null and query.districtCodes.size() > 0">
            and district_code in
            <foreach collection="query.districtCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="query.areaCodeDataAuth != null">
            and  area_code=#{query.areaCodeDataAuth}
        </if>
        GROUP BY area_name,
        area_code,
        pt
        ),
        t_sm_ms_emp_cnt as (
        select
        CAST(sum(ms_emp_cnt) as numeric(18,2)) as ms_emp_cnt
        ,pt
        ,area_name,
        area_code
        from
        report.ads_insurance_district_marketing_progress_dfp
        where
        pt in <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
        <if test="query.districtCodes != null and query.districtCodes.size() > 0">
            and district_code in
            <foreach collection="query.districtCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="query.areaCodeDataAuth != null">
            and  area_code=#{query.areaCodeDataAuth}
        </if>
        GROUP BY area_name,
        area_code,
        pt
        )
        SELECT
        a.area_name,
        a.area_code,
        a.pt,
        SUM(sm_assess_convert_insurance_amt) AS sm_assess_convert_insurance_amt,
        SUM(sy_assess_convert_insurance_amt) AS sy_assess_convert_insurance_amt,
        SUM(sm_assess_convert_insurance_amt_target) AS sm_assess_convert_insurance_amt_target,
        SUM(sy_assess_convert_insurance_amt_target) AS sy_assess_convert_insurance_amt_target,
        round(
        SUM(sm_assess_convert_insurance_amt) / NULLIF(SUM(sm_assess_convert_insurance_amt_target), 0),
        4
        ) AS sm_assess_convert_insurance_amt_achieve_rate,
        round(
        SUM(sy_assess_convert_insurance_amt) / NULLIF(SUM(sy_assess_convert_insurance_amt_target), 0),
        4
        ) AS sy_assess_convert_insurance_amt_achieve_rate,
        round(
        sum(sm_loan_insurance_amt) / NULLIF(SUM(sm_loan_offline_amt), 0),
        4
        ) AS sm_offline_loan_insurance_rate,
        round(
        sum(sy_loan_insurance_amt) / NULLIF(SUM(sy_loan_offline_amt), 0),
        4
        ) AS sy_offline_loan_insurance_rate,
        round(
        max(sm_offline_loan_insurance_rate_target) ,
        4
        ) AS sm_offline_loan_insurance_rate_target,
        round(
        max(sy_offline_loan_insurance_rate_target) ,
        4
        ) AS sy_offline_loan_insurance_rate_target,
        round(
        SUM(sm_insurance_expire_retention_cust_cnt) / NULLIF(sum(sm_insurance_expire_cust_cnt), 0),
        4
        ) AS sm_insurance_retention_rate,
        round(
        SUM(sy_insurance_expire_retention_cust_cnt) / NULLIF(sum(sy_insurance_expire_cust_cnt), 0),
        4
        ) AS sy_insurance_retention_rate,
        max(sm_insurance_retention_rate_target) AS sm_insurance_retention_rate_target,
        max(sy_insurance_retention_rate_target) AS sy_insurance_retention_rate_target
        ,case when sum(tme.ms_emp_cnt)=0 then 0 else round(sum(tsc.sm_effective_share_cnt) / sum(tme.ms_emp_cnt),2) end sm_share_cnt_rate -- 当月平均有效分享率
        FROM
        report.ads_insurance_district_marketing_progress_dfp a
        left join t_sm_share_cnt tsc on tsc.pt = a.pt and tsc.area_code = a.area_code and tsc.area_name = a.area_name
        left join t_sm_ms_emp_cnt tme on tme.pt = a.pt and tme.area_code = a.area_code and tme.area_name = a.area_name
        where a.pt in <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
        <if test="query.districtCodes != null and query.districtCodes.size() > 0">
            and a.district_code in
            <foreach collection="query.districtCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
          <if test="query.areaCodeDataAuth != null">
         and  a.area_code=#{query.areaCodeDataAuth}
        </if>
        GROUP BY a.area_name, a.area_code, a.pt
    </select>

    <select id="listAreaData" resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantBchVO">
        with t_sm_share_cnt as (
        select
        CAST(sum(sm_effective_share_cnt) as numeric(18,2)) as sm_effective_share_cnt
        ,pt
        ,area_name,
        area_code
        from
        report.ads_insurance_area_online_promotion_dfp
        where
        pt in <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
        and area_code = #{query.areaCode}
        <if test="query.areaCodeDataAuth != null">
            and  area_code=#{query.areaCodeDataAuth}
        </if>
        GROUP BY area_name,
        area_code,
        pt
        ),
        t_sm_ms_emp_cnt as (
        select
        CAST(sum(ms_emp_cnt) as numeric(18,2)) as ms_emp_cnt
        ,pt
        ,area_name,
        area_code
        from
        report.ads_insurance_area_marketing_progress_dfp
        where
        pt in <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
        and area_code = #{query.areaCode}
        <if test="query.areaCodeDataAuth != null">
            and  area_code=#{query.areaCodeDataAuth}
        </if>
        GROUP BY area_name,
        area_code,
        pt
        )
        SELECT a.area_name,
        a.area_code,
        a.pt,
        MAX(sm_assess_convert_insurance_amt)              AS sm_assess_convert_insurance_amt,              -- 当月标准保费
        MAX(sy_assess_convert_insurance_amt)              AS sy_assess_convert_insurance_amt,              -- 当年标准保费
        MAX(sm_assess_convert_insurance_amt_target)       AS sm_assess_convert_insurance_amt_target,       -- 当月标准保费目标
        MAX(sy_assess_convert_insurance_amt_target)       AS sy_assess_convert_insurance_amt_target,       -- 当年标准保费目标
        MAX(sm_assess_convert_insurance_amt_achieve_rate) AS sm_assess_convert_insurance_amt_achieve_rate, -- 当月标准保费达成率
        MAX(sy_assess_convert_insurance_amt_achieve_rate) AS sy_assess_convert_insurance_amt_achieve_rate, -- 当年标准保费达成率
        round(MAX(sm_offline_loan_insurance_rate),4)               AS sm_offline_loan_insurance_rate,               -- 当月异业保费配比
        round(MAX(sy_offline_loan_insurance_rate),4)               AS sy_offline_loan_insurance_rate,               -- 当年异业保费配比
        round(MAX(sm_offline_loan_insurance_rate_target),4)        AS sm_offline_loan_insurance_rate_target,        -- 当月异业保费配比目标
        round(MAX(sy_offline_loan_insurance_rate_target),4)       AS sy_offline_loan_insurance_rate_target,        -- 当年异业保费配比目标
        MAX(sm_insurance_retention_rate)                  AS sm_insurance_retention_rate,                  -- 当月留存率
        MAX(sy_insurance_retention_rate)                  AS sy_insurance_retention_rate,                  -- 当年留存率
        MAX(sm_insurance_retention_rate_target)           AS sm_insurance_retention_rate_target,           -- 当月留存率目标
        MAX(sy_insurance_retention_rate_target)           AS sy_insurance_retention_rate_target,           -- 当年留存率目标
        MAX(sm_loan_insurance_retention_rate)             AS sm_loan_insurance_retention_rate,             -- 当月信贷客户留存率
        MAX(sy_loan_insurance_retention_rate)             AS sy_loan_insurance_retention_rate,             -- 当年信贷客户留存率
        MAX(sm_unloan_insurance_retention_rate)           AS sm_unloan_insurance_retention_rate,           -- 当月非信贷客户留存率
        MAX(sy_unloan_insurance_retention_rate)           AS sy_unloan_insurance_retention_rate,           -- 当年非信贷客户留存率
        MAX(sm_loan_cust_trans_rate)                      AS sm_loan_cust_trans_rate,                      -- 当月信贷客户转化率
        MAX(sy_loan_cust_trans_rate)                      AS sy_loan_cust_trans_rate                       -- 当年信贷客户转化率
        ,case when sum(tme.ms_emp_cnt)=0 then 0 else round(sum(tsc.sm_effective_share_cnt) / sum(tme.ms_emp_cnt),2) end sm_share_cnt_rate -- 当月平均有效分享率
        FROM report.ads_insurance_area_marketing_progress_dfp a
        left join t_sm_share_cnt tsc on tsc.pt = a.pt and tsc.area_code = a.area_code and tsc.area_name = a.area_name
        left join t_sm_ms_emp_cnt tme on tme.pt = a.pt and tme.area_code = a.area_code and tme.area_name = a.area_name
        where a.pt in  <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
          and a.area_code = #{query.areaCode}
        <if test="query.areaCodeDataAuth != null">
          and  a.area_code=#{query.areaCodeDataAuth}
        </if>
        GROUP BY a.area_name,
                 a.area_code,
                 a.pt
        order by a.pt
    </select>
    <select id="listAllData" resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantBchVO">
        with t_sm_share_cnt as (
            select
            CAST(sum(sm_effective_share_cnt) as numeric(18,2)) as sm_effective_share_cnt
                ,pt
            from
            report.ads_insurance_area_online_promotion_dfp
            where
                pt in <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
            group by pt
        ),
        t_sm_ms_emp_cnt as (
            select
            CAST(sum(ms_emp_cnt) as numeric(18,2)) as ms_emp_cnt
                ,pt
            from
                report.ads_insurance_area_marketing_progress_dfp
            where
                pt in <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
            group by pt
        )
        select sm_assess_convert_insurance_amt,
        sy_assess_convert_insurance_amt,
        sm_assess_convert_insurance_amt_target,
        sy_assess_convert_insurance_amt_target,
        sm_assess_convert_insurance_amt_achieve_rate,
        sy_assess_convert_insurance_amt_achieve_rate,
        round(sm_offline_loan_insurance_rate,4) sm_offline_loan_insurance_rate,
        round(sy_offline_loan_insurance_rate,4) sy_offline_loan_insurance_rate,
        round(sm_offline_loan_insurance_rate_target,4) sm_offline_loan_insurance_rate_target,
        round(sy_offline_loan_insurance_rate_target,4) sy_offline_loan_insurance_rate_target,
        sm_insurance_retention_rate,
        sy_insurance_retention_rate,
        sm_insurance_retention_rate_target,
        sy_insurance_retention_rate_target,
        a.pt,
        case when tme.ms_emp_cnt=0 then 0 else round(tsc.sm_effective_share_cnt / tme.ms_emp_cnt, 2) end AS sm_share_cnt_rate -- 当月平均有效分享率
        from report.ads_insurance_cfpamf_marketing_progress_dfp a
        left join t_sm_share_cnt tsc on tsc.pt = a.pt
        left join t_sm_ms_emp_cnt tme on tme.pt = a.pt
          where a.pt in
        <foreach collection="pts" item="item" open="(" close=")" separator=",">#{item}</foreach>
        order by a.pt
    </select>


    <select id="listAreaMonthAmtRank"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantRankAmtVO">

        SELECT row_number()
                       over (order by sm_assess_convert_insurance_amt_achieve_rate desc, sm_assess_convert_insurance_amt desc)
                                                               rank_num,
               area_name,
               area_code,
               (sm_assess_convert_insurance_amt)            AS assess_convert_insurance_amt,        -- 当月标准保费
               (sm_assess_convert_insurance_amt_target)     AS assess_convert_insurance_amt_target, -- 当月标准保费目标
               sm_assess_convert_insurance_amt_achieve_rate AS assess_convert_insurance_amt_achieve_rate
        FROM report.ads_insurance_area_marketing_progress_dfp
        where pt = #{pt}
        order by sm_assess_convert_insurance_amt_achieve_rate desc, sm_assess_convert_insurance_amt desc
    </select>

    <select id="listAreaYearAmtRank"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantRankAmtVO">

        SELECT row_number()
                       over (order by sy_assess_convert_insurance_amt_achieve_rate desc, sy_assess_convert_insurance_amt desc)
                                                               rank_num,
               area_name,
               area_code,
               sy_assess_convert_insurance_amt            AS assess_convert_insurance_amt,        -- 当月标准保费
               sy_assess_convert_insurance_amt_target     AS assess_convert_insurance_amt_target, -- 当月标准保费目标
               sy_assess_convert_insurance_amt_achieve_rate AS assess_convert_insurance_amt_achieve_rate
        FROM report.ads_insurance_area_marketing_progress_dfp
        where pt = #{pt}
        order by sy_assess_convert_insurance_amt_achieve_rate desc, sy_assess_convert_insurance_amt desc
    </select>
    <select id="listAmtRank" resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantRankAmtVO">

        SELECT row_number()  over (${orderSql})
            rank_num,
       ${nameCol},
       ${colSql}
        FROM report.${tableName}
        where pt = #{pt}
        <if test="query.areaName != null">
            and  area_name=#{query.areaName}
        </if>
         <if test="tableName == 'ads_insurance_emp_marketing_progress_dfp'" >
            <if test="query.bchName != null">
                and bch_name=#{query.bchName}
            </if>
             <if test="query.bchCode != null">
                 and bch_code=#{query.bchCode}
             </if>
             <if test="query.districtCodes != null and query.districtCodes.size() > 0">
                 and district_code in
                 <foreach collection="query.districtCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
             </if>
         </if>
            ${orderSql}
    </select>
    <select id="listRank" resultType="java.util.Map">
        SELECT row_number()  over (${orderSql})
        rank_num,
        ${colSql}
        FROM report.${tableName}
        where pt = #{pt}
        <if test="query.areaCodeDataAuth != null">
            and  area_code=#{query.areaCodeDataAuth}
        </if>

        <if test="query.areaCode != null and query.areaCode != ''">
            and  area_code=#{query.areaCode}
        </if>

          <if test="query.dimName != null and query.dimName != ''">
              and ${query.dimCol} like concat('%',#{query.dimName},'%')
          </if>
        <if test="query.areaDim.name() != 'ALL'" >
            <if test="query.bchCodeDataAuth != null">
                and bch_code=#{query.bchCodeDataAuth}
            </if>
            <if test="query.bchCode != null and query.bchCode!=''">
                and bch_code=#{query.bchCode}
            </if>
            <if test="query.districtCodes != null and query.districtCodes.size() > 0">
                and district_code in
                <foreach collection="query.districtCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
        </if>
        <if  test="query.dimCol == 'emp_name'">
            and leave_date is  null
        </if>
        ${orderSql}

    </select>

    <select id="listBchDataByBchCodeList" resultType="com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantBchVO">
        select
            t.ms_emp_cnt
        from ads_insurance_bch_marketing_progress_dfp t
        where
            t.area_code in
            <foreach collection="query.bchCodeList" item="bchCode" open="(" close=")" separator=",">
                #{bchCode,jdbcType=VARCHAR}
            </foreach>
    </select>
</mapper>