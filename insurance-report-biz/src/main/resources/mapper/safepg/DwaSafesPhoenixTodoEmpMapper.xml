<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.report.dao.safepg.DwaSafesPhoenixTodoEmpMapper">
    <resultMap type="com.cfpamf.ms.insur.report.pojo.po.DwaSafesPhoenixTodoEmp" id="DwaSafesPhoenixTodoEmpMap">
<!--        <result property="empName" column="emp_name" jdbcType="String"/>-->
<!--        <result property="empCode" column="emp_code" jdbcType="String"/>-->
<!--        <result property="bchName" column="bch_name" jdbcType="String"/>-->
<!--        <result property="bchCode" column="bch_code" jdbcType="String"/>-->
<!--        <result property="districtName" column="district_name" jdbcType="String"/>-->
<!--        <result property="districtCode" column="district_code" jdbcType="String"/>-->
<!--        <result property="areaCode" column="area_code" jdbcType="String"/>-->
<!--        <result property="areaName" column="area_name" jdbcType="String"/>-->
<!--        <result property="syInterruptionTodoCnt" column="sy_interruption_todo_cnt" jdbcType="int4"/>-->
<!--        <result property="syRenewShortTodoCnt" column="sy_renew_short_todo_cnt" jdbcType="int4"/>-->
<!--        <result property="syLoanFirstTodoCnt" column="sy_loan_first_todo_cnt" jdbcType="int4"/>-->
<!--        <result property="syTodoCnt" column="sy_todo_cnt" jdbcType="int4"/>-->
<!--        <result property="syInterruptionTodoFollowRate" column="sy_interruption_todo_follow_rate" jdbcType="Double"/>-->
<!--        <result property="syRenewShortTodoFollowRate" column="sy_renew_short_todo_follow_rate" jdbcType="Double"/>-->
<!--        <result property="syInterruptionTodoConversionRate" column="sy_interruption_todo_conversion_rate" jdbcType="Double"/>-->
<!--        <result property="syRenewShortTodoConversionRate" column="sy_renew_short_todo_conversion_rate" jdbcType="Double"/>-->
<!--        <result property="syInterruptionTodoConversionAmt" column="sy_interruption_todo_conversion_amt" jdbcType="Double"/>-->
<!--        <result property="syRenewShortTodoConversionAmt" column="sy_renew_short_todo_conversion_amt" jdbcType="Double"/>-->
<!--        <result property="pt" column="pt" jdbcType="String"/>-->
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="queryByEmpCode" resultMap="DwaSafesPhoenixTodoEmpMap">
        select
            emp_name,emp_code,bch_name,bch_code,district_name,district_code,area_code,area_name,
        sy_interruption_todo_cnt,
        sy_renew_short_todo_cnt,
        sy_loan_first_todo_cnt,
        sy_todo_cnt,
        sy_interruption_todo_follow_rate,
        sy_renew_short_todo_follow_rate,
        sy_interruption_todo_conversion_rate,
        sy_renew_short_todo_conversion_rate,
        sy_interruption_todo_conversion_amt,
        sy_renew_short_todo_conversion_amt,
        sm_interruption_todo_follow_cnt,
        sy_interruption_todo_follow_cnt,
        sy_interruption_tdo_follow_cust,
        sm_interruption_tdo_follow_cust,
        sy_tdo_short_follow_cnt,
        sm_tdo_short_follow_cnt,
        sm_tdo_short_follow_policy,
        sy_tdo_short_follow_policy,
        pt
        from report.dwa_safes_phoenix_todo_emp
        where emp_code = #{empCode} and pt = #{pt}
    </select>

    <select id="getCustomerFollowMetrics" resultType="com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveBasicDTO">
        select
            emp_code,emp_name,bch_name,bch_code,pt,interruption_todo_cnt,sy_interruption_todo_follow_cnt,sm_interruption_todo_follow_cnt,sm_interruption_tdo_follow_cust,sm_interruption_todo_conversion_amt
            ,renew_short_todo_cnt,sm_tdo_short_follow_cnt,sm_tdo_short_follow_policy,sm_renew_short_todo_conversion_amt
            ,renew_long_todo_cnt,sm_tdo_long_follow_cnt,sm_tdo_long_follow_policy,sm_renew_long_todo_conversion_amt
        from report.dwa_safes_phoenix_todo_emp
        where emp_code in
        <foreach collection="empCodes" item="empCode" open="(" separator="," close=")">
            #{empCode}
        </foreach>
        and pt = #{pt}
        and bch_code = #{bchCode}
    </select>
</mapper>