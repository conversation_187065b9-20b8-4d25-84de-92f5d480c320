<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.report.dao.dw.SmDcReportMapper">

    <!--业绩报表 begin-->
    <select id="listSmPersonalBusinessDetails" resultType="com.cfpamf.ms.insur.report.pojo.vo.PersonalBusinessVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        bch_name as orgName,
        emp_name as userName,
        emp_id as userId,
        sum(COALESCE(insured_cnt ,0) - COALESCE(surrender_cnt ,0))as insuredCnt,
        sum(COALESCE(insured_amt,0) - COALESCE(surrender_amt,0) ) as insuredAmt,
        sum(COALESCE(insured_amt_accident_person,0) - COALESCE(surrender_amt_accident_person,0)) as
        insuredAmtAccidentPerson,
        sum(COALESCE(insured_amt_accident_group,0) - COALESCE(surrender_amt_accident_group,0)) as
        insuredAmtAccidentGroup,
        sum(COALESCE(insured_amt_medical,0) - COALESCE(surrender_amt_medical,0)) as insuredAmtMedical,
        sum(COALESCE(insured_amt_liability,0) - COALESCE(surrender_amt_liability,0)) as insuredAmtLiability,
        sum(COALESCE(insured_amt_annuity,0) - COALESCE(surrender_amt_annuity,0)) as insuredAmtAnnuity,
        sum(COALESCE(insured_amt_illness,0) - COALESCE(surrender_amt_illness,0)) as insuredAmtIllness,
        sum(COALESCE(insured_amt_1y,0) - COALESCE(surrender_amt_1y,0)) as insuredAmt1y
        from rpt.insurance_order_emp_stat_dip
        WHERE
        1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>

        <if test='zoneCode != null and zoneCode!=""'>
            AND district_code = #{zoneCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE
            CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>

        <if test='startDate != null'>
            AND rpt_date>=#{startDate}

        </if>

        <if test='endDate != null'>
            <![CDATA[  AND rpt_date<= #{endDate} ]]>
        </if>
        group by area_name,bch_name,emp_id,emp_name
        ORDER BY
        <choose>
            <when test='sortType!=null and sortType=="insuredCnt"'>
                insuredCnt
            </when>
            <when test='sortType!=null and sortType=="insuredAmt"'>
                insuredAmt
            </when>
            <when test='sortType!=null and sortType=="insuredAmtAccidentPerson"'>
                insuredAmtAccidentPerson
            </when>
            <when test='sortType!=null and sortType=="insuredAmtAccidentGroup"'>
                insuredAmtAccidentGroup
            </when>
            <when test='sortType!=null and sortType=="insuredAmtMedical"'>
                insuredAmtMedical
            </when>
            <when test='sortType!=null and sortType=="insuredAmtLiability"'>
                insuredAmtLiability
            </when>
            <when test='sortType!=null and sortType=="insuredAmtAnnuity"'>
                insuredAmtAnnuity
            </when>
            <when test='sortType!=null and sortType=="insuredAmtIllness"'>
                insuredAmtIllness
            </when>
            <when test='sortType!=null and sortType=="insuredAmt1y"'>
                insuredAmt1y
            </when>
            <otherwise>
                area_name
            </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>


    <select id="listSmOrgBusinessDetails" resultType="com.cfpamf.ms.insur.report.pojo.vo.OrgBusinessVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        regionName,orgName,
        sum(COALESCE(empcnt,0))/count(rptMonth) as empCnt,
        planEmpCnt,
        sum(COALESCE(insured_cnt ,0))as insuredCnt,
        sum(COALESCE(insured_amt,0)) as insuredAmt,
        cast((case when sum(COALESCE(empcnt,0))/count(rptMonth) = 0 then 0 else sum(COALESCE(insured_amt,0.0000)) /
        (sum(COALESCE(empcnt,0.0000))/count(rptMonth)) end ) as decimal (10,2)) as empAverageAmt,
        cast((case when planEmpCnt =0 then 0 else sum(COALESCE(insured_amt,0.0000))
        / planEmpCnt end) as decimal(10,2)) as planEmpAverageAmt,

        sum(COALESCE(insured_cnt ,0))as insuredCnt,
        sum(COALESCE(insured_amt,0)) as insuredAmt,
        sum(COALESCE(insured_amt_accident_person,0)) as insuredAmtAccidentPerson,
        sum(COALESCE(insured_amt_accident_group,0)) as insuredAmtAccidentGroup,
        sum(COALESCE(insured_amt_medical,0)) as insuredAmtMedical,
        sum(COALESCE(insured_amt_liability,0)) as insuredAmtLiability,
        sum(COALESCE(insured_amt_annuity,0)) as insuredAmtAnnuity,
        sum(COALESCE(insured_amt_illness,0)) as insuredAmtIllness,
        sum(COALESCE(insured_amt_1y,0)) as insuredAmt1y
        from
        (select
        to_char(rpt_date,'yyyy-mm') as rptMonth,
        area_name as regionName,
        bch_name as orgName,
        0 as planEmpCnt,
        emp_cnt as empCnt,
        sum(COALESCE(insured_cnt ,0) - COALESCE(surrender_cnt ,0))as insured_cnt,
        sum(COALESCE(insured_amt,0) - COALESCE(surrender_amt ,0)) as insured_amt,
        sum(COALESCE(insured_amt_accident_person,0) - COALESCE(surrender_amt_accident_person ,0)) as
        insured_amt_accident_person,
        sum(COALESCE(insured_amt_accident_group,0) - COALESCE(surrender_amt_accident_group ,0)) as
        insured_amt_accident_group,
        sum(COALESCE(insured_amt_medical,0) - COALESCE(surrender_amt_medical ,0)) as insured_amt_medical,
        sum(COALESCE(insured_amt_liability,0) - COALESCE(surrender_amt_liability ,0)) as insured_amt_liability,
        sum(COALESCE(insured_amt_annuity,0) - COALESCE(surrender_amt_annuity ,0)) as insured_amt_annuity,
        sum(COALESCE(insured_amt_illness,0) - COALESCE(surrender_amt_illness ,0)) as insured_amt_illness,
        sum(COALESCE(insured_amt_1y,0) - COALESCE(surrender_amt_1y ,0)) as insured_amt_1y
        from rpt.insurance_order_bch_stat_dip

        WHERE
        1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='zoneCode != null and zoneCode!=""'>
            AND district_code = #{zoneCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>

        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>

        <if test='startDate != null'>
            AND rpt_date>=#{startDate}

        </if>

        <if test='endDate != null'>
            <![CDATA[  AND rpt_date<= #{endDate} ]]>
        </if>
        group by area_name,bch_name,planEmpCnt,empCnt,rptMonth
        ) a
        group by regionName,orgName,planEmpCnt
        ORDER BY
        <choose>
            <when test='sortType!=null and sortType=="insuredCnt"'>
                insuredCnt
            </when>
            <when test='sortType!=null and sortType=="insuredAmt"'>
                insuredAmt
            </when>
            <when test='sortType!=null and sortType=="insuredAmtAccidentPerson"'>
                insuredAmtAccidentPerson
            </when>
            <when test='sortType!=null and sortType=="insuredAmtAccidentGroup"'>
                insuredAmtAccidentGroup
            </when>
            <when test='sortType!=null and sortType=="insuredAmtMedical"'>
                insuredAmtMedical
            </when>
            <when test='sortType!=null and sortType=="insuredAmtLiability"'>
                insuredAmtLiability
            </when>
            <when test='sortType!=null and sortType=="insuredAmtAnnuity"'>
                insuredAmtAnnuity
            </when>
            <when test='sortType!=null and sortType=="insuredAmtIllness"'>
                insuredAmtIllness
            </when>
            <when test='sortType!=null and sortType=="insuredAmt1y"'>
                insuredAmt1y
            </when>
            <when test='sortType!=null and sortType=="empAverageAmt"'>
                empAverageAmt
            </when>
            <when test='sortType!=null and sortType=="planEmpAverageAmt"'>
                planEmpAverageAmt
            </when>
            <otherwise>
                regionName
            </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>

    </select>


    <select id="listSmRegionBusinessDetails" resultType="com.cfpamf.ms.insur.report.pojo.vo.RegionBusinessVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        regionName,
        sum(COALESCE(empcnt,0))/count(distinct rptMonth) as empCnt,
        planEmpCnt,
        sum(COALESCE(insured_cnt ,0))as insuredCnt,
        sum(COALESCE(insured_amt,0)) as insuredAmt,
        cast((case when sum(COALESCE(empcnt,0))/count(distinct rptMonth) = 0 then 0 else
        sum(COALESCE(insured_amt,0.0000)) / (sum(COALESCE(empcnt,0.0000))/count(distinct rptMonth)) end ) as decimal
        (10,2)) as empAverageAmt,
        cast((case when planEmpCnt =0 then 0 else sum(COALESCE(insured_amt,0.0000))
        / planEmpCnt end) as decimal(10,2)) as planEmpAverageAmt,

        sum(COALESCE(insured_cnt ,0))as insuredCnt,
        sum(COALESCE(insured_amt,0)) as insuredAmt,
        sum(COALESCE(insured_amt_accident_person,0)) as insuredAmtAccidentPerson,
        sum(COALESCE(insured_amt_accident_group,0)) as insuredAmtAccidentGroup,
        sum(COALESCE(insured_amt_medical,0)) as insuredAmtMedical,
        sum(COALESCE(insured_amt_liability,0)) as insuredAmtLiability,
        sum(COALESCE(insured_amt_annuity,0)) as insuredAmtAnnuity,
        sum(COALESCE(insured_amt_illness,0)) as insuredAmtIllness,
        sum(COALESCE(insured_amt_1y,0)) as insuredAmt1y
        from
        (select
        to_char(rpt_date,'yyyy-mm') as rptMonth,
        area_name as regionName,
        bch_name as orgName,
        0 as planEmpCnt,
        emp_cnt as empCnt,
        sum(COALESCE(insured_cnt ,0) - COALESCE(surrender_cnt ,0))as insured_cnt,
        sum(COALESCE(insured_amt,0) - COALESCE(surrender_amt ,0)) as insured_amt,
        sum(COALESCE(insured_amt_accident_person,0) - COALESCE(surrender_amt_accident_person ,0)) as
        insured_amt_accident_person,
        sum(COALESCE(insured_amt_accident_group,0) - COALESCE(surrender_amt_accident_group ,0)) as
        insured_amt_accident_group,
        sum(COALESCE(insured_amt_medical,0) - COALESCE(surrender_amt_medical ,0)) as insured_amt_medical,
        sum(COALESCE(insured_amt_liability,0) - COALESCE(surrender_amt_liability ,0)) as insured_amt_liability,
        sum(COALESCE(insured_amt_annuity,0) - COALESCE(surrender_amt_annuity ,0)) as insured_amt_annuity,
        sum(COALESCE(insured_amt_illness,0) - COALESCE(surrender_amt_illness ,0)) as insured_amt_illness,
        sum(COALESCE(insured_amt_1y,0) - COALESCE(surrender_amt_1y ,0)) as insured_amt_1y
        from rpt.insurance_order_bch_stat_dip

        WHERE
        1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='zoneCode != null and zoneCode!=""'>
            AND district_code = #{zoneCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>

        <if test='startDate != null'>
            AND rpt_date>=#{startDate}

        </if>

        <if test='endDate != null'>
            <![CDATA[  AND rpt_date<= #{endDate} ]]>
        </if>
        group by area_name,bch_name,planEmpCnt,empCnt,rptMonth
        ) a
        group by regionName,planEmpCnt
        ORDER BY
        <choose>
            <when test='sortType!=null and sortType=="insuredCnt"'>
                insuredCnt
            </when>
            <when test='sortType!=null and sortType=="insuredAmt"'>
                insuredAmt
            </when>
            <when test='sortType!=null and sortType=="insuredAmtAccidentPerson"'>
                insuredAmtAccidentPerson
            </when>
            <when test='sortType!=null and sortType=="insuredAmtAccidentGroup"'>
                insuredAmtAccidentGroup
            </when>
            <when test='sortType!=null and sortType=="insuredAmtMedical"'>
                insuredAmtMedical
            </when>
            <when test='sortType!=null and sortType=="insuredAmtLiability"'>
                insuredAmtLiability
            </when>
            <when test='sortType!=null and sortType=="insuredAmtAnnuity"'>
                insuredAmtAnnuity
            </when>
            <when test='sortType!=null and sortType=="insuredAmtIllness"'>
                insuredAmtIllness
            </when>
            <when test='sortType!=null and sortType=="insuredAmt1y"'>
                insuredAmt1y
            </when>
            <when test='sortType!=null and sortType=="empAverageAmt"'>
                empAverageAmt
            </when>
            <when test='sortType!=null and sortType=="planEmpAverageAmt"'>
                planEmpAverageAmt
            </when>
            <otherwise>
                regionName
            </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>

    </select>
    <!--业绩报表 end-->

    <!--续保率报表 begin-->
    <!--续保率汇总-->
    <select id="listRenewalRate" resultType="com.cfpamf.ms.insur.report.pojo.vo.RenewalRateVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        sum(COALESCE(end_cnt,0)) as endCnt,
        sum(COALESCE(renewal_cnt,0)) as renewalCnt,
        cast((case when sum(COALESCE(end_cnt,0))=0 then 0 else
        sum(COALESCE(renewal_cnt,0.0000))/sum(COALESCE(end_cnt,0)) end ) *100 as decimal (10,2)) as renewalRate,

        sum(COALESCE(start_amount,0)) as startAmount,
        sum(COALESCE(renewal_amount,0)) as renewalAmount,
        cast((case when sum(COALESCE(start_amount,0))=0 then 0 else
        sum(COALESCE(renewal_amount,0.0000))/sum(COALESCE(start_amount,0)) end ) *100 as decimal (10,2)) as
        renewalAmountRate,

        sum(COALESCE (end_loaner_cnt,0)) as endLoanerCnt,
        sum(COALESCE (renewal_loaner_cnt,0)) as renewalLoanerCnt,
        cast((case when sum(COALESCE(end_loaner_cnt,0))=0 then 0 else
        sum(COALESCE(renewal_loaner_cnt,0.0000))/sum(COALESCE(end_loaner_cnt,0)) end ) *100 as decimal (10,2)) as
        loanerRenewalRate,

        sum(COALESCE (end_loancust_cnt,0)) as endLoancustCnt,
        sum(COALESCE (renewal_loancust_cnt,0)) as renewalLoancustCnt,
        cast((case when sum(COALESCE(end_loancust_cnt,0))=0 then 0 else
        sum(COALESCE(renewal_loancust_cnt,0.0000))/sum(COALESCE(end_loancust_cnt,0)) end ) *100 as decimal (10,2)) as
        loancustRenewalRate,

        cast((case when (sum(COALESCE(end_cnt,0))-sum(COALESCE(end_loancust_cnt,0))-sum(COALESCE (end_loaner_cnt,0)))
        <![CDATA[  <= ]]> 0 then 0
        else (sum(COALESCE(renewal_cnt,0.0000))-sum(COALESCE(renewal_loaner_cnt,0.0000))-sum(COALESCE
        (renewal_loancust_cnt,0.0000)))/(sum(COALESCE(end_cnt,0))-sum(COALESCE(end_loancust_cnt,0))-sum(COALESCE
        (end_loaner_cnt,0))) end ) *100 as decimal (10,2)) as noLoanRenewalRate,
        sum(COALESCE(person_accident_end,0)) as personAccidentEnd,
        sum(COALESCE(person_accident_renewal,0)) as personAccidentRenewal,
        cast((case when sum(COALESCE(person_accident_end,0)) =0 then 0 else
        sum(COALESCE(person_accident_renewal,0.0000))/sum(COALESCE(person_accident_end,0)) end)*100 AS decimal(10,2)) as
        personRenewalRate,
        sum(COALESCE(medical_end,0)) as medicalEnd,
        sum(COALESCE(medical_renewal,0)) as medicalRenewal,
        cast((case when sum(COALESCE(medical_end,0)) =0 then 0 else
        sum(COALESCE(medical_renewal,0.0000))/sum(COALESCE(medical_end,0)) end)*100 AS decimal(10,2)) as
        medicalRenewalRate,
        sum(COALESCE(group_accident_end,0)) as groupAccidentEnd,
        sum(COALESCE(group_accident_renewal,0)) as groupAccidentRenewal,
        cast((case when sum(COALESCE(group_accident_end,0)) =0 then 0 else
        sum(COALESCE(group_accident_renewal,0.0000))/sum(COALESCE(group_accident_end,0)) end)*100 AS decimal(10,2)) as
        groupAccidentRenewalRate,
        sum(COALESCE(liability_end,0)) as liabilityEnd,
        sum(COALESCE(liability_renewal,0)) as liabilityRenewal,
        cast((case when sum(COALESCE(liability_end,0)) =0 then 0 else
        sum(COALESCE(liability_renewal,0.0000))/sum(COALESCE(liability_end,0)) end)*100 AS decimal(10,2)) as
        liabilityRenewalRate
        from rpt.insurance_renewal_bch_stat_dfp

        WHERE
        1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>
        <if test='endDate != null'>
            AND rpt_date=#{endDate}
        </if>
        <choose>
            <when test='recordType=="year"'>
                AND dtype='year'
            </when>
            <otherwise>
                AND dtype='month'
            </otherwise>
        </choose>
        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <include refid="listRenewalOrderBy"/>

    </select>
    <!--个人续保率汇总-->
    <select id="listPersonalRenewalRate" resultType="com.cfpamf.ms.insur.report.pojo.vo.PersonalRenewalRateVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        bch_name as orgName,
        emp_name as userName,
        emp_id as userId,
        COALESCE(end_cnt,0) as endCnt,
        COALESCE(renewal_cnt,0) as renewalCnt,
        cast((case when COALESCE(end_cnt,0)=0 then 0 else COALESCE(renewal_cnt,0.0000)/COALESCE(end_cnt,0) end ) *100 as
        decimal (10,2)) as renewalRate,

        COALESCE(renewal_rate_rank_all,0) as renewalRateRankAll,
        COALESCE(renewal_rate_rank_area,0) as renewalRateRankArea,

        COALESCE(start_amount,0) as startAmount,
        COALESCE(renewal_amount,0) as renewalAmount,
        cast((case when COALESCE(start_amount,0)=0 then 0 else COALESCE(renewal_amount,0.0000)/COALESCE(start_amount,0)
        end ) *100 as decimal (10,2)) as renewalAmountRate,

        COALESCE (end_loaner_cnt,0) as endLoanerCnt,
        COALESCE (renewal_loaner_cnt,0) as renewalLoanerCnt,
        cast((case when COALESCE(end_loaner_cnt,0)=0 then 0 else
        COALESCE(renewal_loaner_cnt,0.0000)/COALESCE(end_loaner_cnt,0) end ) *100 as decimal (10,2)) as
        loanerRenewalRate,

        COALESCE (end_loancust_cnt,0) as endLoancustCnt,
        COALESCE (renewal_loancust_cnt,0) as renewalLoancustCnt,
        cast((case when COALESCE(end_loancust_cnt,0)=0 then 0 else
        COALESCE(renewal_loancust_cnt,0.0000)/COALESCE(end_loancust_cnt,0) end ) *100 as decimal (10,2)) as
        loancustRenewalRate,

        cast((case when (COALESCE(end_cnt,0)-COALESCE(end_loancust_cnt,0)-COALESCE (end_loaner_cnt,0)) <![CDATA[  <= ]]>
        0 then 0
        else (COALESCE(renewal_cnt,0.0000)-COALESCE(renewal_loaner_cnt,0.0000)-COALESCE
        (renewal_loancust_cnt,0.0000))/(COALESCE(end_cnt,0)-COALESCE(end_loancust_cnt,0)-COALESCE (end_loaner_cnt,0))
        end ) *100 as decimal (10,2)) as noLoanRenewalRate,
        COALESCE(person_accident_end,0) as personAccidentEnd,
        COALESCE(person_accident_renewal,0) as personAccidentRenewal,
        cast((case when COALESCE(person_accident_end,0) =0 then 0 else
        COALESCE(person_accident_renewal,0.0000)/COALESCE(person_accident_end,0) end)*100 AS decimal(10,2)) as
        personRenewalRate,
        COALESCE(medical_end,0) as medicalEnd,
        COALESCE(medical_renewal,0) as medicalRenewal,
        cast((case when COALESCE(medical_end,0) =0 then 0 else COALESCE(medical_renewal,0.0000)/COALESCE(medical_end,0)
        end)*100 AS decimal(10,2)) as medicalRenewalRate,
        COALESCE(group_accident_end,0) as groupAccidentEnd,
        COALESCE(group_accident_renewal,0) as groupAccidentRenewal,
        cast((case when COALESCE(group_accident_end,0) =0 then 0 else
        COALESCE(group_accident_renewal,0.0000)/COALESCE(group_accident_end,0) end)*100 AS decimal(10,2)) as
        groupAccidentRenewalRate,
        COALESCE(liability_end,0) as liabilityEnd,
        COALESCE(liability_renewal,0) as liabilityRenewal,
        cast((case when COALESCE(liability_end,0) =0 then 0 else
        COALESCE(liability_renewal,0.0000)/COALESCE(liability_end,0) end)*100 AS decimal(10,2)) as liabilityRenewalRate
        from rpt.insurance_renewal_emp_stat_dfp

        WHERE
        1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE
            CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>

        <if test='endDate != null'>
            AND rpt_date=#{endDate}
        </if>
        <choose>
            <when test='recordType=="year"'>
                AND dtype='year'
            </when>
            <otherwise>
                AND dtype='month'
            </otherwise>
        </choose>
        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="listRenewalOrderBy"/>
    </select>

    <!--机构续保率汇总-->
    <select id="listOrgRenewalRate" resultType="com.cfpamf.ms.insur.report.pojo.vo.OrgRenewalRateVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        bch_name as orgName,
        COALESCE(end_cnt,0) as endCnt,
        COALESCE(renewal_cnt,0) as renewalCnt,
        cast((case when COALESCE(end_cnt,0)=0 then 0 else COALESCE(renewal_cnt,0.0000)/COALESCE(end_cnt,0) end ) *100 as
        decimal (10,2)) as renewalRate,

        COALESCE(renewal_rate_rank_all,0) as renewalRateRankAll,
        COALESCE(renewal_rate_rank_area,0) as renewalRateRankArea,

        COALESCE(start_amount,0) as startAmount,
        COALESCE(renewal_amount,0) as renewalAmount,
        cast((case when COALESCE(start_amount,0)=0 then 0 else COALESCE(renewal_amount,0.0000)/COALESCE(start_amount,0)
        end ) *100 as decimal (10,2)) as renewalAmountRate,

        COALESCE (end_loaner_cnt,0) as endLoanerCnt,
        COALESCE (renewal_loaner_cnt,0) as renewalLoanerCnt,
        cast((case when COALESCE(end_loaner_cnt,0)=0 then 0 else
        COALESCE(renewal_loaner_cnt,0.0000)/COALESCE(end_loaner_cnt,0) end ) *100 as decimal (10,2)) as
        loanerRenewalRate,

        COALESCE (end_loancust_cnt,0) as endLoancustCnt,
        COALESCE (renewal_loancust_cnt,0) as renewalLoancustCnt,
        cast((case when COALESCE(end_loancust_cnt,0)=0 then 0 else
        COALESCE(renewal_loancust_cnt,0.0000)/COALESCE(end_loancust_cnt,0) end ) *100 as decimal (10,2)) as
        loancustRenewalRate,

        cast((case when (COALESCE(end_cnt,0)-COALESCE(end_loancust_cnt,0)-COALESCE (end_loaner_cnt,0)) <![CDATA[  <= ]]>
        0 then 0
        else (COALESCE(renewal_cnt,0.0000)-COALESCE(renewal_loaner_cnt,0.0000)-COALESCE
        (renewal_loancust_cnt,0.0000))/(COALESCE(end_cnt,0)-COALESCE(end_loancust_cnt,0)-COALESCE (end_loaner_cnt,0))
        end ) *100 as decimal (10,2)) as noLoanRenewalRate,
        COALESCE(person_accident_end,0) as personAccidentEnd,
        COALESCE(person_accident_renewal,0) as personAccidentRenewal,
        cast((case when COALESCE(person_accident_end,0) =0 then 0 else
        COALESCE(person_accident_renewal,0.0000)/COALESCE(person_accident_end,0) end)*100 AS decimal(10,2)) as
        personRenewalRate,
        COALESCE(medical_end,0) as medicalEnd,
        COALESCE(medical_renewal,0) as medicalRenewal,
        cast((case when COALESCE(medical_end,0) =0 then 0 else COALESCE(medical_renewal,0.0000)/COALESCE(medical_end,0)
        end)*100 AS decimal(10,2)) as medicalRenewalRate,
        COALESCE(group_accident_end,0) as groupAccidentEnd,
        COALESCE(group_accident_renewal,0) as groupAccidentRenewal,
        cast((case when COALESCE(group_accident_end,0) =0 then 0 else
        COALESCE(group_accident_renewal,0.0000)/COALESCE(group_accident_end,0) end)*100 AS decimal(10,2)) as
        groupAccidentRenewalRate,
        COALESCE(liability_end,0) as liabilityEnd,
        COALESCE(liability_renewal,0) as liabilityRenewal,
        cast((case when COALESCE(liability_end,0) =0 then 0 else
        COALESCE(liability_renewal,0.0000)/COALESCE(liability_end,0) end)*100 AS decimal(10,2)) as liabilityRenewalRate
        from rpt.insurance_renewal_bch_stat_dfp

        WHERE
        1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>
        <if test='endDate != null'>
            AND rpt_date=#{endDate}
        </if>
        <choose>
            <when test='recordType=="year"'>
                AND dtype='year'
            </when>
            <otherwise>
                AND dtype='month'
            </otherwise>
        </choose>
        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="listRenewalOrderBy"/>
    </select>

    <!--区域续保率汇总-->
    <select id="listRegionRenewalRate" resultType="com.cfpamf.ms.insur.report.pojo.vo.OrgRenewalRateVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        sum(COALESCE(end_cnt,0)) as endCnt,
        sum(COALESCE(renewal_cnt,0)) as renewalCnt,
        cast((case when sum(COALESCE(end_cnt,0))=0 then 0 else
        sum(COALESCE(renewal_cnt,0.0000))/sum(COALESCE(end_cnt,0)) end ) *100 as decimal (10,2)) as renewalRate,

        sum(COALESCE(start_amount,0)) as startAmount,
        sum(COALESCE(renewal_amount,0)) as renewalAmount,
        cast((case when sum(COALESCE(start_amount,0))=0 then 0 else
        sum(COALESCE(renewal_amount,0.0000))/sum(COALESCE(start_amount,0)) end ) *100 as decimal (10,2)) as
        renewalAmountRate,

        sum(COALESCE (end_loaner_cnt,0)) as endLoanerCnt,
        sum(COALESCE (renewal_loaner_cnt,0)) as renewalLoanerCnt,
        cast((case when sum(COALESCE(end_loaner_cnt,0))=0 then 0 else
        sum(COALESCE(renewal_loaner_cnt,0.0000))/sum(COALESCE(end_loaner_cnt,0)) end ) *100 as decimal (10,2)) as
        loanerRenewalRate,

        sum(COALESCE (end_loancust_cnt,0)) as endLoancustCnt,
        sum(COALESCE (renewal_loancust_cnt,0)) as renewalLoancustCnt,
        cast((case when sum(COALESCE(end_loancust_cnt,0))=0 then 0 else
        sum(COALESCE(renewal_loancust_cnt,0.0000))/sum(COALESCE(end_loancust_cnt,0)) end ) *100 as decimal (10,2)) as
        loancustRenewalRate,

        cast((case when (sum(COALESCE(end_cnt,0))-sum(COALESCE(end_loancust_cnt,0))-sum(COALESCE (end_loaner_cnt,0)))
        <![CDATA[  <= ]]> 0 then 0
        else (sum(COALESCE(renewal_cnt,0.0000))-sum(COALESCE(renewal_loaner_cnt,0.0000))-sum(COALESCE
        (renewal_loancust_cnt,0.0000)))/(sum(COALESCE(end_cnt,0))-sum(COALESCE(end_loancust_cnt,0))-sum(COALESCE
        (end_loaner_cnt,0))) end ) *100 as decimal (10,2)) as noLoanRenewalRate,
        sum(COALESCE(person_accident_end,0)) as personAccidentEnd,
        sum(COALESCE(person_accident_renewal,0)) as personAccidentRenewal,
        cast((case when sum(COALESCE(person_accident_end,0)) =0 then 0 else
        sum(COALESCE(person_accident_renewal,0.0000))/sum(COALESCE(person_accident_end,0)) end)*100 AS decimal(10,2)) as
        personRenewalRate,
        sum(COALESCE(medical_end,0)) as medicalEnd,
        sum(COALESCE(medical_renewal,0)) as medicalRenewal,
        cast((case when sum(COALESCE(medical_end,0)) =0 then 0 else
        sum(COALESCE(medical_renewal,0.0000))/sum(COALESCE(medical_end,0)) end)*100 AS decimal(10,2)) as
        medicalRenewalRate,
        sum(COALESCE(group_accident_end,0)) as groupAccidentEnd,
        sum(COALESCE(group_accident_renewal,0)) as groupAccidentRenewal,
        cast((case when sum(COALESCE(group_accident_end,0)) =0 then 0 else
        sum(COALESCE(group_accident_renewal,0.0000))/sum(COALESCE(group_accident_end,0)) end)*100 AS decimal(10,2)) as
        groupAccidentRenewalRate,
        sum(COALESCE(liability_end,0)) as liabilityEnd,
        sum(COALESCE(liability_renewal,0)) as liabilityRenewal,
        cast((case when sum(COALESCE(liability_end,0)) =0 then 0 else
        sum(COALESCE(liability_renewal,0.0000))/sum(COALESCE(liability_end,0)) end)*100 AS decimal(10,2)) as
        liabilityRenewalRate
        from rpt.insurance_renewal_bch_stat_dfp

        WHERE
        1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>
        <if test='endDate != null'>
            AND rpt_date=#{endDate}
        </if>
        <choose>
            <when test='recordType=="year"'>
                AND dtype='year'
            </when>
            <otherwise>
                AND dtype='month'
            </otherwise>
        </choose>
        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by area_name
        <include refid="listRenewalOrderBy"/>
    </select>


    <sql id="listRenewalOrderBy">
        ORDER BY
        <choose>
            <when test='sortType!=null and sortType=="renewalCnt"'>
                renewalCnt
            </when>
            <when test='sortType!=null and sortType=="renewalRate"'>
                renewalRate
            </when>
            <when test='sortType!=null and sortType=="renewalAmountRate"'>
                renewalAmountRate
            </when>
            <when test='sortType!=null and sortType=="loanerRenewalRate"'>
                loanerRenewalRate
            </when>
            <when test='sortType!=null and sortType=="loancustRenewalRate"'>
                loancustRenewalRate
            </when>
            <when test='sortType!=null and sortType=="noLoanRenewalRate"'>
                noLoanRenewalRate
            </when>
            <when test='sortType!=null and sortType=="personRenewalRate"'>
                personRenewalRate
            </when>
            <when test='sortType!=null and sortType=="medicalRenewalRate"'>
                medicalRenewalRate
            </when>
            <when test='sortType!=null and sortType=="groupAccidentRenewalRate"'>
                groupAccidentRenewalRate
            </when>
            <when test='sortType!=null and sortType=="liabilityRenewalRate"'>
                liabilityRenewalRate
            </when>
            <otherwise>
                startDate
            </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </sql>

    <!--续保率报表 end-->

    <!--个人推广费-->
    <select id="listPersonalPromoFee" resultType="com.cfpamf.ms.insur.report.pojo.vo.PersonalPromoFeeVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        bch_name as orgName,
        emp_name as userName,
        emp_id as userId,
        job_name as jobName,
        finance_org_name as financeOrgName,
        sum(COALESCE(qty,0)) as qty,
        sum(COALESCE(amount,0)) as amount,

        sum(COALESCE(commission_amount,0)) as promoFee
        from rpt.insurance_promfee_emp_mip
        where 1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE
            CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>
        <if test='financeOrgCode != null and financeOrgCode!=""'>
            AND finance_org_code = #{financeOrgCode,jdbcType=VARCHAR}
        </if>

        <if test='startDate != null'>
            AND rpt_date>=#{startDate}

        </if>

        <if test='endDate != null'>
            <![CDATA[  AND rpt_date<= #{endDate} ]]>
        </if>
        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by area_name ,bch_name,emp_name , emp_id ,job_name,finance_org_name
    </select>
    <!--个人推广费end-->

    <!--个人推广费2021-->
    <select id="searchPersonalPromoFee" resultType="com.cfpamf.ms.insur.report.pojo.vo.PersonalPromoFeeVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        bch_name as orgName,
        emp_name as userName,
        emp_id as userId,
        job_name as jobName,
        finance_org_name as financeOrgName,
        sum(COALESCE(qty,0)) as qty,
        sum(COALESCE(amount,0)) as amount,

        sum(COALESCE(commission_amount,0)) as promoFee
        from rpt.insurance_promfee_emp_dfp
        where 1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE
            CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>
        <if test='financeOrgCode != null and financeOrgCode!=""'>
            AND finance_org_code = #{financeOrgCode,jdbcType=VARCHAR}
        </if>
        <if test="localDateList != null">
            and rpt_date in
            <foreach collection="localDateList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by emp_id ,emp_name , area_name ,bch_name,job_name,finance_org_name

    </select>
    <!--个人推广费2021 end-->

    <!--机构绩效-->
    <select id="listOrgPromoFee" resultType="com.cfpamf.ms.insur.report.pojo.vo.OrgPromoFeeVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        bch_name as orgName,
        emp_name as userName,
        emp_id as userId,
        job_name as jobName,
        finance_org_name as financeOrgName,
        sum(COALESCE(qty,0)) as qty,
        sum(COALESCE(amount,0)) as amount,
        sum(COALESCE(reward_amount,0)) as promoFee
        from rpt.insurance_promfee_bch_mip
        where 1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE
            CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>
        <if test='financeOrgCode != null and financeOrgCode!=""'>
            AND finance_org_code = #{financeOrgCode,jdbcType=VARCHAR}
        </if>

        <if test='startDate != null'>
            AND rpt_date>=#{startDate}
        </if>

        <if test='endDate != null'>
            <![CDATA[  AND rpt_date<= #{endDate} ]]>
        </if>

        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by area_name ,bch_name,emp_name , emp_id ,job_name,finance_org_name
    </select>


    <!--业务排名 begin-->
    <select id="listOrgBusinessRank" resultType="com.cfpamf.ms.insur.report.pojo.vo.BusinessRankVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        bch_name as orgName,
        emp_name as userName,
        emp_id as userId,

        COALESCE(valid_insured_cnt,0) as validInsuredCnt,
        COALESCE(unloan_valid_insured_cnt,0) as unloanValidInsuredCnt,
        cast(COALESCE(unloan_valid_insured_per,0)*100 as decimal(10,2)) as unloanValidInsuredPer,
        cast(COALESCE(renewal_rate,0)*100 as decimal(10,2)) as renewalRate,
        COALESCE(renewal_rate_rank,0) as renewalRateRank,
        cadidation_rank as cadidationRank,
        COALESCE(insurance_cnt_monthly,0) as insuranceCntMonthly,
        COALESCE(insurance_amt_monthly,0) as insuranceAmtMonthly,
        COALESCE(valid_employee_cnt,0) as validEmployeeCnt,
        COALESCE(avg_insurance_amt_monthly,0) as avgInsuranceAmtMonthly,
        COALESCE(acm_avg_insurance_amt_monthly,0) as acmAvgInsuranceAmtMonthly,
        COALESCE(is_reach_renewal,0) as reachRenewal,
        COALESCE(is_reach_insurance_amt,0) as reachInsuranceAmt,
        cast(COALESCE(allowance_per,0)*100 as decimal(10,2)) as allowancePer


        from rpt.insurance_2021_kpi_rank_dfp
        where 1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE
            CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>

        <if test='endDate != null'>
            AND rpt_date = #{endDate}
        </if>

        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        ORDER BY
        <choose>
            <when test='sortType!=null and sortType=="unloanValidInsuredPer"'>
                unloanValidInsuredPer
            </when>
            <when test='sortType!=null and sortType=="renewalRate"'>
                renewalRate
            </when>
            <when test='sortType!=null and sortType=="avgInsuranceAmtMonthly"'>
                avgInsuranceAmtMonthly
            </when>
            <when test='sortType!=null and sortType=="acmAvgInsuranceAmtMonthly"'>
                acmAvgInsuranceAmtMonthly
            </when>
            <otherwise>
                area_name
            </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>

    </select>
    <!--业务排名 end-->
    <!--基础报表数据 begin-->
    <select id="getInsuranceOrderAllStat" resultType="com.cfpamf.ms.insur.report.pojo.dto.InsuranceOrderAllStatDTO">
        select rpt_date                        as rptDate,
               COALESCE(insured_cust_day, 0)   as insuredCustDay,
               COALESCE(insured_cnt_day, 0)    as insuredCntDay,
               COALESCE(insured_amt_day, 0)    as insuredAmtDay,
               COALESCE(insured_cust_month, 0) as insuredCustMonth,
               COALESCE(insured_cnt_month, 0)  as insuredCntMonth,
               COALESCE(insured_amt_month, 0)  as insuredAmtMonth,
               COALESCE(insured_cust_year, 0)  as insuredCustYear,
               COALESCE(insured_cnt_year, 0)   as insuredCntYear,
               COALESCE(insured_amt_year, 0)   as insuredAmtYear,
               COALESCE(insured_cust_all, 0)   as insuredCustAll,
               COALESCE(insured_cnt_all, 0)    as insuredCntAll,
               COALESCE(insured_amt_all, 0)    as insuredAmtAll
        from rpt.insurance_order_all_stat_dfp
        order by rpt_date desc limit 1;
    </select>
    <!--基础报表数据 end-->

    <!--    个人报表返查询 start-->
    <select id="searchPersonalPerformance" resultMap="personalPerformanceMap">
        select
        emp_name as name,
        emp_id as jobNumber,
        area_name as region,
        bch_name as organizationName,
        join_date as entryDate,
        job_name as jobName,
        COALESCE ( insured_cnt_day, 0 ) AS dayOrderQuantity,
        COALESCE ( insured_amt_day, 0 ) AS dayPremium,
        COALESCE ( insured_cnt_month, 0 ) AS monthOrderQuantity,
        COALESCE ( insured_amt_month, 0 ) AS monthPremium,
        COALESCE ( insured_cnt_year, 0 ) AS yearOrderQuantity,
        COALESCE ( insured_amt_year, 0 ) AS yearPremium,
        -- 非信贷客户相关
        COALESCE (non_loan_insured_amt_month,0) AS noLoanerMonthPremium,
        COALESCE (non_loan_insured_amt_year,0) AS noLoanerYearPremium,

        CAST ( ( CASE WHEN COALESCE (insured_amt_month , 0) = 0 THEN 0 ELSE COALESCE (non_loan_insured_amt_month,0.000)
        / insured_amt_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanermonthPremiumProportion,

        CAST ( ( CASE WHEN COALESCE (insured_amt_year , 0) = 0 THEN 0 ELSE COALESCE (non_loan_insured_amt_year,0.000) /
        insured_amt_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanerYearPremiumProportion,

        CAST ( ( CASE WHEN COALESCE (insured_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (non_loan_insured_cust_month,0.000) /
        insured_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanerCurrentMonthProportion,

        -- 续保率
        -- 当月续保率=当月续保人数/当月到期客户数
        CAST ( ( CASE WHEN COALESCE (end_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (stay_cust_month,0.000) /
        end_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS monthRenewInsuranceRateVo,
        -- 当年续保率=当年续保人数/当年到期客户数
        CAST ( ( CASE WHEN COALESCE (end_cust_year , 0) = 0 THEN 0 ELSE COALESCE
        (stay_cust_year,0.000) /
        end_cust_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS yearRenewInsuranceRateVo,
        -- 客户转化率
        -- 当月信贷客户转化率=当月保险且借款人数/当月借款人数
        CAST ( ( CASE WHEN COALESCE (loan_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (loan_insured_cust_month,0.000) /
        loan_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS loanerCustomerConversionRate,
        -- 当月信贷相关客户转化率=当月保险且信贷相关客户数/当月信贷相关客户数
        CAST ( ( CASE WHEN COALESCE (loan_relation_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (loan_relation_insured_cust_month ,0.000) /
        loan_relation_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS loanerRelevantCustomerConversionRate
        from rpt.insurance_order_emp_stat_dfp
        WHERE
        1=1
        <if test='organizationCode != null and organizationCode!=""'>
            AND bch_code = #{organizationCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='regionName != null and regionName!=""'>
            AND (area_code LIKE CONCAT(#{regionName,jdbcType=VARCHAR},'%') OR area_name LIKE
            CONCAT(#{regionName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE
            CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>

        <if test='singleDate != null'>
            AND rpt_date = #{singleDate}
        </if>

        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY
        <choose>
            <when test='sortType!=null and sortType=="dayOrderQuantity"'>
                insured_cnt_day
            </when>
            <when test='sortType!=null and sortType=="dayPremium"'>
                insured_amt_day
            </when>
            <when test='sortType!=null and sortType=="monthOrderQuantity"'>
                insured_cnt_month
            </when>
            <when test='sortType!=null and sortType=="monthPremium"'>
                insured_amt_month
            </when>
            <when test='sortType!=null and sortType=="yearOrderQuantity"'>
                insured_cnt_year
            </when>
            <when test='sortType!=null and sortType=="yearPremium"'>
                insured_amt_year
            </when>
            <otherwise>
                area_name
            </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>
    <!--    个人报表查询 end-->
    <!--    个人报表返回值映射 start-->
    <resultMap id="personalPerformanceMap" type="com.cfpamf.ms.insur.report.pojo.vo.personnel.PersonnelStatisticsVo">
        <result property="personnelVo.name" column="name"/>
        <result property="personnelVo.jobNumber" column="jobNumber"/>
        <result property="personnelVo.organizationName" column="organizationName"/>
        <result property="personnelVo.region" column="region"/>
        <result property="personnelVo.jobName" column="jobName"/>
        <result property="personnelVo.entryDate" column="entryDate"/>

        <result property="orderQuantityStatisticsVo.day" column="dayOrderQuantity"/>
        <result property="orderQuantityStatisticsVo.month" column="monthOrderQuantity"/>
        <result property="orderQuantityStatisticsVo.year" column="yearOrderQuantity"/>

        <result property="premiumStatisticsVo.day" column="dayPremium"/>
        <result property="premiumStatisticsVo.month" column="monthPremium"/>
        <result property="premiumStatisticsVo.year" column="yearPremium"/>

        <!--非信贷客户统计-->
        <result property="noLoanerStatisticsVo.currentMonthProportion" column="noLoanerCurrentMonthProportion"/>
        <result property="noLoanerStatisticsVo.monthPremium" column="noLoanerMonthPremium"/>
        <result property="noLoanerStatisticsVo.monthPremiumProportion" column="noLoanerMonthPremiumProportion"/>
        <result property="noLoanerStatisticsVo.yearPremium" column="noLoanerYearPremium"/>
        <result property="noLoanerStatisticsVo.yearPremiumProportion" column="noLoanerYearPremiumProportion"/>
        <!--        续保率-->
        <result property="renewInsuranceRateVo.month" column="monthRenewInsuranceRateVo"/>
        <result property="renewInsuranceRateVo.year" column="yearRenewInsuranceRateVo"/>
        <!--        客户转化率-->
        <result property="customerConversionRateVo.loaner" column="loanerCustomerConversionRate"/>
        <result property="customerConversionRateVo.loanerRelevant" column="loanerRelevantCustomerConversionRate"/>
    </resultMap>
    <!--    个人报表返回值映射 end-->

    <!--    机构报表查询 start-->
    <select id="searchBranchPerformance"
            resultMap="branchPerformanceMap">
        select
        area_name AS region,
        bch_name AS organizationName,
        COALESCE ( target_emp_cnt, 0 ) AS organizationStaffCount,
        COALESCE ( emp_cnt_month, 0 ) AS monthJobStaffCount,
        COALESCE ( target_amt_month, 0 ) AS currentMonthPremiumTarget,
        COALESCE ( target_amt_year, 0 ) AS currentYearPremiumTarget,
        COALESCE ( target_amt_months, 0 ) AS presentCurrentMonthPremiumTarget,
        COALESCE ( insured_cnt_day, 0 ) AS dayOrderQuantity,
        COALESCE ( insured_amt_day, 0 ) AS dayPremium,
        COALESCE ( insured_cnt_month, 0 ) AS monthOrderQuantity,
        COALESCE ( insured_amt_month, 0 ) AS monthPremium,
        COALESCE ( insured_cnt_year, 0 ) AS yearOrderQuantity,
        COALESCE ( insured_amt_year, 0 ) AS yearPremium,
        CAST ( ( CASE WHEN COALESCE ( emp_cnt_month, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_month, 0.000 ) /
        emp_cnt_month END ) AS DECIMAL ( 10, 2 ) ) AS currentMonthJobStaffPerCapitaPremium ,

        CAST ( ( CASE WHEN COALESCE ( emp_cnt_year, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        emp_cnt_year END ) AS DECIMAL ( 10, 2 ) ) AS currentYearJobStaffPerCapitaPremium ,

        CAST ( ( CASE WHEN COALESCE ( target_emp_cnt, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_month, 0.000 ) /
        target_emp_cnt END ) AS DECIMAL ( 10, 2 ) ) AS currentMonthOrganizationStaffPerCapitaPremium ,

        CAST ( ( CASE WHEN COALESCE ( target_emp_cnt, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        target_emp_cnt / extract(month from rpt_date) END ) AS DECIMAL ( 10, 2 ) ) AS
        currentYearOrganizationStaffPerCapitaPremium,

        CAST ( ( CASE WHEN COALESCE ( target_amt_month, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_month, 0.000 ) /
        target_amt_month END ) *100 AS DECIMAL ( 10, 2 ) ) AS currentMonthCompletionRate ,

        CAST ( ( CASE WHEN COALESCE ( target_amt_months, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        target_amt_months END ) *100 AS DECIMAL ( 10, 2 ) ) AS presentCurrentMonthCompletionRate ,

        CAST ( ( CASE WHEN COALESCE ( target_amt_year, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        target_amt_year END ) *100 AS DECIMAL ( 10, 2 ) ) AS currentYearCompletionRate,

        -- 非信贷客户相关
        COALESCE (non_loan_insured_amt_month,0) AS noLoanerMonthPremium,
        COALESCE (non_loan_insured_amt_year,0) AS noLoanerYearPremium,

        CAST ( ( CASE WHEN COALESCE (insured_amt_month , 0) = 0 THEN 0 ELSE COALESCE (non_loan_insured_amt_month,0.000)
        / insured_amt_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanermonthPremiumProportion,

        CAST ( ( CASE WHEN COALESCE (insured_amt_year , 0) = 0 THEN 0 ELSE COALESCE (non_loan_insured_amt_year,0.000) /
        insured_amt_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanerYearPremiumProportion,

        CAST ( ( CASE WHEN COALESCE (insured_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (non_loan_insured_cust_month,0.000) /
        insured_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanerCurrentMonthProportion,

        -- 续保率
        -- 当月续保率=当月续保人数/当月到期客户数
        CAST ( ( CASE WHEN COALESCE (end_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (stay_cust_month,0.000) /
        end_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS monthRenewInsuranceRateVo,
        -- 当年续保率=当年续保人数/当年到期客户数
        CAST ( ( CASE WHEN COALESCE (end_cust_year , 0) = 0 THEN 0 ELSE COALESCE
        (stay_cust_year,0.000) /
        end_cust_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS yearRenewInsuranceRateVo,
        -- 客户转化率
        -- 当月信贷客户转化率=当月保险且借款人数/当月借款人数
        CAST ( ( CASE WHEN COALESCE (loan_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (loan_insured_cust_month,0.000) /
        loan_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS loanerCustomerConversionRate,
        -- 当月信贷相关客户转化率=当月保险且信贷相关客户数/当月信贷相关客户数
        CAST ( ( CASE WHEN COALESCE (loan_relation_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (loan_relation_insured_cust_month ,0.000) /
        loan_relation_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS loanerRelevantCustomerConversionRate

        from rpt.insurance_order_bch_stat_dfp
        where 1=1
        <if test='organizationCode != null and organizationCode!=""'>
            AND bch_code = #{organizationCode,jdbcType=VARCHAR}
        </if>
        <if test='organizationName != null and organizationName!=""'>
            AND (bch_id LIKE CONCAT(#{organizationName,jdbcType=VARCHAR},'%') OR bch_name LIKE
            CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='singleDate != null'>
            AND rpt_date = #{singleDate}
        </if>
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='regionName != null and regionName!=""'>
            AND (area_code LIKE CONCAT(#{regionName,jdbcType=VARCHAR},'%') OR area_name LIKE
            CONCAT(#{regionName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY
        <choose>
            <when test='sortType!=null and sortType=="dayOrderQuantity"'>
                insured_cnt_day
            </when>
            <when test='sortType!=null and sortType=="dayPremium"'>
                insured_amt_day
            </when>
            <when test='sortType!=null and sortType=="monthOrderQuantity"'>
                insured_cnt_month
            </when>
            <when test='sortType!=null and sortType=="monthPremium"'>
                insured_amt_month
            </when>
            <when test='sortType!=null and sortType=="yearOrderQuantity"'>
                insured_cnt_year
            </when>
            <when test='sortType!=null and sortType=="yearPremium"'>
                insured_amt_year
            </when>
            <otherwise>
                area_name
            </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>
    <!--    机构报表查询 end-->

    <!--   机构报表查询映射 start-->
    <resultMap id="branchPerformanceMap" type="com.cfpamf.ms.insur.report.pojo.vo.branch.BranchStatisticsVo">
        <!--基础信息映射-->
        <result property="organizationName" column="organizationName"/>
        <result property="region" column="region"/>
        <!-- 员工人数映射-->
        <result property="staffCountVo.monthJobStaffCount" column="monthJobStaffCount"/>
        <result property="staffCountVo.organizationStaffCount" column="organizationStaffCount"/>
        <!--保费目标映射-->
        <result property="premiumTargetVo.currentMonth" column="currentMonthPremiumTarget"/>
        <result property="premiumTargetVo.currentYear" column="currentYearPremiumTarget"/>
        <result property="premiumTargetVo.presentCurrentMonth" column="presentCurrentMonthPremiumTarget"/>
        <!--月度人均保费-->
        <result property="perCapitaPremiumVo.currentMonthJobStaff" column="currentMonthJobStaffPerCapitaPremium"/>
        <result property="perCapitaPremiumVo.currentYearJobStaff" column="currentYearJobStaffPerCapitaPremium"/>
        <result property="perCapitaPremiumVo.currentMonthOrganizationStaff"
                column="currentMonthOrganizationStaffPerCapitaPremium"/>
        <result property="perCapitaPremiumVo.currentYearOrganizationStaff"
                column="currentYearOrganizationStaffPerCapitaPremium"/>
        <!--保费完成率-->
        <result property="completionRateVo.currentYear" column="currentYearCompletionRate"/>
        <result property="completionRateVo.currentMonth" column="currentMonthCompletionRate"/>
        <result property="completionRateVo.presentCurrentMonth" column="presentCurrentMonthCompletionRate"/>

        <!-- 单量映射-->
        <result property="orderQuantityStatisticsVo.day" column="dayOrderQuantity"/>
        <result property="orderQuantityStatisticsVo.month" column="monthOrderQuantity"/>
        <result property="orderQuantityStatisticsVo.year" column="yearOrderQuantity"/>
        <!--保费映射-->
        <result property="premiumStatisticsVo.day" column="dayPremium"/>
        <result property="premiumStatisticsVo.month" column="monthPremium"/>
        <result property="premiumStatisticsVo.year" column="yearPremium"/>

        <!--非信贷客户统计-->
        <result property="noLoanerStatisticsVo.currentMonthProportion" column="noLoanerCurrentMonthProportion"/>
        <result property="noLoanerStatisticsVo.monthPremium" column="noLoanerMonthPremium"/>
        <result property="noLoanerStatisticsVo.monthPremiumProportion" column="noLoanerMonthPremiumProportion"/>
        <result property="noLoanerStatisticsVo.yearPremium" column="noLoanerYearPremium"/>
        <result property="noLoanerStatisticsVo.yearPremiumProportion" column="noLoanerYearPremiumProportion"/>

        <!--        续保率-->
        <result property="renewInsuranceRateVo.month" column="monthRenewInsuranceRateVo"/>
        <result property="renewInsuranceRateVo.year" column="yearRenewInsuranceRateVo"/>
        <!--        客户转化率-->
        <result property="customerConversionRateVo.loaner" column="loanerCustomerConversionRate"/>
        <result property="customerConversionRateVo.loanerRelevant" column="loanerRelevantCustomerConversionRate"/>
    </resultMap>
    <!--    机构报表查询映射 end-->


    <!--机构绩效-->
    <select id="listOrgCommission" resultType="com.cfpamf.ms.insur.report.pojo.vo.SmOrgCommissionReportVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        bch_name as orgName,
        sum(COALESCE(qty,0)) as insuredCnt,
        sum(COALESCE(amount,0)) as insuredAmt,
        sum(COALESCE(commission_amount,0)) as cmsnExpe,
        sum(COALESCE(settlement_amount,0)) as cmsnIncome
        from rpt.insurance_promfee_bch_mip
        where 1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE
            CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>

        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>

        <if test='startDate != null'>
            AND rpt_date>=#{startDate}
        </if>


        <if test='endDate != null'>
            <![CDATA[  AND rpt_date<= #{endDate} ]]>
        </if>

        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by area_name ,bch_name
    </select>

    <!--查询区域明细统计 start-->
    <select id="searchRegionPerformance"
            resultMap="regionPerformanceMap">
        select
        area_name AS region,
        COALESCE ( target_emp_cnt, 0 ) AS organizationStaffCount,
        COALESCE ( emp_cnt_month, 0 ) AS monthJobStaffCount,
        COALESCE ( target_amt_month, 0 ) AS currentMonthPremiumTarget,
        COALESCE ( target_amt_year, 0 ) AS currentYearPremiumTarget,
        COALESCE ( target_amt_months, 0 ) AS presentCurrentMonthPremiumTarget,
        COALESCE ( insured_cnt_day, 0 ) AS dayOrderQuantity,
        COALESCE ( insured_amt_day, 0 ) AS dayPremium,
        COALESCE ( insured_cnt_month, 0 ) AS monthOrderQuantity,
        COALESCE ( insured_amt_month, 0 ) AS monthPremium,
        COALESCE ( insured_cnt_year, 0 ) AS yearOrderQuantity,
        COALESCE ( insured_amt_year, 0 ) AS yearPremium,
        -- 员工人均保费
        CAST ( ( CASE WHEN COALESCE ( emp_cnt_month, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_month, 0.000 ) /
        emp_cnt_month END ) AS DECIMAL ( 10, 2 ) ) AS currentMonthJobStaffPerCapitaPremium ,

        CAST ( ( CASE WHEN COALESCE ( emp_cnt_year, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        emp_cnt_year END ) AS DECIMAL ( 10, 2 ) ) AS currentYearJobStaffPerCapitaPremium
        ,

        CAST ( ( CASE WHEN COALESCE ( target_emp_cnt, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_month, 0.000 ) /
        target_emp_cnt END ) AS DECIMAL ( 10, 2 ) ) AS currentMonthOrganizationStaffPerCapitaPremium ,

        CAST ( ( CASE WHEN COALESCE ( target_emp_cnt, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        target_emp_cnt / extract(month from rpt_date) END ) AS DECIMAL ( 10, 2 ) ) AS
        currentYearOrganizationStaffPerCapitaPremium,

        CAST ( ( CASE WHEN COALESCE ( target_amt_month, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_month, 0.000 ) /
        target_amt_month END ) *100 AS DECIMAL ( 10, 2 ) ) AS currentMonthCompletionRate ,

        CAST ( ( CASE WHEN COALESCE ( target_amt_months, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        target_amt_months END ) *100 AS DECIMAL ( 10, 2 ) ) AS presentCurrentMonthCompletionRate ,

        CAST ( ( CASE WHEN COALESCE ( target_amt_year, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        target_amt_year END ) *100 AS DECIMAL ( 10, 2 ) ) AS currentYearCompletionRate,

        -- 非信贷客户相关
        COALESCE (non_loan_insured_amt_month,0) AS noLoanerMonthPremium,
        COALESCE (non_loan_insured_amt_year,0) AS noLoanerYearPremium,

        CAST ( ( CASE WHEN COALESCE (insured_amt_month , 0) = 0 THEN 0 ELSE COALESCE (non_loan_insured_amt_month,0.000)
        / insured_amt_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanermonthPremiumProportion,

        CAST ( ( CASE WHEN COALESCE (insured_amt_year , 0) = 0 THEN 0 ELSE COALESCE (non_loan_insured_amt_year,0.000) /
        insured_amt_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanerYearPremiumProportion,

        CAST ( ( CASE WHEN COALESCE (insured_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (non_loan_insured_cust_month,0.000) /
        insured_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanerCurrentMonthProportion,
        -- 放款金额相关
        COALESCE ( loan_amt_month, 0 ) AS currentMonthLoanAmount,
        COALESCE ( loan_amt_year, 0 ) AS currentYearLoanAmount,
        COALESCE ( offline_loan_amt_month, 0 ) AS currentMonthOfflineLoanAmount,
        COALESCE ( offline_loan_amt_year, 0 ) AS currentYearOfflineLoanAmount,

        CAST ( ( CASE WHEN COALESCE (offline_loan_amt_month , 0) = 0 THEN 0 ELSE COALESCE
        (insured_amt_month,0.000) /
        offline_loan_amt_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS currentMonthOfflinePremiumProportionLoanAmount,

        CAST ( ( CASE WHEN COALESCE (offline_loan_amt_year , 0) = 0 THEN 0 ELSE COALESCE
        (insured_amt_year,0.000) /
        offline_loan_amt_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS currentYearOfflinePremiumProportionLoanAmount,

        CAST ( ( CASE WHEN COALESCE (loan_amt_month , 0) = 0 THEN 0 ELSE COALESCE
        (insured_amt_month,0.000) /
        loan_amt_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS currentMonthPremiumProportionLoanAmount,

        CAST ( ( CASE WHEN COALESCE (loan_amt_year , 0) = 0 THEN 0 ELSE COALESCE
        (insured_amt_year,0.000) /
        loan_amt_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS currentYearPremiumProportionLoanAmount,

        -- 续保率
        -- 当月续保率=当月续保人数/当月到期客户数
        CAST ( ( CASE WHEN COALESCE (end_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (stay_cust_month,0.000) /
        end_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS monthRenewInsuranceRateVo,
        -- 当年续保率=当年续保人数/当年到期客户数
        CAST ( ( CASE WHEN COALESCE (end_cust_year , 0) = 0 THEN 0 ELSE COALESCE
        (stay_cust_year,0.000) /
        end_cust_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS yearRenewInsuranceRateVo,
        -- 客户转化率
        -- 当月信贷客户转化率=当月保险且借款人数/当月借款人数
        CAST ( ( CASE WHEN COALESCE (loan_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (loan_insured_cust_month,0.000) /
        loan_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS loanerCustomerConversionRate,
        -- 当月信贷相关客户转化率=当月保险且信贷相关客户数/当月信贷相关客户数
        CAST ( ( CASE WHEN COALESCE (loan_relation_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (loan_relation_insured_cust_month ,0.000) /
        loan_relation_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS loanerRelevantCustomerConversionRate

        from rpt.insurance_order_area_stat_dfp

        where 1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='regionName != null and regionName!=""'>
            AND (area_code LIKE CONCAT(#{regionName,jdbcType=VARCHAR},'%') OR area_name LIKE
            CONCAT(#{regionName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='singleDate != null'>
            AND rpt_date = #{singleDate}
        </if>
        ORDER BY
        <choose>
            <when test='sortType!=null and sortType=="dayOrderQuantity"'>
                insured_cnt_day
            </when>
            <when test='sortType!=null and sortType=="dayPremium"'>
                insured_amt_day
            </when>
            <when test='sortType!=null and sortType=="monthOrderQuantity"'>
                insured_cnt_month
            </when>
            <when test='sortType!=null and sortType=="monthPremium"'>
                insured_amt_month
            </when>
            <when test='sortType!=null and sortType=="yearOrderQuantity"'>
                insured_cnt_year
            </when>
            <when test='sortType!=null and sortType=="yearPremium"'>
                insured_amt_year
            </when>
            <otherwise>
                area_name
            </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>

    <!--查询区域明细统计 end-->

    <!--   区域报表查询映射 start-->
    <resultMap id="regionPerformanceMap" type="com.cfpamf.ms.insur.report.pojo.vo.region.RegionStatisticsVo">
        <!--基础信息映射-->
        <result property="region" column="region"/>
        <!-- 员工人数映射-->
        <result property="staffCountVo.monthJobStaffCount" column="monthJobStaffCount"/>
        <result property="staffCountVo.organizationStaffCount" column="organizationStaffCount"/>
        <!--保费目标映射-->
        <result property="premiumTargetVo.currentMonth" column="currentMonthPremiumTarget"/>
        <result property="premiumTargetVo.currentYear" column="currentYearPremiumTarget"/>
        <result property="premiumTargetVo.presentCurrentMonth" column="presentCurrentMonthPremiumTarget"/>
        <!--月度人均保费-->
        <result property="perCapitaPremiumVo.currentMonthJobStaff" column="currentMonthJobStaffPerCapitaPremium"/>
        <result property="perCapitaPremiumVo.currentYearJobStaff" column="currentYearJobStaffPerCapitaPremium"/>
        <result property="perCapitaPremiumVo.currentMonthOrganizationStaff"
                column="currentMonthOrganizationStaffPerCapitaPremium"/>
        <result property="perCapitaPremiumVo.currentYearOrganizationStaff"
                column="currentYearOrganizationStaffPerCapitaPremium"/>
        <!--保费完成率-->
        <result property="completionRateVo.currentYear" column="currentYearCompletionRate"/>
        <result property="completionRateVo.currentMonth" column="currentMonthCompletionRate"/>
        <result property="completionRateVo.presentCurrentMonth" column="presentCurrentMonthCompletionRate"/>
        <!-- 单量映射-->
        <result property="orderQuantityStatisticsVo.day" column="dayOrderQuantity"/>
        <result property="orderQuantityStatisticsVo.month" column="monthOrderQuantity"/>
        <result property="orderQuantityStatisticsVo.year" column="yearOrderQuantity"/>
        <!--保费映射-->
        <result property="premiumStatisticsVo.day" column="dayPremium"/>
        <result property="premiumStatisticsVo.month" column="monthPremium"/>
        <result property="premiumStatisticsVo.year" column="yearPremium"/>
        <!--非信贷客户统计-->
        <result property="noLoanerStatisticsVo.currentMonthProportion" column="noLoanerCurrentMonthProportion"/>
        <result property="noLoanerStatisticsVo.monthPremium" column="noLoanerMonthPremium"/>
        <result property="noLoanerStatisticsVo.monthPremiumProportion" column="noLoanerMonthPremiumProportion"/>
        <result property="noLoanerStatisticsVo.yearPremium" column="noLoanerYearPremium"/>
        <result property="noLoanerStatisticsVo.yearPremiumProportion" column="noLoanerYearPremiumProportion"/>

        <!--    放款金额    -->
        <result property="loanAmountVo.currentMonth" column="currentMonthLoanAmount"/>
        <result property="loanAmountVo.currentYear" column="currentYearLoanAmount"/>
        <result property="loanAmountVo.currentMonthOffline" column="currentMonthOfflineLoanAmount"/>
        <result property="loanAmountVo.currentYearOffline" column="currentYearOfflineLoanAmount"/>
        <result property="loanAmountVo.currentMonthOfflinePremiumProportion"
                column="currentMonthOfflinePremiumProportionLoanAmount"/>
        <result property="loanAmountVo.currentMonthPremiumProportion" column="currentMonthPremiumProportionLoanAmount"/>
        <result property="loanAmountVo.currentYearOfflinePremiumProportion"
                column="currentYearOfflinePremiumProportionLoanAmount"/>
        <result property="loanAmountVo.currentYearPremiumProportion" column="currentYearPremiumProportionLoanAmount"/>
        <!--        续保率-->
        <result property="renewInsuranceRateVo.month" column="monthRenewInsuranceRateVo"/>
        <result property="renewInsuranceRateVo.year" column="yearRenewInsuranceRateVo"/>
        <!--        客户转化率-->
        <result property="customerConversionRateVo.loaner" column="loanerCustomerConversionRate"/>
        <result property="customerConversionRateVo.loanerRelevant" column="loanerRelevantCustomerConversionRate"/>
    </resultMap>
    <!--    区域报表查询映射 end-->

    <!--    合计业绩明细报表查询 start-->
    <select id="searchTotalPerformance" resultMap="totalPerformanceMap">
        select
        COALESCE ( target_emp_cnt, 0 ) AS organizationStaffCount,
        COALESCE ( emp_cnt_month, 0 ) AS monthJobStaffCount,
        COALESCE ( insured_cnt_day, 0 ) AS dayOrderQuantity,
        COALESCE ( insured_amt_day, 0 ) AS dayPremium,
        COALESCE ( insured_cnt_month, 0 ) AS monthOrderQuantity,
        COALESCE ( insured_amt_month, 0 ) AS monthPremium,
        COALESCE ( insured_cnt_year, 0 ) AS yearOrderQuantity,
        COALESCE ( insured_amt_year, 0 ) AS yearPremium,
        -- 保费目标
        COALESCE ( target_amt_month, 0 ) AS currentMonthPremiumTarget,
        COALESCE ( target_amt_year, 0 ) AS currentYearPremiumTarget,
        COALESCE ( target_amt_months, 0 ) AS presentCurrentMonthPremiumTarget,
        -- 员工人均保费
        CAST ( ( CASE WHEN COALESCE ( emp_cnt_month, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_month, 0.000 ) /
        emp_cnt_month END ) AS DECIMAL ( 10, 2 ) ) AS currentMonthJobStaffPerCapitaPremium ,

        CAST ( ( CASE WHEN COALESCE ( emp_cnt_year, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        emp_cnt_year END ) AS DECIMAL ( 10, 2 ) ) AS currentYearJobStaffPerCapitaPremium ,

        CAST ( ( CASE WHEN COALESCE ( target_emp_cnt, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_month, 0.000 ) /
        target_emp_cnt END ) AS DECIMAL ( 10, 2 ) ) AS currentMonthOrganizationStaffPerCapitaPremium ,

        CAST ( ( CASE WHEN COALESCE ( target_emp_cnt, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        target_emp_cnt / extract(month from rpt_date) END ) AS DECIMAL ( 10, 2 ) ) AS
        currentYearOrganizationStaffPerCapitaPremium,
        -- 保费目标完成率
        CAST ( ( CASE WHEN COALESCE ( target_amt_month, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_month, 0.000 ) /
        target_amt_month END ) *100 AS DECIMAL ( 10, 2 ) ) AS currentMonthCompletionRate ,

        CAST ( ( CASE WHEN COALESCE ( target_amt_months, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        target_amt_months END ) *100 AS DECIMAL ( 10, 2 ) ) AS presentCurrentMonthCompletionRate ,

        CAST ( ( CASE WHEN COALESCE ( target_amt_year, 0 ) = 0 THEN 0 ELSE COALESCE ( insured_amt_year, 0.000 ) /
        target_amt_year END ) *100 AS DECIMAL ( 10, 2 ) ) AS currentYearCompletionRate,
        -- 非信贷客户相关
        COALESCE (non_loan_insured_amt_month,0) AS noLoanerMonthPremium,
        COALESCE (non_loan_insured_amt_year,0) AS noLoanerYearPremium,

        CAST ( ( CASE WHEN COALESCE (insured_amt_month , 0) = 0 THEN 0 ELSE COALESCE (non_loan_insured_amt_month,0.000)
        / insured_amt_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanermonthPremiumProportion,

        CAST ( ( CASE WHEN COALESCE (insured_amt_year , 0) = 0 THEN 0 ELSE COALESCE (non_loan_insured_amt_year,0.000) /
        insured_amt_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanerYearPremiumProportion,

        CAST ( ( CASE WHEN COALESCE (insured_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (non_loan_insured_cust_month,0.000) /
        insured_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS noLoanerCurrentMonthProportion,
        -- 放款金额相关
        COALESCE ( loan_amt_month, 0 ) AS currentMonthLoanAmount,
        COALESCE ( loan_amt_year, 0 ) AS currentYearLoanAmount,
        COALESCE ( offline_loan_amt_month, 0 ) AS currentMonthOfflineLoanAmount,
        COALESCE ( offline_loan_amt_year, 0 ) AS currentYearOfflineLoanAmount,

        CAST ( ( CASE WHEN COALESCE (offline_loan_amt_month , 0) = 0 THEN 0 ELSE COALESCE
        (insured_amt_month,0.000) /
        offline_loan_amt_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS currentMonthOfflinePremiumProportionLoanAmount,

        CAST ( ( CASE WHEN COALESCE (offline_loan_amt_year , 0) = 0 THEN 0 ELSE COALESCE
        (insured_amt_year,0.000) /
        offline_loan_amt_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS currentYearOfflinePremiumProportionLoanAmount,

        CAST ( ( CASE WHEN COALESCE (loan_amt_month , 0) = 0 THEN 0 ELSE COALESCE
        (insured_amt_month,0.000) /
        loan_amt_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS currentMonthPremiumProportionLoanAmount,

        CAST ( ( CASE WHEN COALESCE (loan_amt_year , 0) = 0 THEN 0 ELSE COALESCE
        (insured_amt_year,0.000) /
        loan_amt_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS currentYearPremiumProportionLoanAmount,

        -- 续保率
        -- 当月续保率=当月续保人数/当月到期客户数
        CAST ( ( CASE WHEN COALESCE (end_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (stay_cust_month,0.000) /
        end_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS monthRenewInsuranceRateVo,

        -- 当年续保率=当年续保人数/当年到期客户数
        CAST ( ( CASE WHEN COALESCE (end_cust_year , 0) = 0 THEN 0 ELSE COALESCE
        (stay_cust_year,0.000) /
        end_cust_year END ) * 100 AS DECIMAL ( 10, 2 ) ) AS yearRenewInsuranceRateVo,

        -- 客户转化率
        -- 当月信贷客户转化率=当月保险且借款人数/当月借款人数
        CAST ( ( CASE WHEN COALESCE (loan_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (loan_insured_cust_month,0.000) /
        loan_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS loanerCustomerConversionRate,

        -- 当月信贷相关客户转化率=当月保险且信贷相关客户数/当月信贷相关客户数
        CAST ( ( CASE WHEN COALESCE (loan_relation_cust_month , 0) = 0 THEN 0 ELSE COALESCE
        (loan_relation_insured_cust_month ,0.000) /
        loan_relation_cust_month END ) * 100 AS DECIMAL ( 10, 2 ) ) AS loanerRelevantCustomerConversionRate

        from rpt.insurance_order_all_stat_dfp
        WHERE
        1=1
        <if test='singleDate != null'>
            AND rpt_date = #{singleDate}
        </if>
        ORDER BY
        <choose>
            <when test='sortType!=null and sortType=="dayOrderQuantity"'>
                insured_cnt_day
            </when>
            <when test='sortType!=null and sortType=="dayPremium"'>
                insured_amt_day
            </when>
            <when test='sortType!=null and sortType=="monthOrderQuantity"'>
                insured_cnt_month
            </when>
            <when test='sortType!=null and sortType=="monthPremium"'>
                insured_amt_month
            </when>
            <when test='sortType!=null and sortType=="yearOrderQuantity"'>
                insured_cnt_year
            </when>
            <when test='sortType!=null and sortType=="yearPremium"'>
                insured_amt_year
            </when>
            <otherwise>
                insured_cnt_day
            </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>

    <!--    合计业绩明细报表查询 end-->

    <!--    合计业绩明细报表查询映射 start-->
    <resultMap id="totalPerformanceMap" type="com.cfpamf.ms.insur.report.pojo.vo.total.TotalStatisticsVo">
        <!-- 员工人数映射-->
        <result property="staffCountVo.monthJobStaffCount" column="monthJobStaffCount"/>
        <result property="staffCountVo.organizationStaffCount" column="organizationStaffCount"/>
        <!--保费目标映射-->
        <result property="premiumTargetVo.currentMonth" column="currentMonthPremiumTarget"/>
        <result property="premiumTargetVo.currentYear" column="currentYearPremiumTarget"/>
        <result property="premiumTargetVo.presentCurrentMonth" column="presentCurrentMonthPremiumTarget"/>
        <!-- 单量映射-->
        <result property="orderQuantityStatisticsVo.day" column="dayOrderQuantity"/>
        <result property="orderQuantityStatisticsVo.month" column="monthOrderQuantity"/>
        <result property="orderQuantityStatisticsVo.year" column="yearOrderQuantity"/>
        <!--保费映射-->
        <result property="premiumStatisticsVo.day" column="dayPremium"/>
        <result property="premiumStatisticsVo.month" column="monthPremium"/>
        <result property="premiumStatisticsVo.year" column="yearPremium"/>
        <!--月度人均保费-->
        <result property="perCapitaPremiumVo.currentMonthJobStaff" column="currentMonthJobStaffPerCapitaPremium"/>
        <result property="perCapitaPremiumVo.currentYearJobStaff" column="currentYearJobStaffPerCapitaPremium"/>
        <result property="perCapitaPremiumVo.currentMonthOrganizationStaff"
                column="currentMonthOrganizationStaffPerCapitaPremium"/>
        <result property="perCapitaPremiumVo.currentYearOrganizationStaff"
                column="currentYearOrganizationStaffPerCapitaPremium"/>
        <!--保费完成率-->
        <result property="completionRateVo.currentYear" column="currentYearCompletionRate"/>
        <result property="completionRateVo.currentMonth" column="currentMonthCompletionRate"/>
        <result property="completionRateVo.presentCurrentMonth" column="presentCurrentMonthCompletionRate"/>
        <!--非信贷客户统计-->
        <result property="noLoanerStatisticsVo.currentMonthProportion" column="noLoanerCurrentMonthProportion"/>
        <result property="noLoanerStatisticsVo.monthPremium" column="noLoanerMonthPremium"/>
        <result property="noLoanerStatisticsVo.monthPremiumProportion" column="noLoanerMonthPremiumProportion"/>
        <result property="noLoanerStatisticsVo.yearPremium" column="noLoanerYearPremium"/>
        <result property="noLoanerStatisticsVo.yearPremiumProportion" column="noLoanerYearPremiumProportion"/>

        <!--    放款金额    -->
        <result property="loanAmountVo.currentMonth" column="currentMonthLoanAmount"/>
        <result property="loanAmountVo.currentYear" column="currentYearLoanAmount"/>
        <result property="loanAmountVo.currentMonthOffline" column="currentMonthOfflineLoanAmount"/>
        <result property="loanAmountVo.currentYearOffline" column="currentYearOfflineLoanAmount"/>
        <result property="loanAmountVo.currentMonthOfflinePremiumProportion"
                column="currentMonthOfflinePremiumProportionLoanAmount"/>
        <result property="loanAmountVo.currentMonthPremiumProportion" column="currentMonthPremiumProportionLoanAmount"/>
        <result property="loanAmountVo.currentYearOfflinePremiumProportion"
                column="currentYearOfflinePremiumProportionLoanAmount"/>
        <result property="loanAmountVo.currentYearPremiumProportion" column="currentYearPremiumProportionLoanAmount"/>
        <!--        续保率-->
        <result property="renewInsuranceRateVo.month" column="monthRenewInsuranceRateVo"/>
        <result property="renewInsuranceRateVo.year" column="yearRenewInsuranceRateVo"/>
        <!--        客户转化率-->
        <result property="customerConversionRateVo.loaner" column="loanerCustomerConversionRate"/>
        <result property="customerConversionRateVo.loanerRelevant" column="loanerRelevantCustomerConversionRate"/>
    </resultMap>
    <!--    合计业绩明细报表查询映射 end-->
    <!--    个人转换率统计 start-->
    <select id="searchPersonnelTransferRate"
            resultType="com.cfpamf.ms.insur.report.pojo.vo.personnel.PersonnelTransferRateVo">
        SELECT
        usr_cde AS jobNumber,
        area_name AS regionName,
        bch_name AS orgName,
        usr_name AS recommendUserName,
        SUM(insured_count) AS safesPerQty,
        SUM(loaner_count) AS loanPerQty,
        SUM( offline_loaner_count) AS safesBorrowerPerQty,
        SUM( offline_shareloan_count) AS safesCoBorrowerPerQty,
        SUM( offline_guarantor_count) AS safesGuarantorPerQty,
        SUM(loan_rel_count) AS safesRelevanterPerQty,

        SUM(no_loaner_count) AS safesNoLoanPerQty,
        CAST ((CASE WHEN SUM(loan_rel_count)=0 THEN 0 ELSE
        SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(loan_rel_count) END) *100 as decimal (10,2))
        AS safesOtherTransferRatio,

        CAST ( (CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(no_loaner_count)*1.0/SUM(insured_count) END) *100 as
        decimal (10,2))
        AS safesNoLoanRatio,

        CAST ((CASE WHEN SUM(loaner_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(loaner_count) END)*100 as
        decimal (10,2))
        AS safesBorrowerTransferRatio,

        CAST (( CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(insured_count) END) *100 as
        decimal (10,2))
        AS safesBorrowerRatio,
        CAST ((CASE WHEN SUM(insured_count)=0 THEN 0 ELSE
        SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(insured_count) END) *100 as decimal (10,2))
        AS safesLoanRatio
        FROM rpt.insured_loan_trans_static_monthly
        WHERE usr_cde != 'Total'
        AND area_cde != 'Total'
        AND district_cde != 'Total'
        <if test='startDate != null'>
            AND rpt_date >= #{startDate}
        </if>
        <if test="endDate!=null">
            <![CDATA[ AND rpt_date <= #{endDate}    ]]>
        </if>
        <if test='userName != null and userName!=""'>
            AND (usr_cde LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR usr_name LIKE
            CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND usr_cde = #{userId,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode!=""'>
            AND area_cde = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_cde = #{orgCode,jdbcType=VARCHAR}
        </if>
        GROUP BY usr_cde,area_name,bch_name,usr_name
        ORDER BY area_name ASC
    </select>
    <!--    个人转换率统计 end-->

</mapper>
