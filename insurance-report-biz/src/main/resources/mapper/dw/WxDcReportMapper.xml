<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.report.dao.dw.WxDcReportMapper">
    <select id="listWxPersonalBusinessDetails" resultType="com.cfpamf.ms.insur.report.pojo.vo.WxPersonalBusinessVO">
        select to_char(t1.startDate, 'yyyy-mm-dd') as startDate,
        to_char(t1.endDate, 'yyyy-mm-dd') as endDate,
        t1.regionName,t1.orgName,t1.userName,t1.userId,t1.insuredCnt,t1.insuredAmt,
        COALESCE(t2.end_cnt,0) as endCnt,
        COALESCE(t2.renewal_cnt,0) as renewalCnt,
        cast((case when COALESCE(t2.end_cnt,0)=0 then 0 else COALESCE(t2.renewal_cnt,0.0000)/COALESCE(t2.end_cnt,0) end ) *100 as decimal (10,2))  as renewalRate

        from
        (select
        to_date(#{startDate},'yyyy-mm-dd')as startDate,
        to_date(#{endDate},'yyyy-mm-dd')as endDate,
        area_code as regionCode,
        area_name as regionName,
        bch_code as orgCode,
        bch_name as orgName,
        emp_name as userName,
        emp_id as userId,
        sum(COALESCE(insured_cnt ,0))as insuredCnt,
        sum(COALESCE(insured_amt,0)) as insuredAmt
        from rpt.insurance_order_emp_stat_dip
        WHERE
        1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>

        <if test='zoneCode != null and zoneCode!=""'>
            AND district_code = #{zoneCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>

        <if test='startDate != null'>
            AND rpt_date>=#{startDate}

        </if>

        <if test='endDate != null'>
            <![CDATA[  AND rpt_date<= #{endDate} ]]>
        </if>
        group by area_code,area_name,bch_code,bch_name,emp_id,emp_name) t1
        left join  rpt.insurance_renewal_emp_stat_dfp t2 on t1.regionCode = t2.area_code and t1.orgCode=t2.bch_code and
        t1.userId = t2.emp_id and t1.endDate = t2.rpt_date
        where t2.dtype='month'
        ORDER BY
        <choose>
        <when test='sortType!=null and sortType=="insuredCnt"'>
            t1.insuredCnt
        </when>
        <when test='sortType!=null and sortType=="insuredAmt"'>
            t1.insuredAmt
        </when>
        <when test='sortType!=null and sortType=="endCnt"'>
            endCnt
        </when>
        <when test='sortType!=null and sortType=="renewalCnt"'>
            renewalCnt
        </when>
        <when test='sortType!=null and sortType=="renewalRate"'>
            renewalRate
        </when>
        <otherwise>
            startDate
        </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>


    </select>
    <select id="listWxOrgBusinessDetails" resultType="com.cfpamf.ms.insur.report.pojo.vo.WxOrgBusinessVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        bch_name as orgName,
        COALESCE(insurance_cnt_monthly,0) as insuredCnt,-- 单量
        COALESCE(insurance_amt_monthly,0) as insuredAmt,-- 保费
        0  as achievementRate,-- 达成率
        cast(COALESCE(unloan_valid_insured_per,0)*100 as decimal(10,2)) as unloanValidInsuredPer,-- 非信贷及相关客户占比
        cast(COALESCE(renewal_rate,0)*100 as decimal(10,2)) as renewalRate,-- 续保率
        COALESCE(renewal_rate_rank,0) as renewalRateRank,-- 续保率排名
        cadidation_rank as cadidationRank,-- 入围排名
        COALESCE(avg_insurance_amt_monthly,0) as avgInsuranceAmtMonthly,-- 人均保费
        0 as planEmpAverageAmt,-- 编制人均保费
        COALESCE(acm_avg_insurance_amt_monthly,0) as acmAvgInsuranceAmtMonthly -- 累计月度人均保费


        from rpt.insurance_2021_kpi_rank_dfp
        where 1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>

        <if test='endDate != null'>
            AND rpt_date = #{endDate}
        </if>

        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        ORDER BY
        <choose>
            <when test='sortType!=null and sortType=="insuredCnt"'>
                insuredCnt
            </when>
            <when test='sortType!=null and sortType=="insuredAmt"'>
                insuredAmt
            </when>
            <when test='sortType!=null and sortType=="achievementRate"'>
                achievementRate
            </when>
            <when test='sortType!=null and sortType=="renewalRate"'>
                renewalRate
            </when>
            <when test='sortType!=null and sortType=="avgInsuranceAmtMonthly"'>
                avgInsuranceAmtMonthly
            </when>
            <when test='sortType!=null and sortType=="acmAvgInsuranceAmtMonthly"'>
                acmAvgInsuranceAmtMonthly
            </when>
            <otherwise>
                area_name
            </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>
    <select id="listWxRegionBusinessDetails" resultType="com.cfpamf.ms.insur.report.pojo.vo.WxRegionBusinessVO">
        select to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate,
        to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate,
        area_name as regionName,
        sum(COALESCE(insurance_cnt_monthly,0)) as insuredCnt,-- 单量
        sum(COALESCE(insurance_amt_monthly,0)) as insuredAmt,-- 保费
        0  as achievementRate,-- 达成率
        sum(COALESCE(renewal_cnt,0)) as renewalCnt,-- 续保客户数
        sum(COALESCE(end_cnt,0)) as endCnt,-- 到期客户数
        cast((case when sum(COALESCE(end_cnt,0))=0 then 0
        else sum(COALESCE(renewal_cnt,0.0000))/sum(COALESCE(end_cnt,0)) end ) *100 as decimal (10,2))  as renewalRate,-- 续保率
        sum(COALESCE(valid_employee_cnt,0)) as validEmployeeCnt,-- 当月在职人数
        cast((case when sum(COALESCE(valid_employee_cnt,0))=0 then 0
        else sum(COALESCE(insurance_amt_monthly,0.0000))/sum(COALESCE(valid_employee_cnt,0)) end )  as decimal (10,2))  as avgInsuranceAmtMonthly,-- 人均保费
        0 as planEmpAverageAmt -- 编制人均保费

        from rpt.insurance_2021_kpi_rank_dfp
        where 1=1
        <if test='regionCode != null and regionCode!=""'>
            AND area_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='orgCode != null and orgCode!=""'>
            AND bch_code = #{orgCode,jdbcType=VARCHAR}
        </if>
        <if test='isCustomerManager != null and isCustomerManager=="1"'>
            AND 1!=1
        </if>
        <if test='userName != null and userName!=""'>
            AND (emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR emp_name LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null and userId!=""'>
            AND emp_id = #{userId,jdbcType=VARCHAR}
        </if>

        <if test='endDate != null'>
            AND rpt_date = #{endDate}
        </if>

        <if test='orgCodeList != null'>
            and bch_code in
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by area_name
        ORDER BY
        <choose>
            <when test='sortType!=null and sortType=="insuredCnt"'>
                insuredCnt
            </when>
            <when test='sortType!=null and sortType=="insuredAmt"'>
                insuredAmt
            </when>
            <when test='sortType!=null and sortType=="achievementRate"'>
                achievementRate
            </when>
            <when test='sortType!=null and sortType=="renewalRate"'>
                renewalRate
            </when>
            <when test='sortType!=null and sortType=="avgInsuranceAmtMonthly"'>
                avgInsuranceAmtMonthly
            </when>
            <when test='sortType!=null and sortType=="planEmpAverageAmt"'>
                planEmpAverageAmt
            </when>
            <otherwise>
                area_name
            </otherwise>
        </choose>
        <choose>
            <when test='sortMode=="1"'>
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>



</mapper>
