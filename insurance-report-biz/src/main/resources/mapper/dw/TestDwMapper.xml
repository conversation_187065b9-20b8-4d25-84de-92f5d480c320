<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.report.dao.dw.TestDwMapper">

    <select id="test" resultType="java.util.Map">
        SELECT
        area_name AS regionName,
        SUM(insured_count) AS safesPerQty,
        SUM(loaner_count) AS loanPerQty,
        SUM( offline_loaner_count) AS safesBorrowerPerQty,
        SUM( offline_shareloan_count) AS safesCoBorrowerPerQty,
        SUM( offline_guarantor_count) AS safesGuarantorPerQty,
        SUM(loan_rel_count) AS safesRelevanterPerQty,

        SUM(no_loaner_count) AS safesNoLoanPerQty,
        CASE WHEN SUM(loan_rel_count)=0 THEN 0 ELSE
        SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(loan_rel_count) END AS safesOtherTransferRatio,
        CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(no_loaner_count)*1.0/SUM(insured_count) END AS safesNoLoanRatio,


        CASE WHEN SUM(loaner_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(loaner_count) END AS
        safesBorrowerTransferRatio,
        CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(insured_count) END AS
        safesBorrowerRatio,
        CASE WHEN SUM(insured_count)=0 THEN 0 ELSE
        SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(insured_count) END AS safesLoanRatio
        FROM rpt.insured_loan_trans_static_monthly
        WHERE area_cde != 'Total'
        AND district_cde = 'Total'

             AND rpt_date = to_date('2020-11-11','yyyy-MM-dd')
            AND area_name ='湖南区域'
        GROUP BY area_name
        ORDER BY area_name ASC
    </select>
</mapper>
