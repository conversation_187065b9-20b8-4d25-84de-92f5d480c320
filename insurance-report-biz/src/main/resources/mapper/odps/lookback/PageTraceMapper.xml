<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.report.dao.odps.PageTraceMapper">
    <select id="urlName" resultType="java.util.Map">
        SELECT urlname,
               MAX(conv(duration, 10, 10) / 1000) `最大耗时`,
               MIN(conv(duration, 10, 10) / 1000) `最小耗时`,
               AVG(conv(duration, 10, 10) / 1000) `平均每次耗时`,
               sum(conv(duration, 10, 10)) / 1000 `总耗时`,
               count(1)                           `使用次数`
        from cdfinance.stg_userevent_event_app5_dip
        WHERE urlname is not null
          and pt = '20200818'
          and eventname = '页面时长统计'
        GROUP by urlname
    </select>
    <select id="getMaxRow" resultType="java.lang.Long">
        SELECT count(1)
        from cdfinance.stg_userevent_event_app5_dip
        WHERE urlname is not null
          and pt = '20200818'
          and eventname = '页面时长统计';
    </select>
    <select id="listByEvent" resultType="com.cfpamf.ms.insur.report.pojo.vo.TraceEventVO">
        SELECT eventname, eventattr, begintime, urlname
        from cdfinance.stg_userevent_event_app5_dip
        WHERE pt BETWEEN '20200801' and '20200826'
          <if test="userId!=null">
              userid = #{userId}
          </if>
          and urlname is not null
    </select>

</mapper>
