<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.report.dao.odps.LookBackMapper">
    <select id="getInstByOrderId" resultType="com.cfpamf.ms.insur.report.pojo.dto.LookBackInstantDTO">

        SELECT max(serverreceivetime) end_time,
               min(serverreceivetime) start_time,
               max(userid)            userid,
               max(isshare)           isshare
        FROM cdfinance.stg_userevent_event_app6_dip
        WHERE pt between #{start} and #{end}
          and orderid = #{orderId}
    </select>
    <select id="events" resultType="com.cfpamf.ms.insur.report.pojo.vo.LookBackEventVO">
        SELECT serverreceivedate,
               serverreceivetime,
               version,
               os,
               userid,
               platform,
               appid,
               eventname,
               eventattr,
               urlname,
               begintime,
               beginmsec,
               tracename,
               tracecode,
               ossurl,
               orderid,
               usermobile,
               wxopenid,
               isshare,
               productversion,
               buyplatform,
               pt
        FROM cdfinance.stg_userevent_event_app6_dip
        WHERE pt between #{start} and #{end}
        <![CDATA[  and serverreceivetime <= #{endTime} ]]>
         and isshare = #{share}
          and userid = #{userId}
        order by serverreceivetime desc
        LIMIT 100;
    </select>
</mapper>
