<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.report.dao.safes.UserPostMapper">
    <select id="listStatusByJobNumbers" resultType="com.cfpamf.ms.insur.report.pojo.dto.EmpStatus">
        select main_job_number, count(distinct org_code) org_cts, group_concat(employee_status)
         all_status
        from user_post
         where main_job_number in <foreach collection="jobNumbers" item="item" open="(" close=")" separator=",">#{item}</foreach>
        group by main_job_number
    </select>
</mapper>