<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.report.dao.safes.SmOrderMapper">

    <select id="selectByLookBack" resultType="com.cfpamf.ms.insur.report.pojo.vo.OrderVO">
        select t1.fhOrderId,
        soa.personName applicant_person_name,
        soi.personName insured_person_name,
        sp.productName,
        soi.policyNo,
        t1.create_time create_time
        from sm_order t1
        left join sm_order_applicant soa on t1.fhOrderId = soa.fhOrderId
        left join sm_order_insured soi on t1.fhOrderId = soi.fhOrderId
        left join sm_product sp on t1.productId = sp.id
        where
        t1.payStatus = 2
        <if test='orderNo != null'>
            AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
        </if>
        <if test='policyNo !=  null'>
            AND soi.policyNo=#{policyNo}
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='applicantdName != null'>
            AND ((soa.personName LIKE CONCAT(#{applicantdName},'%')) OR (soa.idNumber LIKE CONCAT(#{applicantdName},'%'))
                )
        </if>
        <if test='insuredName != null'>
            AND ((soi.personName LIKE CONCAT(#{insuredName},'%')) OR (soi.idNumber LIKE CONCAT(#{insuredName},'%'))
           )
        </if>
        order by t1.id desc
    </select>
    <select id="getDetail" resultType="com.cfpamf.ms.insur.report.pojo.vo.OrderDetailVO">
        select so.fhOrderId,
               so.create_time,
               sp.companyId,
               sp.productName,
               sp2.planName,
               so.startTime,
               so.endTime,

               soa.personName          applicant_person_name,
               soa.personGender        applicant_person_gender,
               soa.idNumber            applicant_id_number,
               soi.policyNo            policyNo,
               soi.personName          insured_person_name,
               soi.idNumber            insured_id_number,
               soi.birthday            insured_birthday,
               soi.personGender        insured_person_gender,
               soi.appStatus,
               so.qty                  qty,
               (so.qty * so.unitPrice) totalAmount,
               so.recommendId          recommendUserId,
               t7.userName             recommendUserName,
               t7.userMobile           recommendUserMobile,
               t7.regionName,
               t7.organizationName,
               so.channel,
               so.subChannel,
               so.paymentTime
        from sm_order so
                 LEFT join sm_product sp
                           on so.productId = sp.id
                 LEFT join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
                 LEFT join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
                 LEFT join sm_plan sp2 on sp2.id = so.planId
                 LEFT JOIN auth_user t7 ON t7.userId = so.recommendId and t7.enabled_flag = 0
        where so.fhOrderId = #{orderNo}
        limit 1
    </select>

    <select id="getTodayCommissionByTime" resultType="com.cfpamf.ms.insur.report.pojo.dto.OrderCommissionDTO">
        SELECT ifnull(SUM(totalAmount),0) AS insuredAmt, ifnull(SUM(qty),0) AS insuredCnt,ifnull(sum(paymentAmount),0) as paymentAmt FROM sm_order_commission WHERE
        <![CDATA[ accountTime >= #{startTime} AND accountTime <= #{endTime} ]]> AND appStatus IN (1, 4) and enabled_flag=0
    </select>
</mapper>
