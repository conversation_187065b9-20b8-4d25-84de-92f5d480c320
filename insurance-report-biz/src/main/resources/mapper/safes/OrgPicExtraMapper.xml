<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.report.dao.safes.OrgPicExtraMapper">

    <select id="getOrgPicExtraByUserId" resultType="com.cfpamf.ms.insur.report.pojo.vo.OrgPicExtraVO">
        SELECT *
        FROM org_pic_extra
        WHERE enabled_flag = 0
          AND userId = #{userId}
        LIMIT 1
    </select>
    <select id="getOrgPicExtraByUserIdAndOrg" resultType="com.cfpamf.ms.insur.report.pojo.vo.OrgPicExtraVO">
        SELECT *
        FROM org_pic_extra
        WHERE enabled_flag = 0
          AND mainJobNumber = #{userId}
          AND orgCode = #{orgCode}
        LIMIT 1
    </select>
</mapper>
