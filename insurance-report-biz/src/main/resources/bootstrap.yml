spring:
  application:
    name:  insurance-report
  jackson:
    date-format: 'yyyy-MM-dd HH:mm:ss'
  cloud:
    config:
      enabled: false
      name: ${spring.application.name}
      failFast: true
      retry:
        initialInterval: 3000
        multiplier: 1.3
        maxInterval: 5000
        maxAttempts: 20
      uri: http://config-server.default/


server:
  port: 10120

health:
  config:
    enabled: false

logging:
  level:
    root: info
    com.cfpamf: debug
    io.swagger: error

pagehelper:
  #helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql


mybatis:
  configuration:
    cache-enabled: false
    default-statement-timeout: 30
    map-underscore-to-camel-case: true


mapper:
  mappers:
    - com.cfpamf.ms.insur.report.dao.CommonMapper
  # 禁止全表修改
  safe-update: true
  # 禁止全表删除
  safe-delete: true

nacos:
  config:
    bootstrap:
      enable: true
      log-enable: true
    type: yaml
    server-addr: @config_uri@ #连接地址，不同环境有不同的地址，建议采用变量名方式
    namespace: @namespace@ #配置文件对应的命名空间，填入ID
    group: safes #组名，默认是DEFAULT_GROUP，按需所建
    username: @username@ #连接nacos服务器的用户名
    password: @password@ #连接nacos服务器的密码
    data-ids: application,insurance-report.yml
