package com.cfpamf.ms.insur.report.advice;

import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 全部改为把空字符串参数全部改为null
 *
 * <AUTHOR>
 */
@Component
public class RequestFilter implements Filter {

    /**
     * 初始化
     *
     * @param config
     */
    @Override
    public void init(FilterConfig config) {
        // 初始化
    }

    /**
     * 修改request
     *
     * @param srequest
     * @param sresponse
     * @param filterChain
     * @throws IOException
     * @throws ServletException
     */
    @Override
    public void doFilter(ServletRequest srequest, ServletResponse sresponse, FilterChain filterChain)
            throws IOException, ServletException {
        srequest.setCharacterEncoding(StandardCharsets.UTF_8.name());
        sresponse.setCharacterEncoding(StandardCharsets.UTF_8.name());
        HttpServletRequest request = (HttpServletRequest) srequest;
        filterChain.doFilter(new RequestWrapper(request), sresponse);
    }

    /**
     * 销毁
     */
    @Override
    public void destroy() {
        // 销毁
    }
}
