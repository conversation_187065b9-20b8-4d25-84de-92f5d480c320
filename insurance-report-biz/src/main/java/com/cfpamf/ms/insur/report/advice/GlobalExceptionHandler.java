package com.cfpamf.ms.insur.report.advice;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.common.ms.exception.MSNONeedRetryException;
import com.cfpamf.ms.insur.report.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {


    /**
     * 业务操作异常：用户误操作，参数错误等等
     * @param e
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = {MSBizNormalException.class})
    public CommonResult handler(MSBizNormalException e) {
        log.info("业务正常校验异常", e);
        return CommonResult.failResult(e);
    }

    /**
     * 业务异常
     * MSNONeedRetryException.class
     * @param e
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = {MSNONeedRetryException.class})
    public CommonResult handler(MSNONeedRetryException e) {
        log.warn("不需要报警异常", e);
        return CommonResult.failResult(e);
    }

    /**
     * 处理MSException异常
     * @param e 异常对象
     * @return 返回处理结果
     */
    @ResponseBody
    @ExceptionHandler(value = MSException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public CommonResult handler(MSException e) {
        log.error("服务器内部异常", e);
        return CommonResult.failResult(e);
    }

    /**
     * 处理HttpMessageNotReadableException异常
     * @param e 异常对象
     * @return 返回处理结果
     */
    @ResponseBody
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public CommonResult handler(HttpMessageNotReadableException e){
        log.warn("客户端异常", e);
        return CommonResult.failResult(e);
    }

    /**
     * 处理HttpRequestMethodNotSupportedException异常
     * @param e 异常对象
     * @return 返回处理结果
     */
    @ResponseBody
    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public CommonResult handler(HttpRequestMethodNotSupportedException e) {
        log.warn("客户端异常", e);
        return CommonResult.failResult(e);
    }

    /**
     * 处理ServletRequestBindingException异常
     * @param e 异常对象
     * @return 返回处理结果
     */
    @ResponseBody
    @ExceptionHandler(value = ServletRequestBindingException.class)
    public CommonResult handler(ServletRequestBindingException e) {
        log.warn("参数错误", e);
        return CommonResult.failResult(e);
    }

    /**
     * 处理MethodArgumentTypeMismatchException异常
     * @param e 异常对象
     * @return 返回处理结果
     */
    @ResponseBody
    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    public CommonResult handler(MethodArgumentTypeMismatchException e) {
        log.warn("参数类型不匹配", e);
        return CommonResult.failResult(e);
    }

    /**
     * 处理ClientAbortException异常
     * @param e 异常对象
     * @return 返回处理结果
     */
    @ResponseBody
    @ExceptionHandler(value = ClientAbortException.class)
    public CommonResult handler(ClientAbortException e) {
        log.warn("客户端已关闭连接", e);
        return CommonResult.failResult(e);
    }

    /**
     * 处理Exception异常
     * @param e 异常对象
     * @return 返回处理结果
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public CommonResult handler(Exception e) {
        log.error("服务器内部异常", e);
        return CommonResult.failResult(e);
    }
}
