package com.cfpamf.ms.insur.report.enums;

import com.cfpamf.ms.insur.report.pojo.vo.excel.BranchStatisticsExcelVo;
import com.cfpamf.ms.insur.report.pojo.vo.excel.PersonnelStatisticsExcelVo;
import com.cfpamf.ms.insur.report.pojo.vo.excel.RegionStatisticsExcelVo;
import com.cfpamf.ms.insur.report.pojo.vo.excel.TotalStatisticsExcelVo;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>

@AllArgsConstructor
@Getter
public enum ReportType {
    /**
     * 个人
     */
    PERSONAL("personal", "个人", PersonnelStatisticsExcelVo.class, "xlsx/PersonnelPerformanceExcelTemplate.xlsx"),
    /**
     * 机构
     */
    ORG("org", "机构", BranchStatisticsExcelVo.class, "xlsx/BranchPerformanceExcelTemplate.xlsx"),
    /**
     * 区域
     */
    REGION("region", "区域", RegionStatisticsExcelVo.class, "xlsx/RegionPerformanceExcelTemplate.xlsx"),
    /**
     * 汇总
     */
    TOTAL("total", "汇总", TotalStatisticsExcelVo.class, "xlsx/TotalPerformanceExcelTemplate.xlsx");

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * excelVo class对象
     */
    private Class excelVoClass;

    /**
     * excel模板路径
     */
    private String excelTemplatePath;

    /**
     * 通过code获取ReportType
     * 默认返回OTHER
     *
     * @param code
     * @return
     */
    public static ReportType getReportTypeByCode(String code) {
        ReportType[] reportTypeArr = ReportType.values();
        for (int i = 0; i < reportTypeArr.length; i++) {
            if (reportTypeArr[i].getCode().equals(code)) {
                return reportTypeArr[i];
            }
        }
        return null;
    }
}
