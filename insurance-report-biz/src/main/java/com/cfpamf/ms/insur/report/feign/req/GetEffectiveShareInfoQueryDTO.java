package com.cfpamf.ms.insur.report.feign.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class GetEffectiveShareInfoQueryDTO {

    @ApiModelProperty(value = "员工编码列表")
    private List<String> masterJobNumberList;
    @ApiModelProperty(value = "业务类型")
    private String bizType;
    @ApiModelProperty(value = "分享时间")
    private String forwardTime;
}
