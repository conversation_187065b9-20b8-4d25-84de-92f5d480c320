package com.cfpamf.ms.insur.report.feign;

import com.cfpamf.ms.insur.report.pojo.CommonResult;
import com.cfpamf.ms.insur.report.pojo.dto.DataAuthDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> 2022/9/8 11:26
 */
@FeignClient(name = "insurance-admin", contextId = "DataAuthFacade", url = "${insurance-admin.url}", path = "/back/data/auth")
public interface DataAuthFacade {

    /**
     * 数据权限
     *
     * @param authorization
     * @return
     */
    @GetMapping("dataAuth")
    CommonResult<DataAuthDTO> dataAuth(@RequestParam("authorization") String authorization);
}
