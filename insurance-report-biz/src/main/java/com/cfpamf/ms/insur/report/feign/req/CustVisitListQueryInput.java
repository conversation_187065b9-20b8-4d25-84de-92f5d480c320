package com.cfpamf.ms.insur.report.feign.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CustVisitListQueryInput {

    /**
     * 跟进标识 0 未跟进, 1 已跟进
     */
    @ApiModelProperty(value = "跟进标识 0 未跟进, 1 已跟进")
    private Integer followFlag;

    @ApiModelProperty(value = "客户经理编码")
    @NotBlank(message = "客户经理编码不能为空")
    private String manageCode;

    @ApiModelProperty(value = "素材编码")
    private String marketingCode;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "一页多少条数据")
    private Integer pageSize;
}
