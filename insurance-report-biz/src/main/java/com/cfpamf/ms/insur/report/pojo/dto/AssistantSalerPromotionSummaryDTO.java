package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "保险销售端推广指标对象", description = "")
public class AssistantSalerPromotionSummaryDTO {
    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "当月分享数", notes = "")
    Integer smShareCnt;
    /**
     * 当月人均分享数
     */
    @ApiModelProperty(value = "当月人均分享数", notes = "")
    BigDecimal smShareCntAvg;
    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "当月访客数", notes = "")
    Integer smVisitCnt;
    /**
     * 当月人均访客数
     */
    @ApiModelProperty(value = "当月人均访客数", notes = "")
    BigDecimal smVisitCntAvg;
    /**
     * 当月转化保单数
     */
    @ApiModelProperty(value = "当月转化保单数", notes = "")
    Integer smTransformPolicyCnt;
    /**
     * 当日分享数
     */
    @ApiModelProperty(value = "当日分享数", notes = "")
    Integer sdShareCnt;
    /**
     * 当日访客数
     */
    @ApiModelProperty(value = "当日访客数", notes = "")
    Integer sdVisitCnt;

    /**
     * 访客全国排名
     */
    @ApiModelProperty(value = "当月分享榜首员工姓名", notes = "")
    String monthShareTopName;

    /**
     * 访客区域排名
     */
    @ApiModelProperty(value = "当月分享榜首员工姓名", notes = "")
    String monthVisitTopEmpName;

    /**
     * 未跟进访客数
     */
    @ApiModelProperty(value = "未跟进访客数", notes = "")
    Integer unfollowCnt;

    /**
     * 当月转换客户数
     */
    @ApiModelProperty(value = "当月转换客户数", notes = "")
    Integer smTransformCustomerCnt;

    private String empName;

    @ApiModelProperty(value = "层级名称", notes = "")
    String name;

    @ApiModelProperty(value = "区域编码", notes = "")
    private String areaCode;
    @ApiModelProperty(value = "片区编码", notes = "")
    private String districtCode;
    @ApiModelProperty(value = "分支编码", notes = "")
    private String bchCode;
    @ApiModelProperty(value = "区域名称", notes = "")
    private String areaName;
    @ApiModelProperty(value = "分支名称", notes = "")
    private String bchName;

    @ApiModelProperty(value = "微信头像", notes = "")
    private String empWxImgUrl;

    @ApiModelProperty(value = "当月有效访客数全国排名", notes = "")
    private Integer smEffectiveVisitUserCntRank;

    @ApiModelProperty(value = "当月有效分享数全国排名", notes = "")
    private Integer smEffectiveShareCntRank;

    @ApiModelProperty(value = "当月有效访客数区域排名", notes = "")
    private Integer smEffectiveVisitUserCntAreaRank;

    @ApiModelProperty(value = "当月有效分享数区域排名", notes = "")
    private Integer smEffectiveShareCntAreaRank;

    @ApiModelProperty(value = "员工工号", notes = "")
    private String empId;

    @ApiModelProperty(value = "昨日分享数", notes = "")
    private Integer cdShareCnt;

    @ApiModelProperty(value = "昨日分访客数", notes = "")
    private Integer cdVisitCnt;
}
