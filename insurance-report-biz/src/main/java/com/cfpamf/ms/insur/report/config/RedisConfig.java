package com.cfpamf.ms.insur.report.config;

import com.cfpamf.ms.insur.report.constant.CacheKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.util.Assert;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Configuration
@EnableCaching
@EnableRedisHttpSession
@Slf4j
public class RedisConfig {
    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        //默认1
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofSeconds(30L))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(keySerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(valueSerializer()))
                .disableCachingNullValues()
                ;
        //config.usePrefix();
        Set<String> cacheNames =  new HashSet<>();
        cacheNames.add(CacheKeyConstants.WX_LBT_IMAGE);
        cacheNames.add(CacheKeyConstants.PRODUCT_LIST);
        cacheNames.add(CacheKeyConstants.OTHER_PREFIX);
        cacheNames.add(CacheKeyConstants.HOUR_PREFIX);


        // 对每个缓存空间应用不同的实现时间
        Map<String, RedisCacheConfiguration> configMap = new HashMap<>();
        configMap.put(CacheKeyConstants.WX_LBT_IMAGE, config.entryTtl(Duration.ofSeconds(43200L)));
        configMap.put(CacheKeyConstants.PRODUCT_LIST, config.entryTtl(Duration.ofSeconds(43200L)));
        configMap.put(CacheKeyConstants.OTHER_PREFIX, config.entryTtl(Duration.ofSeconds(43200L)));
        configMap.put(CacheKeyConstants.HOUR_PREFIX, config.entryTtl(Duration.ofSeconds(30L)));

        RedisCacheManager redisCacheManager = RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(config)
                .initialCacheNames(cacheNames)// 注意这两句的调用顺序，一定要先调用该方法设置初始化的缓存名，再初始化相关的配置
                .withInitialCacheConfigurations(configMap)
                .transactionAware()
                .build();

        log.debug("自定义RedisCacheManager加载完成");
        return redisCacheManager;
    }
    @Bean(name = "redisTemplate")
    public RedisTemplate<String,Object> redisTemplate(RedisConnectionFactory redisConnectionFactory){
        RedisTemplate<String,Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        GenericJackson2JsonRedisSerializer valueSerializer = new GenericJackson2JsonRedisSerializer();
        redisTemplate.setValueSerializer(valueSerializer);
        redisTemplate.setHashValueSerializer(valueSerializer);
        log.debug("自定义RedisTemplate加载完成");
        return redisTemplate;
    }

    private RedisSerializer<Object> valueSerializer() {
        return new GenericJackson2JsonRedisSerializer();
    }

    private RedisSerializer<String> keySerializer(){
        return new StringRedisSerializer();
    }


    /**
     * redis序列化改造
     *
     * <AUTHOR>
     **/
    public class StringRedisSerializer implements RedisSerializer<String> {

        private final Charset charset;

        public StringRedisSerializer() {
            this(Charset.forName(StandardCharsets.UTF_8.name()));
        }

        public StringRedisSerializer(Charset charset) {
            Assert.notNull(charset, "Charset must not be null!");
            this.charset = charset;
        }

        @Override
        public String deserialize(byte[] bytes) {
            return (bytes == null ? null : new String(bytes, charset));
        }

        @Override
        public byte[] serialize(String object) {
            return (object == null ? null : object.getBytes(charset));
        }
    }
}
