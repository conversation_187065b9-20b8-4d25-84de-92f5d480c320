package com.cfpamf.ms.insur.report.enums;

import lombok.Getter;

@Getter
public enum OnlinePromotionLevelTypeEnum {

    NATION("nation","全国"),
    AREA("area","区域"),
    DISTRICT("district","片区"),
    BCH("bch","分支")
    ;

    private String levelType;

    private String desc;

    OnlinePromotionLevelTypeEnum(String levelType,String desc){
        this.levelType = levelType;
        this.desc = desc;
    }

}
