package com.cfpamf.ms.insur.report.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.feign.DataAuthFacade;
import com.cfpamf.ms.insur.report.pojo.CommonResult;
import com.cfpamf.ms.insur.report.pojo.dto.DataAuthDTO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * <AUTHOR>
 * @since 2024/2/22 16:58
 */
@Service
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DataAuthService {
    BmsService bmsService;

    DataAuthFacade dataAuthFacade;


    /**
     * 获取数据权限
     * @return DataAuthDTO 数据权限对象
     */
    public DataAuthDTO dataAuth() {
        // 调用dataAuthFacade的数据权限接口，传入token
        CommonResult<DataAuthDTO> commonResult = dataAuthFacade.dataAuth(bmsService.getToken());

        // 如果调用不成功
        if (!commonResult.isSuccess()) {
            // 抛出异常，表示获取数据权限失败
            throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(), "获取数据权限失败:" + commonResult.getMessage());
        } else if (Objects.isNull(commonResult.getData())) {
            // 如果返回数据为空
            throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
        }

        // 返回数据权限对象
        return commonResult.getData();
    }

}
