package com.cfpamf.ms.insur.report.util;

import com.cfpamf.ms.insur.report.annotation.Mask;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;

/**
 * 手机号  身份证脱敏
 *
 * <AUTHOR>
 **/
public class DataMaskUtil {

    /**
     * 隐藏手机号正则
     */
    private final static String MASK_MOBILE_REGEX = "(\\d{3})\\d{4}(\\d{4})";

    /**
     * 隐藏邮箱正则
     */
    private final static String MASK_EMAIL_REGEX = "(\\w)(\\w+)(@\\w+)";

    /**
     * 隐藏身份证正则
     */
    private final static String MASK_ID_CARD_REGEX = "(\\d)(\\w+)(\\w{2})";

    /**
     * 隐藏手机号
     *
     * @param mobile
     * @return
     */
    public static String maskMobile(String mobile) {
        if (StringUtils.isEmpty(mobile) || (mobile.length() != 11)) {
            return mobile;
        }
        return safeReplaceAll(mobile, MASK_MOBILE_REGEX, "$1****$2");
    }

    /**
     * 隐藏邮箱信息
     *
     * @param email
     * @return
     */
    public static String maskEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            return email;
        }
        return safeReplaceAll(email, MASK_EMAIL_REGEX, "$1***$3");
    }

    /**
     * 隐藏身份证号码
     *
     * @param id
     * @return
     */
    public static String maskIdCardNo(String id) {
        if (StringUtils.isEmpty(id) || (id.length() < 8)) {
            return id;
        }

        return safeReplaceAll(id, MASK_ID_CARD_REGEX, "$1*********$3");
    }

    /**
     * 隐藏姓名姓氏
     *
     * @param name
     * @return
     */
    public static String maskFullName(String name) {
        if (StringUtils.isEmpty(name)) {
            return name;
        }
        return "*" + name.substring(1);
    }

    /**
     * 对实体列表脱敏
     *
     * @param datas
     * @param <T>
     * @return
     */
    @SuppressWarnings("uncheck")
    public static <T> List<T> maskList(List<T> datas) {
        datas.forEach(DataMaskUtil::maskObject);
        return datas;
    }

    public static <T> Collection<T> maskCollection(Collection datas) {
        datas.forEach(DataMaskUtil::maskObject);
        return datas;
    }

    /**
     * 对单实体脱敏
     *
     * @param data
     * @param <T>
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T maskObject(T data) {
        Class clazz0 = data.getClass();
        List<Class> clazzs = new ArrayList<>();
        clazzs.add(clazz0);
        while (clazz0.getSuperclass() != Object.class) {
            clazz0 = clazz0.getSuperclass();
            clazzs.add(clazz0);
        }
        clazzs.forEach(clazz -> Stream.of(clazz.getDeclaredFields()).forEach(f -> {
            Mask mask = f.getAnnotation(Mask.class);
            if (mask != null) {
                String fName = f.getName();
                try {
                    String methodName = fName.substring(0, 1).toUpperCase() + fName.substring(1);
                    Method getMethod = clazz.getMethod("get" + methodName, null);
                    Method setMethod = clazz.getMethod("set" + methodName, String.class);
                    StringFunction sf = null;
                    if (mask.dataType() == Mask.DataType.MOBILE) {
                        sf = DataMaskUtil::maskMobile;
                    } else if (mask.dataType() == Mask.DataType.EMAIL) {
                        sf = DataMaskUtil::maskEmail;
                    } else if (mask.dataType() == Mask.DataType.ID_CARD) {
                        sf = DataMaskUtil::maskIdCardNo;
                    }
                    if (sf != null) {
                        setMethod.invoke(data, sf.apply(getMethod.invoke(data, null).toString()));
                    }
                } catch (Exception e) {

                }
            }
        }));
        return data;
    }

    /**
     * 数据被传递至应用程序并作为正则表达式使用。可能导致线程过度使用 CPU 资源，从而导致拒绝服务攻击。
     * 使用线程池 + Future, 限定执行时间, 并捕获异常.
     *
     * @param input
     * @param regex
     * @param replacement
     * @return
     */
    public static String safeReplaceAll(String input, String regex, String replacement) {
        return input.replaceAll(regex, replacement);
    }
}
