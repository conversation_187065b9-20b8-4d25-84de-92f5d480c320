package com.cfpamf.ms.insur.report.dao.odps;

import com.cfpamf.ms.insur.report.pojo.vo.TraceEventVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Create By zhengjing on 2020/8/21 10:10
 */
@Mapper
public interface PageTraceMapper {

    List<Map<String, Object>> urlName();


    long getMaxRow();

    List<TraceEventVO> listByEvent(@Param("userId") String userId);
}
