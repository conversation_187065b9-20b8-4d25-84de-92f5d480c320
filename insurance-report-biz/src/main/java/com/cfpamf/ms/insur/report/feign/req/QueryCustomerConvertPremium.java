package com.cfpamf.ms.insur.report.feign.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class QueryCustomerConvertPremium {
    @ApiModelProperty("客户身份证号码")
    @NotEmpty(message = "客户身份证不能为空")
    List<String> idCardList;

    @ApiModelProperty("险种类型")
    @NotBlank(message = "险种类型不能为空")
    private String productAttrCode;

    @ApiModelProperty("查询日期")
    @NotBlank(message = "查询日期不能为空")
    private String date;
}
