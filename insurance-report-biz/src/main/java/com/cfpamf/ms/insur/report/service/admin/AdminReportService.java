package com.cfpamf.ms.insur.report.service.admin;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.dao.dw.SmDcReportMapper;
import com.cfpamf.ms.insur.report.dao.safepg.SmSafepgReportMapper;
import com.cfpamf.ms.insur.report.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.pojo.dto.InsuranceOrderAllStatDTO;
import com.cfpamf.ms.insur.report.pojo.dto.OrderCommissionDTO;
import com.cfpamf.ms.insur.report.pojo.query.Pageable;
import com.cfpamf.ms.insur.report.pojo.query.PromoFeeQuery;
import com.cfpamf.ms.insur.report.pojo.query.RenewalRateQuery;
import com.cfpamf.ms.insur.report.pojo.query.ReportQuery;
import com.cfpamf.ms.insur.report.pojo.vo.*;
import com.cfpamf.ms.insur.report.util.CommonUtil;
import com.cfpamf.ms.insur.report.util.ExcelBuilderUtil;
import com.cfpamf.ms.insur.report.util.LocalDateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.sf.jxls.transformer.XLSTransformer;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Slf4j
@Service
public class AdminReportService {


    @Autowired
    private SmDcReportMapper smDcReportMapper;
    @Autowired
    private SmSafepgReportMapper smSafepgReportMapper;
    @Autowired
    SmOrderMapper orderMapper;


    /**
     * 个人业绩报表
     *
     * @param query
     * @return
     */
    public PageInfo<PersonalBusinessVO> listSmPersonalBusinessDetailsByPage(ReportQuery query) {
        //验证查询日期是否为空
        CommonUtil.validateQueryDate(query);
        ifNeedPage(query);
        return new PageInfo<>(smDcReportMapper.listSmPersonalBusinessDetails(query));
    }

    /**
     * 下载个人业绩明细详情表
     *
     * @param query
     * @param resp
     */
    public void downloadSmPersonalBusinessDetails(ReportQuery query, HttpServletResponse resp) {
        commonDownloadExcel(query, resp, PersonalBusinessVO.class, this::listSmPersonalBusinessDetailsByPage, "个人业绩报表");
    }

    /**
     * 机构有业绩报表
     *
     * @param query
     * @return
     */
    public PageInfo<OrgBusinessVO> listSmOrgBusinessDetailsByPage(ReportQuery query) {
        CommonUtil.validateQueryDate(query);
        ifNeedPage(query);
        return new PageInfo<>(smDcReportMapper.listSmOrgBusinessDetails(query));
    }

    public void downloadSmOrgBusinessDetails(ReportQuery query, HttpServletResponse resp) {
        commonDownloadExcel(query, resp, OrgBusinessVO.class, this::listSmOrgBusinessDetailsByPage, "机构业绩报表");
    }

    /**
     * 区域业绩报表
     *
     * @param query
     * @return
     */
    public PageInfo<RegionBusinessVO> listSmRegionBusinessDetailsByPage(ReportQuery query) {
        CommonUtil.validateQueryDate(query);
        ifNeedPage(query);
        return new PageInfo<>(smDcReportMapper.listSmRegionBusinessDetails(query));
    }

    public void downloadSmRegionBusinessDetails(ReportQuery query, HttpServletResponse resp) {
        commonDownloadExcel(query, resp, RegionBusinessVO.class, this::listSmRegionBusinessDetailsByPage, "区域业绩报表");
    }


    /**
     * 个人续保率报表
     *
     * @param query
     * @return
     */
    public PageInfo<PersonalRenewalRateVO> listPersonalRenewalRateByPage(RenewalRateQuery query) {
        CommonUtil.validateRenewalRateDate(query);
        ifNeedPage(query);
        return new PageInfo<>(smDcReportMapper.listPersonalRenewalRate(query));
    }

    public void downloadPersonalRenewalRate(RenewalRateQuery query, HttpServletResponse resp) {
        commonDownloadExcel(query, resp, PersonalRenewalRateVO.class, this::listPersonalRenewalRateByPage, "个人续保率报表");
    }

    /**
     * 机构续保率报表
     *
     * @param query
     * @return
     */
    public PageInfo<OrgRenewalRateVO> listOrgRenewalRateByPage(RenewalRateQuery query) {
        CommonUtil.validateRenewalRateDate(query);
        ifNeedPage(query);
        return new PageInfo<>(smDcReportMapper.listOrgRenewalRate(query));
    }

    public void downloadOrgRenewalRate(RenewalRateQuery query, HttpServletResponse resp) {
        commonDownloadExcel(query, resp, OrgRenewalRateVO.class, this::listOrgRenewalRateByPage, "机构续保率报表");
    }

    /**
     * 区域续保率报表
     *
     * @param query
     * @return
     */
    public PageInfo<RegionRenewalRateVO> listRegionRenewalRateByPage(RenewalRateQuery query) {
        CommonUtil.validateRenewalRateDate(query);
        ifNeedPage(query);
        return new PageInfo<>(smDcReportMapper.listRegionRenewalRate(query));
    }

    public void downloadRegionRenewalRate(RenewalRateQuery query, HttpServletResponse resp) {
        commonDownloadExcel(query, resp, RegionRenewalRateVO.class, this::listRegionRenewalRateByPage, "区域续保率报表");
    }

    /**
     * 续保率汇总报表
     *
     * @param query
     * @return
     */
    public PageInfo<RenewalRateVO> listRenewalRateByPage(RenewalRateQuery query) {
        CommonUtil.validateRenewalRateDate(query);
        ifNeedPage(query);
        return new PageInfo<>(smDcReportMapper.listRenewalRate(query));
    }

    public void downloadRenewalRate(RenewalRateQuery query, HttpServletResponse resp) {
        commonDownloadExcel(query, resp, RenewalRateVO.class, this::listRenewalRateByPage, "续保率汇总报表");
    }


    /**
     * 个人推广费报表
     *
     * @param query
     * @return
     */
    public PageInfo<PersonalPromoFeeVO> listPersonalPromoFeeByPage(PromoFeeQuery query) {
        CommonUtil.validatePromoFeeDate(query);
        ifNeedPage(query);
        return new PageInfo<>(smDcReportMapper.listPersonalPromoFee(query));
    }


    /**
     * 个人推广费报表2021
     *
     * @param query
     * @return
     */
    public PageInfo<PersonalPromoFeeVO> searchPersonalPromoFeeByPage(PromoFeeQuery query) {
        // 如果结束日期是当月的话应该是截止当日的前一天
        CommonUtil.validatePromoFeeDate(query, LocalDate.now().plusDays(-1));
        // 如果当前日期没有在结束日期之后 就把结束日期设置为当前日期前一天
        if (!LocalDate.now().isAfter(query.getEndDate())) {
            query.setEndDate(LocalDate.now().plusDays(-1));
        }
        //获取查询日期范围中的每个末日期
        LocalDate endDate = query.getEndDate();
        //获取时间范围的内的月末集合
        List<LocalDate> monthEndList = LocalDateUtil.getMonthEndBetween(query.getStartDate(), endDate);
        //设置查询集合
        List<LocalDate> localDateList = Lists.newArrayList(endDate);
        localDateList.addAll(monthEndList);
        query.setLocalDateList(localDateList);
        ifNeedPage(query);
        return new PageInfo<>(smDcReportMapper.searchPersonalPromoFee(query));
    }

    public void downloadPersonalPromoFee(PromoFeeQuery query, HttpServletResponse resp) {
        commonDownloadExcel(query, resp, PersonalPromoFeeVO.class, this::searchPersonalPromoFeeByPage, "保险推广费报表");
    }

    /**
     * 机构绩效报表
     *
     * @param query
     * @return
     */
    public PageInfo<OrgPromoFeeVO> listOrgPromoFeeByPage(PromoFeeQuery query) {

        CommonUtil.validatePromoFeeDate(query);
        ifNeedPage(query);
        return new PageInfo<>(smDcReportMapper.listOrgPromoFee(query));
    }

    public void downloadOrgPromoFee(PromoFeeQuery query, HttpServletResponse resp) {
        commonDownloadExcel(query, resp, OrgPromoFeeVO.class, this::listOrgPromoFeeByPage, "机构绩效报表");
    }

    /**
     * 机构绩效报表
     *
     * @param query
     * @return
     */
    public PageInfo<BusinessRankVO> listOrgBusinessRankByPage(ReportQuery query) {

        CommonUtil.validateQueryDate(query);
        ifNeedPage(query);
        return new PageInfo<>(smDcReportMapper.listOrgBusinessRank(query));
    }

    public void downloadOrgBusinessRank(ReportQuery query, HttpServletResponse resp) {
        commonDownloadExcel(query, resp, BusinessRankVO.class, this::listOrgBusinessRankByPage, "机构业务排名报表");
    }


    private void ifNeedPage(Pageable pageable) {
        if (!pageable.isAll()) {
            PageHelper.startPage(pageable.getPage(), pageable.getSize());
        }
    }

    /**
     * 机构佣金明细
     *
     * @param query
     * @return
     */
    public PageInfo<SmOrgCommissionReportVO> listOrgCommissionByPage(ReportQuery query) {

        CommonUtil.validateQueryDate(query);
        ifNeedPage(query);
//        smDcReportMapper.listOrgCommission(query);
        return new PageInfo<>(smSafepgReportMapper.listOrgCommission(query));
    }

    public void downloadOrgCommission(ReportQuery query, HttpServletResponse resp) {
        commonDownloadExcel(query, resp, SmOrgCommissionReportVO.class, this::listOrgCommissionByPage, "机构佣金明细报表");
    }


    private void ifNeedPage(ReportQuery query) {
        if (!query.isAll()) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
    }


    private <T extends ReportQuery> void commonDownloadExcel(T query, HttpServletResponse resp, Class clazz,
                                                             Function<T, PageInfo> queryFun, String title) {
        query.setAll(true);
        PageInfo sPage = queryFun.apply(query);
        try (OutputStream os = resp.getOutputStream()) {
            resp.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(title, StandardCharsets.UTF_8.name())
                    + new SimpleDateFormat("yyyyMMdd").format(new Date()) + ".xlsx");
            resp.setContentType("application/octet-stream");
            ExcelBuilderUtil.newInstance()
                    .createSheet(title)
                    .initSheetHead(clazz, true)
                    .addSheetData(sPage.getList())
                    .write(os);
        } catch (Exception e) {
            log.warn("文件下载失败", e);
            throw new MSBizNormalException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006);
        }
    }

    /**
     * 共同下载报表方法（模版下载）
     *
     * @param query
     * @param resp
     * @param queryFun
     * @param templateName
     */
    private <T extends ReportQuery> void downloadCommonSmy(T query, HttpServletResponse resp,
                                                           Function<T, PageInfo> queryFun, String templateName) {
        query.setAll(true);
        PageInfo sPage = queryFun.apply(query);

        Map<String, Object> xlsParams = buildTemplateBeanParams(sPage);
        XLSTransformer transformer = new XLSTransformer();
        try (OutputStream os = resp.getOutputStream()) {
            resp.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(templateName, StandardCharsets.UTF_8.name()));
            resp.setContentType("multipart/form-data");
            try (Workbook workbook = transformer.transformXLS(getTemplateFileInputStream(templateName), xlsParams)) {
                workbook.write(os);
                os.flush();
            } catch (Exception e) {
                log.error("", e);
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }

    /**
     * 构造Excel需要的参数Map
     *
     * @param page
     * @return
     */
    private Map<String, Object> buildTemplateBeanParams(PageInfo page) {
        Map<String, Object> beanParams = new HashMap<>(1);
        beanParams.put("list", page.getList());
        //后续可能需要添加合计
        return beanParams;
    }

    /**
     * 获取模板文件输入流
     *
     * @param name
     * @return
     */
    private InputStream getTemplateFileInputStream(String name) throws IOException {
        return new ClassPathResource(BaseConstants.SM_TEMPLATE_FILE_PATH + File.separatorChar + name).getInputStream();
    }

    /**
     * 基础报表
     *
     * @return
     */
    public SmSmyVO getSmSmSmy() {
        SmSmyVO smSmyVO = new SmSmyVO();
        Date now = DateUtil.getNow();
        Date todayStartTime = DateUtil.getBeginOfDay(now);
        Date todayEndTime = DateUtil.getEndOfDay(now);
        OrderCommissionDTO commissionDTO = orderMapper.getTodayCommissionByTime(todayStartTime, todayEndTime);
        if (commissionDTO != null) {
            smSmyVO.setTodayQty(commissionDTO.getInsuredCnt());
            smSmyVO.setTodayAmount(commissionDTO.getInsuredAmt());
        }
        InsuranceOrderAllStatDTO dto = smSafepgReportMapper.getInsuranceOrderAllStat();
        if (dto != null) {
            smSmyVO.setYstdayQty(dto.getInsuredCntDay());
            smSmyVO.setYstdayAmount(dto.getInsuredAmtDay());
            smSmyVO.setThisMonthQty(dto.getInsuredCntMonth());
            smSmyVO.setThisMonthAmount(dto.getInsuredAmtMonth());
            smSmyVO.setThisYearQty(dto.getInsuredCntYear());
            smSmyVO.setThisYearAmount(dto.getInsuredAmtYear());
            smSmyVO.setPastYearQty(dto.getInsuredCntAll() - dto.getInsuredCntYear());
            smSmyVO.setPastYearAmount(dto.getInsuredAmtAll().subtract(dto.getInsuredAmtYear()));
            smSmyVO.setAddUpQty(dto.getInsuredCntAll());
            smSmyVO.setAddUpAmount(dto.getInsuredAmtAll());
        }

        return smSmyVO;
    }
}
