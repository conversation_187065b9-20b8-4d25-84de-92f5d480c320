package com.cfpamf.ms.insur.report.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 组织级别的枚举类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/02/27
 */
@AllArgsConstructor
@Getter
public enum OrgLevelEnum {
    COUNTRY("COUNTRY", "全国"),
    AREA("AREA", "区域"),
    DISTRICT("DISTRICT", "片区"),
    BRANCH("BRANCH", "分支"),
    ;

    /**
     * 组织级别
     */
    private String level;
    /**
     * 描述
     */
    private String desc;
}
