package com.cfpamf.ms.insur.report.pojo.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分类类
 *
 * <AUTHOR>
 */
@Data
public class Pageable {

    /**
     * 第几页
     */
    @ApiModelProperty(value = "第几页")
    private Integer page = 1;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量")
    private Integer size = 20;

    /**
     * 是否查询所有
     */
    @ApiModelProperty(value = "是否查询所有", hidden = true)
    private boolean all;

    /**
     * 是否分页
     */
    @ApiModelProperty(value = "是否分页", hidden = true)
    private boolean queryPage = true;

    @JsonIgnore
    @ApiModelProperty(hidden = true)
    public Integer getStartRow() {
        if (!all && page != null && size != null) {
            return (page - 1) * size;
        }
        return null;
    }
}
