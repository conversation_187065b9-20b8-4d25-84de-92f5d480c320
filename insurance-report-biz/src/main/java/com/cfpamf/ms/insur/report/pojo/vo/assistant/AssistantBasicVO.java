package com.cfpamf.ms.insur.report.pojo.vo.assistant;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;


/**
 * 保险助手基类
 *
 * <AUTHOR>
 * @since 2024/2/21 14:11
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantBasicVO {

    @ApiModelProperty("对应分区数据")
    String pt;
    @ApiModelProperty("当月标准保费")
    BigDecimal smAssessConvertInsuranceAmt;

    @ApiModelProperty("当年标准保费")
    BigDecimal syAssessConvertInsuranceAmt;

    @ApiModelProperty("当月标准保费目标")
    BigDecimal smAssessConvertInsuranceAmtTarget;

    @ApiModelProperty("当年标准保费目标")
    BigDecimal syAssessConvertInsuranceAmtTarget;

    @ApiModelProperty("当月标准保费达成率")
    BigDecimal smAssessConvertInsuranceAmtAchieveRate;

    @ApiModelProperty("当年标准保费达成率")
    BigDecimal syAssessConvertInsuranceAmtAchieveRate;

    @ApiModelProperty("当月异业保费配比")
    BigDecimal smOfflineLoanInsuranceRate;

    @ApiModelProperty("当年异业保费配比")
    BigDecimal syOfflineLoanInsuranceRate;

    @ApiModelProperty("当月异业保费配比目标")
    BigDecimal smOfflineLoanInsuranceRateTarget;

    @ApiModelProperty("当年异业保费配比目标")
    BigDecimal syOfflineLoanInsuranceRateTarget;

    @ApiModelProperty("当月留存率")
    BigDecimal smInsuranceRetentionRate;

    @ApiModelProperty("当年留存率")
    BigDecimal syInsuranceRetentionRate;

    @ApiModelProperty("当月留存率目标")
    BigDecimal smInsuranceRetentionRateTarget;

    @ApiModelProperty("当年留存率目标")
    BigDecimal syInsuranceRetentionRateTarget;

    @ApiModelProperty("当月信贷客户留存率")
    BigDecimal smLoanInsuranceRetentionRate;

    @ApiModelProperty("当年信贷客户留存率")
    BigDecimal syLoanInsuranceRetentionRate;

    @ApiModelProperty("当月非信贷客户留存率")
    BigDecimal smUnloanInsuranceRetentionRate;

    @ApiModelProperty("当年非信贷客户留存率")
    BigDecimal syUnloanInsuranceRetentionRate;

    @ApiModelProperty("当月信贷客户转化率")
    BigDecimal smLoanCustTransRate;

    @ApiModelProperty("当年信贷客户转化率")
    BigDecimal syLoanCustTransRate;

    @ApiModelProperty("当月人均推广次数")
    BigDecimal smShareCntRate;

    @ApiModelProperty("当月人均推广次数目标值")
    Integer smShareCntRateTarget;
}
