package com.cfpamf.ms.insur.report.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 小额保险-今日昨日累计汇总
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
public class SmSmyVO {

    /**
     * 今日订单总数
     */
    @ApiModelProperty(value = "今日订单总数")
    private Integer todayQty;

    /**
     * 今日订单总金额
     */
    @ApiModelProperty(value = "今日订单总金额")
    private BigDecimal todayAmount;

    /**
     * 昨日订单总数
     */
    @ApiModelProperty(value = "昨日订单总数")
    private Integer ystdayQty;

    /**
     * 昨日订单总金额
     */
    @ApiModelProperty(value = "昨日订单总金额")
    private BigDecimal ystdayAmount;

    /**
     * 累计订单总数
     */
    @ApiModelProperty(value = "累计订单总数")
    private Integer addUpQty;

    /**
     * 累计订单总金额
     */
    @ApiModelProperty(value = "累计订单总金额")
    private BigDecimal addUpAmount;

    /**
     * 当月订单总数
     */
    @ApiModelProperty(value = "当月订单总数")
    private Integer thisMonthQty;

    /**
     * 当月订单总金额
     */
    @ApiModelProperty(value = "当月订单总金额")
    private BigDecimal thisMonthAmount;

    /**
     * 当年订单总数
     */
    @ApiModelProperty(value = "当月订单总数")
    private Integer thisYearQty;

    /**
     * 当年订单总金额
     */
    @ApiModelProperty(value = "当月订单总金额")
    private BigDecimal thisYearAmount;

    /**
     * 往年订单总数
     */
    @ApiModelProperty(value = "当月订单总数")
    private Integer pastYearQty;

    /**
     * 往年订单总金额
     */
    @ApiModelProperty(value = "当月订单总金额")
    private BigDecimal pastYearAmount;

    public Integer getAddUpQty() {
        return addUpQty + todayQty;
    }

    public BigDecimal getAddUpAmount() {
        return addUpAmount.add(getTodayAmount());
    }

    public Integer getThisMonthQty() {
        return thisMonthQty + todayQty;
    }

    public BigDecimal getThisMonthAmount() {
        return thisMonthAmount.add(getTodayAmount());
    }

    public Integer getThisYearQty() {
        return thisYearQty + todayQty;
    }

    public BigDecimal getThisYearAmount() {
        return thisYearAmount.add(getTodayAmount());
    }
}
