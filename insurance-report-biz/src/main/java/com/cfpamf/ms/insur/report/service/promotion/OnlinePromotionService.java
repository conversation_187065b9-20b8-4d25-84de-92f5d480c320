package com.cfpamf.ms.insur.report.service.promotion;

import com.cfpamf.ms.insur.report.pojo.dto.*;
import com.cfpamf.ms.insur.report.pojo.query.AssistantManagerPromotionLevelQuery;
import com.cfpamf.ms.insur.report.pojo.query.AssistantManagerPromotionQuery;
import com.cfpamf.ms.insur.report.pojo.query.AssistantSalerPromotionQuery;
import com.cfpamf.ms.insur.report.pojo.vo.promotion.*;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface OnlinePromotionService {

    AssistantSalerPromotionSummaryVO homeEmpSummary(String pt, String epmId);

    AssistantSalerPromotionSummaryVO indexEmpSummary(String pt, String epmId);

    AssistantSalerPromotionRankVO empRankList(AssistantSalerPromotionQuery assistantSalerPromotionSummaryQuery);

    AssistantManagerPromotionSummaryVO managerSummary(String empId,AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);

    AssistantManagerPromotionRankVO managerRankList(String empId,AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);

    AssistantManagerPromotionLevelVO nextLevelDetailList(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);
}
