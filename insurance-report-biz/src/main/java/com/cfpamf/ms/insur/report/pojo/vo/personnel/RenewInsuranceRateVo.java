package com.cfpamf.ms.insur.report.pojo.vo.personnel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 续保率
 *
 * <AUTHOR>
 * @date 2021/3/30 11:13
 */
@Data
public class RenewInsuranceRateVo {
    /**
     * 年续保率
     */
    @ApiModelProperty(value = "年续保率")
    private BigDecimal year;

    /**
     * 月续保率
     */
    @ApiModelProperty(value = "月续保率")
    private BigDecimal month;
}
