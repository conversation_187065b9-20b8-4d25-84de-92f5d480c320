package com.cfpamf.ms.insur.report.web;


import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.enums.DateTypeEnum;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.feign.DataAuthFacade;
import com.cfpamf.ms.insur.report.feign.resp.vo.UserDetailVO;
import com.cfpamf.ms.insur.report.pojo.CommonResult;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerBasicDTO;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerSingleTrendDTO;
import com.cfpamf.ms.insur.report.pojo.dto.DataAuthDTO;
import com.cfpamf.ms.insur.report.service.BmsService;
import com.cfpamf.ms.insur.report.service.assistant.AssistantSalerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

@Slf4j
@Api(value = "AssistantSalerController", tags = "销售助手指标")
@RestController
@RequestMapping("/assistant/saler")
public class AssistantSalerController {
    @Autowired
    DataAuthFacade dataAuthFacade;
    @Autowired
    private BmsService bmsService;

    @Autowired
    AssistantSalerService assistantSalerService;


    @ApiOperation(value = "查询员工基础指标")
    @GetMapping("/basic/metrics")
    public AssistantSalerBasicDTO empBasicMetrics(@RequestParam(required = false) String pt) {
        UserDetailVO userDetail= bmsService.getContextUserDetail();
        if (userDetail==null ){
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201010);
        }
        if (StringUtils.isEmpty(pt)){
            pt = LocalDate.now().minusDays(1).format(BaseConstants.FMT_YYYYMMDD);//昨天日期yyyyMMdd
        }
        return assistantSalerService.getAssistantSalerData(userDetail.getJobNumber(),pt);
    }

    @ApiOperation(value = "查询年度排名")
    @GetMapping("/rank/year")
    public AssistantSalerBasicDTO empRankYear(@RequestParam(required = false) String pt) {

        UserDetailVO userDetail= bmsService.getContextUserDetail();
        CommonResult<DataAuthDTO> commonResult = dataAuthFacade.dataAuth(bmsService.getToken());
        if (userDetail==null || !commonResult.isSuccess()){
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201010);
        }
        DataAuthDTO dataAuthDTO = commonResult.getData();
        if (StringUtils.isEmpty(pt)){
            pt = LocalDate.now().minusDays(1).format(BaseConstants.FMT_YYYYMMDD);//昨天日期yyyyMMdd
        }
        return assistantSalerService.getAssistantSalerRank(userDetail.getJobNumber(),dataAuthDTO.getOrgCode(),dataAuthDTO.getRegionCode(),pt, DateTypeEnum.year);
    }

    @ApiOperation(value = "查询月度排名")
    @GetMapping("/rank/month")
    public AssistantSalerBasicDTO empRankMonth(@RequestParam(required = false) String pt) {

        UserDetailVO userDetail= bmsService.getContextUserDetail();
        CommonResult<DataAuthDTO> commonResult = dataAuthFacade.dataAuth(bmsService.getToken());
        if (userDetail==null || !commonResult.isSuccess()){
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201010);
        }
        DataAuthDTO dataAuthDTO = commonResult.getData();
        if (StringUtils.isEmpty(pt)){
            pt = LocalDate.now().minusDays(1).format(BaseConstants.FMT_YYYYMMDD);//昨天日期yyyyMMdd
        }
        return assistantSalerService.getAssistantSalerRank(userDetail.getJobNumber(),dataAuthDTO.getOrgCode(),dataAuthDTO.getRegionCode(),pt, DateTypeEnum.month);
    }

    @ApiOperation(value = "查询业绩趋势")
    @GetMapping("/trend/amt")
    public AssistantSalerSingleTrendDTO queryEmpAmtTrend(@RequestParam(required = false) String pt) {

        UserDetailVO userDetail= bmsService.getContextUserDetail();
        if (userDetail==null){
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201010);
        }
        if (StringUtils.isEmpty(pt)){
            pt = LocalDate.now().minusDays(1).format(BaseConstants.FMT_YYYYMMDD);//昨天日期yyyyMMdd
        }
        List<String> ptListCurrentYear = generatePtList(pt);

        return assistantSalerService.queryEmpAmtTrend(userDetail.getJobNumber(),ptListCurrentYear);
    }

    @ApiOperation(value = "查询A类客户转化趋势")
    @GetMapping("/trend/transRate")
    public AssistantSalerSingleTrendDTO queryEmpATypeCustTransRateTrend(@RequestParam(required = false) String pt) {

        UserDetailVO userDetail= bmsService.getContextUserDetail();
        if (userDetail==null){
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201010);
        }
        if (StringUtils.isEmpty(pt)){
            pt = LocalDate.now().minusDays(1).format(BaseConstants.FMT_YYYYMMDD);//昨天日期yyyyMMdd
        }
        List<String> ptListCurrentYear = generatePtList(pt);
        return assistantSalerService.queryEmpATypeCustTransRateTrend(userDetail.getJobNumber(),ptListCurrentYear);
    }

    @ApiOperation(value = "查询保费配比趋势(查询客户合作深度趋势)")
    @GetMapping("/trend/insuranceRate")
    public AssistantSalerSingleTrendDTO queryEmpATypeCustInsuranceRateTrend(@RequestParam(required = false) String pt) {

        UserDetailVO userDetail= bmsService.getContextUserDetail();
        if (userDetail==null){
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201010);
        }
        if (StringUtils.isEmpty(pt)){
            pt = LocalDate.now().minusDays(1).format(BaseConstants.FMT_YYYYMMDD);//昨天日期yyyyMMdd
        }
        List<String> ptListCurrentYear = generatePtList(pt);
        return assistantSalerService.queryEmpATypeCustInsuranceRateTrend(userDetail.getJobNumber(),ptListCurrentYear);
    }

    @ApiOperation(value = "查询客户留存趋势")
    @GetMapping("/trend/retentionRate")
    public AssistantSalerSingleTrendDTO queryEmpRetentionRate(@RequestParam(required = false) String pt) {

        UserDetailVO userDetail= bmsService.getContextUserDetail();
        if (userDetail==null){
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201010);
        }
        if (StringUtils.isEmpty(pt)){
            pt = LocalDate.now().minusDays(1).format(BaseConstants.FMT_YYYYMMDD);//昨天日期yyyyMMdd
        }
        List<String> ptListCurrentYear = generatePtList(pt);
        return assistantSalerService.queryEmpRetentionRate(userDetail.getJobNumber(),ptListCurrentYear);
    }

    private List<String> generatePtList(String pt) {
        List<String> ptListCurrentYear = new java.util.ArrayList<>();
        LocalDate localDate = LocalDate.parse(pt, BaseConstants.FMT_YYYYMMDD);
        int month = localDate.getMonthValue();
        for (int i = 1; i <= month; i++){
            if(i==month){
                ptListCurrentYear.add(pt);
                break;
            }
            ptListCurrentYear.add(localDate.minusMonths(month-i)
                    .with(TemporalAdjusters.lastDayOfMonth()).format(BaseConstants.FMT_YYYYMMDD));
        }
        return ptListCurrentYear;
    }

    public static void main(String[] args) {
        System.out.println(new AssistantSalerController().generatePtList("20240901"));
    }
}
