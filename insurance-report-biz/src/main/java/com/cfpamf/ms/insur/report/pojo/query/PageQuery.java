package com.cfpamf.ms.insur.report.pojo.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * Create By zhengjing on 2020/9/3 14:42
 */
@Data
@FieldDefaults(level = AccessLevel.PROTECTED)
public class PageQuery {

    /**
     * 第几页
     */
    @ApiModelProperty(value = "第几页")
    Integer page = 1;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量")
    Integer size = 20;

    /**
     * 是否分页
     */
    @ApiModelProperty(value = "是否count总行数", example = "true")
    boolean count = true;
}
