package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 机构佣金报表VO
 *
 * <AUTHOR>
 */
@Data
public class SmOrgCommissionReportVO implements Serializable {
    private static final long serialVersionUID = -2093834479655237675L;

    /**
     * 统计日期（始） 时间格式 yyyy-MM-dd
     */
    @ExportField(name = "统计日期（始）",order = 0)
    @ApiModelProperty("统计日期（始）*")
    private String startDate;

    @ExportField(name = "统计日期（止）" ,order = 1)
    @ApiModelProperty("统计日期（止）*")
    private String endDate;

    @ExportField(name = "区域" ,order = 2)
    @ApiModelProperty("区域*")
    private String regionName;

    @ExportField(name = "分支",order = 3)
    @ApiModelProperty("分支")
    private String orgName;

    @ExportField(name = "单量",order = 4,type = "integer")
    @ApiModelProperty("单量")
    private Integer insuredCnt;

    @ExportField(name = "保费",order = 5,type = "money")
    @ApiModelProperty("保费")
    private BigDecimal insuredAmt;

    @ExportField(name = "佣金收入",order = 6,type = "money")
    @ApiModelProperty("佣金收入")
    private BigDecimal cmsnIncome;

    @ExportField(name = "佣金支出",order = 7,type = "money")
    @ApiModelProperty("佣金支出")
    private BigDecimal cmsnExpe;
}
