package com.cfpamf.ms.insur.report.feign.resp.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class UserConfigVo {
    private String assistingSuperVision; // 协助督导
    private String branchCode; // 所属机构
    private String creditLimitIncreaseEnable; // 是否开启尽调授信 Y 是 N 否
    private String independentapprovalGrade; // 独立审批人等级
    private String isIndependentApprova; // 是否独立审批人 Y 是 N 否
    private String isLoanauditMember; // 是否贷审会成员 Y 是 N 否
    private String isLoanofficer; // 是否客户经理 Y 是 N 否
    private String jobNumber; // 统一工号
    private String loanauditmemberGrade; // 贷审会等级
    private String loanofficerGrade; // 客户经理等级 1000-见习 2000-初级 3000-中级 4000-高级
    private BigDecimal maxLoanmoney; // 月最高放款额度
    private BigDecimal onesMaxLoanmoney; // 当次最高放款额度
    private String supervisor; // 管护督导
    private String usrCde; // 员工工号
}
