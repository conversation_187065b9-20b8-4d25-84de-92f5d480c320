package com.cfpamf.ms.insur.report.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.feign.BmsFacade;
import com.cfpamf.ms.insur.report.feign.resp.vo.ModuleVO;
import com.cfpamf.ms.insur.report.feign.resp.vo.OrganizationBaseVO;
import com.cfpamf.ms.insur.report.feign.resp.vo.OrganizationParentVo;
import com.cfpamf.ms.insur.report.feign.resp.vo.UserDetailVO;
import com.cfpamf.ms.insur.report.util.HttpRequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 运营平台同步用户接口
 * @author: zhangnayi
 * @create: 2018-06-21 20:10
 **/
@Slf4j
@Service
public class BmsService {
    public static final String ERROR_PREFIX = "BMS接口调用失败";

    /**
     * bms用户接口
     */
    @Autowired
    private BmsFacade bmsFacade;




    /**
     * 获取当前登录用户所有权限
     *
     * @return
     */
    public List<ModuleVO> getContextUserPermissions() {
        Result<List<ModuleVO>> result = bmsFacade.getAuthPermissions(getToken(), 3, null);
        throwExceptionIfFail(result);
        return result.getData();
    }

    /**
     * 获取当前登录用户所有权限
     *
     * @return
     */
    public List<ModuleVO> getContextUserPermissions(String authorization) {
        Result<List<ModuleVO>> result = bmsFacade.getAuthPermissions(authorization, 3, null);
        throwExceptionIfFail(result);
        return result.getData();
    }
    /**
     * 获取用户详情信息
     *
     * @return
     */
    public UserDetailVO getContextUserDetail() {
        return getUserDetailByToken(getToken());
    }

    /**
     * 获取jwt token
     *
     * @return
     */
    public String getToken() {
        String token = HttpRequestUtil.getToken();
        if (org.apache.commons.lang3.StringUtils.isEmpty(token)) {
            throw new MSBizNormalException(ExcptEnum.GATEWAY_ERROR_TOKEN_801009.getCode(), ExcptEnum.GATEWAY_ERROR_TOKEN_801009.getMsg());
        }
        return token;
    }

    /**
     * 获取用户详情信息
     *
     * @return
     */
    public UserDetailVO getUserDetailByToken(String token) {

        Result<UserDetailVO> result = bmsFacade.getContextUserDetailForSafes(token);
        throwExceptionIfFail(result);
        UserDetailVO userDetail = result.getData();
        //if (userDetail.getOrgName() != null && userDetail.getOrgName().endsWith("办公室")) {
        //log.info("userDetail.getAreaOrgCode() = {}",userDetail.getAreaOrgCode());
        if(userDetail.getOrgName() != null && !StringUtils.isEmpty(userDetail.getAreaOrgCode())){
            userDetail.setIsHeadOrg(Boolean.TRUE);
        }
        return userDetail;
    }


    /**
     * 根据orgCode获取当前机构信息,如果当前机构是区域办公室/总部事业部则返回对应的分支机构树上的区域信息/分支事业部，其他不变
     *
     * @param orgCode
     * @return
     */
    public OrganizationBaseVO getBizOrg(String orgCode) {
        Result<OrganizationBaseVO> result = bmsFacade.getBizOrgBaseVOByOrgCode(orgCode);
        log.info("OrganizationBaseVO = {}", result.getData());
        throwExceptionIfFail(result);
        return result.getData();
    }

    /**
     * 如果接口错误跑出异常
     *
     * @param result
     */
    private void throwExceptionIfFail(Result result) {
        if (!result.isSuccess()) {
            throw new MSBizNormalException(result.getErrorCode(), ERROR_PREFIX + result.getErrorMsg());
        }
    }

    /**
     *
     * @param hrParentId
     * @return
     */
    public List<OrganizationBaseVO> listBranchsByHrParentId(Integer hrParentId){
        Result<List<OrganizationBaseVO>> result = bmsFacade.listBranchesByHrParentId("",hrParentId);
        throwExceptionIfFail(result);
        return result.getData();
    }
    public List<String> listBranchCodesByHrParentId(Integer hrParentId){
        List<OrganizationBaseVO> list = listBranchsByHrParentId(hrParentId);
        return CollectionUtils.isEmpty(list)? null:list.stream().map(OrganizationBaseVO::getOrgCode).collect(Collectors.toList());
    }

    public List<OrganizationParentVo> getParentOrganizationsByOrgCodes(List<String> orgCodes){
        Result<List<OrganizationParentVo>> result = bmsFacade.getParentOrganizationsByOrgCodes(orgCodes);
        throwExceptionIfFail(result);
        return result.getData();
    }
}
