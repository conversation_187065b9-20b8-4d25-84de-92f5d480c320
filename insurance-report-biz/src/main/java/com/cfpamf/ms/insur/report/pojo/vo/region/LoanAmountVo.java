package com.cfpamf.ms.insur.report.pojo.vo.region;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 放款金额
 *
 * <AUTHOR>
 * @date 2021/3/31 15:06
 */
@Data
public class LoanAmountVo {

    /**
     * 当月放款金额
     */
    @ApiModelProperty(value = "当月放款金额")
    private BigDecimal currentMonth;

    /**
     * 当年放款金额
     */
    @ApiModelProperty(value = "当年放款金额")
    private BigDecimal currentYear;

    /**
     * 线下当月放款金额
     */
    @ApiModelProperty(value = "线下当月放款金额")
    private BigDecimal currentMonthOffline;

    /**
     * 线下当年放款金额
     */
    @ApiModelProperty(value = "线下当年放款金额")
    private BigDecimal currentYearOffline;

    /**
     * 当月 保费/线下放款金额
     */
    @ApiModelProperty(value = "当月 保费/线下放款金额")
    private BigDecimal currentMonthOfflinePremiumProportion;

    /**
     * 当年 保费/线下放款金额
     */
    @ApiModelProperty(value = "当年 保费/线下放款金额")
    private BigDecimal currentYearOfflinePremiumProportion;

    /**
     * 当月 保费/放款金额
     */
    @ApiModelProperty(value = "当月 保费/放款金额")
    private BigDecimal currentMonthPremiumProportion;

    /**
     * 当年 保费/放款金额
     */
    @ApiModelProperty(value = "当月 保费/放款金额")
    private BigDecimal currentYearPremiumProportion;
}
