package com.cfpamf.ms.insur.report.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> 2022/9/8 11:22
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataAuthDTO {

    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    String userId;

    /**
     * 代理人Id
     */
    @ApiModelProperty(value = "代理人Id", hidden = true)
    Integer agentId;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    String regionName;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    String regionCode;

    /**
     * 机构
     */
    @ApiModelProperty(value = "机构")
    String orgName;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码")
    String orgCode;

    /**
     * 是否创新业务对接人
     */
    @ApiModelProperty(value = "机构", hidden = true)
    Boolean orgAdmin;

    @ApiModelProperty(value = "渠道", hidden = true)
    String channel;

    /**
     * 是否创新业务对接人（机构保险事物专员）
     */
    @ApiModelProperty(value = "是否创新业务对接人", hidden = true)
    Boolean picRole;

    @ApiModelProperty(value = "是否保险业务中心", hidden = true)
    public Boolean safeCenter;

    /**
     * 是否机构对接人 机构保险事物专员)
     *
     * @return
     */
    @JsonIgnore
    @ApiModelProperty(value = "是否创新业务对接人", hidden = true)
    public boolean isOrgSupport() {
        return Boolean.TRUE.equals(picRole);
    }


    /**
     * 事业部名称
     */
    @ApiModelProperty("事业部名称")
    String branchBuName;

    /**
     * 事业部code
     */
    @ApiModelProperty("事业部code")
    String branchBuCode;

    /**
     * 片区名称（片区/直管）
     */
    @ApiModelProperty("片区名称")
    String zoneName;

    /**
     * 片区code（片区/直管）
     */
    @ApiModelProperty("片区code")
    String zoneCode;

    /**
     * 分支路径
     */
    @ApiModelProperty(value = "分支路径")
    String orgPath;

    @ApiModelProperty("是否分支角色")
    Boolean branch;

}