package com.cfpamf.ms.insur.report.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.feign.WhaleOrderClient;
import com.cfpamf.ms.insur.report.feign.req.CustVisitCountQueryInput;
import com.cfpamf.ms.insur.report.feign.req.CustVisitListQueryInput;
import com.cfpamf.ms.insur.report.feign.resp.WhaleResp;
import com.cfpamf.ms.insur.report.feign.resp.vo.CustVisitCountQueryOutput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WhaleOrderService {

    public static final String ERROR_PREFIX = "BMS接口调用失败";

    @Value("${whale.aid}")
    private String aid;

    private String defaultVersion = "v1.0";

    @Autowired
    private WhaleOrderClient whaleOrderClient;

    /**
     * 获取访客分组信息
     * @param customerGroupVisitGroupQueryInput
     * @return
     */
    public List<CustVisitCountQueryOutput> queryCurrentDayCustVisitInfoCount(CustVisitCountQueryInput customerGroupVisitGroupQueryInput) {
        log.info("queryCurrentDayCustVisitInfoCount req= {}",JSON.toJSONString(customerGroupVisitGroupQueryInput));
        WhaleResp<List<CustVisitCountQueryOutput>> result = whaleOrderClient.queryCurrentDayCustVisitInfoCount(aid,defaultVersion, customerGroupVisitGroupQueryInput);
        if (result.isSuccess()) {
            return result.getData();
        }
        log.warn("queryCurrentDayCustVisitInfoCount result= {}",JSON.toJSONString(result));
        throw new MSBizNormalException("","获取访客分组信息失败，请重试");
    }

    /**
     * 获取访客记录
     * @param custVisitListQueryInput
     * @return
     */
    public Integer queryCustVisitCount(CustVisitListQueryInput custVisitListQueryInput) {
        log.info("queryCustVisitCount req= {}", JSON.toJSONString(custVisitListQueryInput));
        WhaleResp<Integer> result = whaleOrderClient.queryCustVisitCount(aid,defaultVersion, custVisitListQueryInput);
        if (result.isSuccess()) {
            return result.getData();
        }
        log.warn("queryCustVisitCount result= {}",JSON.toJSONString(result));
        return 0;
    }
}
