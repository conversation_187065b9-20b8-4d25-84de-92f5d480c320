package com.cfpamf.ms.insur.report.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * Create By zhengjing on 2020/9/15 15:33
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LookBackEventVO {

    @ApiModelProperty("")
    String serverreceivedate;
    @ApiModelProperty("")
    Long serverreceivetime;
    @ApiModelProperty("")
    String version;
    @ApiModelProperty("")
    String os;
    @ApiModelProperty("")
    String userid;
    @ApiModelProperty("")
    String platform;
    @ApiModelProperty("")
    String appid;
    @ApiModelProperty("")
    String eventname;
    @ApiModelProperty("")
    String eventattr;
    @ApiModelProperty("")
    String urlname;
    @ApiModelProperty("")
    String begintime;
    @ApiModelProperty("")
    String beginmsec;
    @ApiModelProperty("")
    String tracename;
    @ApiModelProperty("")
    String tracecode;
    @ApiModelProperty("")
    String ossurl;
    @ApiModelProperty("")
    String orderid;
    @ApiModelProperty("")
    String usermobile;
    @ApiModelProperty("")
    String wxopenid;
    @ApiModelProperty("")
    String isshare;
    @ApiModelProperty("")
    String productversion;
    @ApiModelProperty("")
    String buyplatform;
    @ApiModelProperty("")
    String pt;
}
