package com.cfpamf.ms.insur.report.pojo.vo.personnel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 保费统计
 *
 * <AUTHOR>
 * @date 2021/3/30 9:59
 */
@Data
public class PremiumStatisticsVo {
    /**
     * 日 保费
     */
    @ApiModelProperty(value = "日 保费")
    private BigDecimal day;

    /**
     * 月 保费
     */
    @ApiModelProperty(value = "月 保费")
    private BigDecimal month;

    /**
     * 年 保费
     */
    @ApiModelProperty(value = "年 保费")
    private BigDecimal year;
}
