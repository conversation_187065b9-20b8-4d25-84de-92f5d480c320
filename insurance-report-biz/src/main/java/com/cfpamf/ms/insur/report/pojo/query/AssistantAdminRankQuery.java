package com.cfpamf.ms.insur.report.pojo.query;

import com.google.common.collect.Sets;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2024/2/22 14:51
 */
@Data
@ApiModel("管理助手查询对象-表格")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantAdminRankQuery extends AssistantAdminQuery {

    static final Set<String> AMT_SM_COLS = Sets.newHashSet(
            // 当月标准保费
            "sm_assess_convert_insurance_amt",
            // 当年标准保费
            // 当月标准保费目标
            "sm_assess_convert_insurance_amt_target",
            // 当年标准保费目标
            // 当月标准保费达成率
            "sm_assess_convert_insurance_amt_achieve_rate"
            // 当年标准保费达成率
    );


    /**
     * 异业保费配比相关字段
     */
    static final Set<String> OFFLINE_LOAN_INSURANCE_RATE = Sets.newHashSet(
            // 当月异业保费配比
            "sm_offline_loan_insurance_rate",
            // 当年异业保费配比
            "sy_offline_loan_insurance_rate",
            // 当月异业保费配比目标
            "sm_offline_loan_insurance_rate_target",
            // 当年异业保费配比目标
            "sy_offline_loan_insurance_rate_target"
    );


    @ApiModelProperty("名称检索")
    String dimName;

    @NotEmpty(message = "排序列不能为空")
    @ApiModelProperty("排序字段 注意这个是按传入数组的字段顺序 先后排序")
    List<OrderByClause> orderByClauses;


    @NotEmpty(message = "查询列不能为空")
    @ApiModelProperty("需要查询的列")
    List<String> selectCols;


    @ApiModelProperty(hidden = true)
    String dimCol;

    /**
     * 将排序条件转换为SQL语句
     *
     * @return SQL语句
     */
    public String toOrderBySql() {
        // 如果排序条件为空，则返回null
        if (orderByClauses == null || orderByClauses.isEmpty()) {
            return null;
        } else {
            // 将排序条件转换为SQL语句并拼接
            return orderByClauses.stream().map(OrderByClause::toSql).collect(Collectors.joining(",", " order by pt,", " "));
        }
    }

    /**
     * 获取维度名称的方法。
     * 这个方法会去除维度名称两端的空白字符。
     *
     * @return 返回处理后的维度名称字符串。
     */
    public String getDimName() {
        return StringUtils.trim(dimName);
    }

    /**
     * 将排序条件转换为SQL语句
     * <p>
     * 该方法不接受参数，主要用于将内部维护的排序条件转换成SQL语句中的列部分。
     * 如果排序条件为空，则不返回任何SQL语句。
     *
     * @return 返回拼接好的SQL语句字符串，如果排序条件为空则返回null。
     */
    public String toColSql() {
        // 检查排序条件是否为空，如果为空则直接返回null
        if (orderByClauses == null || orderByClauses.isEmpty()) {
            return null;
        } else {
            // 将排序条件中的每个列名转换为SQL语句的一部分，特别是对特定的列名进行处理
            return selectCols.stream()
                    .map(s -> {
                        // 如果是异业保费配比相关的字段除以 10000 因为标准的口径是 万元配比
                        if (OFFLINE_LOAN_INSURANCE_RATE.contains(s)) {
                            return "round("+s + ",4) as " + s;
                        }
                        // 对于其他列名，直接返回列名
                        return s;
                    }).collect(Collectors.joining(","));
        }
    }

    @ApiModelProperty("区域下是否有片区")
    boolean existDistrict;
}

