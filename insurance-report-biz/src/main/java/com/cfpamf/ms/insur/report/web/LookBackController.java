package com.cfpamf.ms.insur.report.web;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.pojo.query.LookBackQuery;
import com.cfpamf.ms.insur.report.pojo.vo.LookBackEventVO;
import com.cfpamf.ms.insur.report.pojo.vo.OrderDetailVO;
import com.cfpamf.ms.insur.report.pojo.vo.OrderVO;
import com.cfpamf.ms.insur.report.service.LookBackService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * Create By zhengjing on 2020/9/15 16:09
 */
@Slf4j
@Api(value = "LookBackController", tags = "回溯管理")
@RestController
@RequestMapping("/back/lookback")
public class LookBackController {

    @Autowired
    LookBackService lookBackService;

    @ApiOperation("查询回溯图片")
    @GetMapping("/events/{orderNo}")
    public List<LookBackEventVO> events(@PathVariable("orderNo") String orderId,
                                        @ApiParam(value = "提交时间", example = "2020-09-14")
                                        @RequestParam("subDate") LocalDate subDate,
                                        @ApiParam(value = "支付时间", example = "2020-09-14")
                                        @RequestParam("payDate") LocalDate payDate) {
        log.info("开始查询投保流程回溯信息:{},{},{}", orderId, subDate, payDate);
        List<LookBackEventVO> data = null;
        try {
            return lookBackService.events(orderId, subDate, payDate);
        } catch (Exception e) {
            log.warn("查询回溯列表失败:{}", orderId, e);
            throw new MSBizNormalException("-1", "系统查询回溯轨迹超时,请稍后重试...");
        }
    }

    @GetMapping("/order/{orderNo}/_detail")
    public OrderDetailVO orderDetail(@PathVariable("orderNo") String orderNo) {
        return lookBackService.detail(orderNo);

    }

    @ApiOperation("查询回溯订单")
    @GetMapping("/orders")
    public PageInfo<OrderVO> vos(LookBackQuery query) {
        return lookBackService.pageQuery(query);
    }

}
