package com.cfpamf.ms.insur.report.dao.safepg;

import com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo;
import com.cfpamf.ms.insur.report.service.diagnosis.DiagnosisAndConclusionService;
import com.cfpamf.ms.insur.report.service.diagnosis.InsuranceAmountRateSummaryService;
import com.cfpamf.ms.insur.report.service.diagnosis.RetentionRateSummaryService;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 保险助手片区集市;(ads_insurance_district_marketing_progress_dfp)表数据库访问层
 * <AUTHOR> wanghao
 * @date : 2024-2-28
 */
@Mapper
public interface AdsInsuranceDistrictMarketingProgressDfpMapper {
    /**
     * 查询保险助手片区的诊断数据信息
     * @param pt 分区
     * @param orgCodes 片区编码
     * @return 保险助手片区的诊断数据信息
     */
    DiagnosisAndConclusionService.DiagnosisAndConclusionVo queryDiagnosisData(String pt, List<String> orgCodes);

    /**
     * 查询管理助手小结-业绩目标是否达标
     *
     * @param pt       分区
     * @param orgCodes 片区编码
     * @return 目标是否达标
     */
    Boolean queryAssessConvertInsuranceAmtSummary(String pt, List<String> orgCodes);

    /**
     * 查询管理助手小结-异业保费配比是否达标
     *
     * @param pt       分区
     * @param orgCodes 片区编码
     * @return 目标是否达标
     */
    InsuranceAmountRateSummaryService.InsuranceAmountRateSummaryVo queryInsuranceAmountRateSummary(String pt, List<String> orgCodes);

    /**
     * 查询管理助手小结-客户留存率是否达标
     *
     * @param pt 分区
     * @param orgCodes 片区编码
     * @return
     */
    RetentionRateSummaryService.RetentionRateSummaryVo queryRetentionRateSummary(String pt, List<String> orgCodes);

    /**
     * 查询排名信息
     *
     * @param pt                           分区
     * @param assessConvertInsuranceAmtTag 标准保费达成率标识
     * @param offlineLoanInsuranceRateTag  异业保费配比达成率标识
     * @param insuranceRetentionRateTag
     * @param orgCodes                     组织机构代码
     * @param orgLevel                     组织级别
     * @return
     */
    List<DiagnosisRankInfoVo> queryRankInfo(String pt, boolean assessConvertInsuranceAmtTag, boolean offlineLoanInsuranceRateTag, boolean insuranceRetentionRateTag, List<String> orgCodes, String orgLevel);

    /**
     * 查询业务小助手中诊断相关的数据
     *
     * @param pt                           分区
     * @param assessConvertInsuranceAmtTag 标准保费达成率标识
     * @param orgCodes                     组织机构代码
     * @param orgLevel                     组织级别
     * @return 诊断数据
     */
    List<DiagnosisRankInfoVo> queryAssessConvertInsuranceAmtSummaryRankInfo(String pt, Boolean assessConvertInsuranceAmtTag, List<String> orgCodes, String orgLevel);

    /**
     * 查询管理助手小结-异业保费配比的排名信息
     *
     * @param pt                     分区
     * @param insuranceAmountRateTag 异业保费配比是否达标
     * @param orgCodes               组织机构编码，区域或者片区
     * @param orgLevel               组织机构级别
     * @return 排名信息
     */
    List<DiagnosisRankInfoVo> queryInsuranceAmountRateSummaryRankInfo(String pt, boolean insuranceAmountRateTag, List<String> orgCodes, String orgLevel);

    /**
     * 查询客户留存率小结的排名信息(片区诊断)
     *
     * @param pt               分区
     * @param retentionRateTag 客户留存率是否达标的标识
     * @param orgCodes         组织编码，区域或片区的编码
     * @param orgLevel         组织级别
     * @return 排名信息
     */
    List<DiagnosisRankInfoVo> queryRetentionRateSummaryRankInfo(String pt, boolean retentionRateTag, List<String> orgCodes, String orgLevel);
}
