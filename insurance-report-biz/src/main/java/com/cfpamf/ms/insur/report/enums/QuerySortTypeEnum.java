package com.cfpamf.ms.insur.report.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@AllArgsConstructor
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum QuerySortTypeEnum {
    insuredCnt("insuredCnt","单量"),
    insuredAmt("insuredAmt","保费"),
    achievementRate("achievementRate","达成率"),
    endCnt("endCnt","到期客户数"),
    renewalCnt("renewalCnt","续保客户数"),
    renewalRate("renewalRate","续保率"),
    avgInsuranceAmtMonthly("avgInsuranceAmtMonthly","人均保费"),
    planEmpAverageAmt("planEmpAverageAmt","编制人均保费"),
    acmAvgInsuranceAmtMonthly("acmAvgInsuranceAmtMonthly","累计月度人均保费"),



    ;


    private String code;

    private String desc;

    /**
     * 微信端个人业绩排序类型
     * @return
     */
    public static List<QuerySortTypeEnum> getWxPersonalSortType(){
        return Arrays.asList(
                QuerySortTypeEnum.insuredCnt, QuerySortTypeEnum.insuredAmt,
                QuerySortTypeEnum.renewalRate,QuerySortTypeEnum.endCnt,
                QuerySortTypeEnum.renewalCnt);
    }
    /**
     * 微信端机构业绩排序类型
     * @return
     */
    public static List<QuerySortTypeEnum> getWxOrgSortType(){
        return Arrays.asList(
                QuerySortTypeEnum.insuredCnt, QuerySortTypeEnum.insuredAmt,
                QuerySortTypeEnum.achievementRate,QuerySortTypeEnum.renewalRate,
                QuerySortTypeEnum.avgInsuranceAmtMonthly,QuerySortTypeEnum.acmAvgInsuranceAmtMonthly);
    }
    /**
     * 微信端区域业绩排序类型
     * @return
     */

    public static List<QuerySortTypeEnum> getWxRegionSortType(){
        return Arrays.asList(
                QuerySortTypeEnum.insuredCnt, QuerySortTypeEnum.insuredAmt,
                QuerySortTypeEnum.achievementRate,QuerySortTypeEnum.renewalRate,
                QuerySortTypeEnum.avgInsuranceAmtMonthly,QuerySortTypeEnum.planEmpAverageAmt);
    }
}
