package com.cfpamf.ms.insur.report.service.admin;

import com.cfpamf.ms.insur.report.pojo.query.Pageable;
import com.github.pagehelper.PageHelper;

/**
 * <AUTHOR>
 * @date 2021/4/12 20:51
 */
public class BaseService {

    /**
     * 处理分页信息
     *
     * @param pageable 分页信息
     */
    public void handlePage(Pageable pageable) {
        if (!pageable.isAll()) {
            PageHelper.startPage(pageable.getPage(), pageable.getSize());
        }
    }
}
