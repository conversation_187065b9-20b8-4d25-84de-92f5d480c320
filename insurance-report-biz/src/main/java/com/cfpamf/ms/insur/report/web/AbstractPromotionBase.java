package com.cfpamf.ms.insur.report.web;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.enums.EnumAssistantAreaDim;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.pojo.dto.DataAuthDTO;
import com.cfpamf.ms.insur.report.pojo.query.AssistantAdminQuery;
import com.cfpamf.ms.insur.report.pojo.query.AssistantManagerPromotionQuery;
import com.cfpamf.ms.insur.report.service.BmsService;
import com.cfpamf.ms.insur.report.service.DataAuthService;
import com.cfpamf.ms.insur.report.service.wx.CommonToolService;
import com.cfpamf.ms.insur.report.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractPromotionBase {

    @Autowired
    private DataAuthService dataAuthService;

    @Autowired
    private CommonToolService commonToolService;

    protected final String getPtDay(){
        return commonToolService.getMaxAvailablePtForTables(null);
    }

//    private void checkDataAuth(AssistantManagerPromotionQuery query) {
//        // 获取数据权限信息
//        DataAuthDTO dataAuth = dataAuthService.dataAuth();
//
//        // 如果区域名称不为空，则设置查询对象的区域名称
//        if (StringUtils.isNotBlank(dataAuth.getRegionName())) {
//            if (query.getAreaDim().ordinal() < EnumAssistantAreaDim.AREA.ordinal()) {
//                throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
//            }
//            query.setAreaName(dataAuth.getRegionName());
//            query.setAreaCodeDataAuth(dataAuth.getRegionCode());
//        }
//
//        // 如果组织名称不为空，则设置查询对象的组织名称
//        if (StringUtils.isNotBlank(dataAuth.getOrgName())) {
//            //如果是分支角色 但是查询分支以上的数据 报错
//            if (query.getAreaDim().ordinal() < EnumAssistantAreaDim.BRANCH.ordinal()) {
//                throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
//            }
//            query.setBchName(dataAuth.getOrgName());
//            query.setBchCodeDataAuth(dataAuth.getOrgCode());
//        }
//
//        // 如果用户ID不为空，则抛出无数据权限异常
//        if (StringUtils.isNotBlank(dataAuth.getUserId())) {
//            throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
//        }
//    }
}
