
package com.cfpamf.ms.insur.report.pojo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/15 14:51
 */
@Data
@ApiModel("保险销售助手-员工报表查询对象")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ManagerRankPromotionQuery extends PromotionBaseQuery{
    /**
     * 数据类型 share 分享, visit
     */
    @ApiModelProperty(value = "数据类型 share 分享, visit")
    String dataType;

    @ApiModelProperty("排名类型 nation 全国, area 区域")
    String rankType;

    @ApiModelProperty("时间分区")
    String pt;

    @ApiModelProperty("员工编码")
    String empId;

    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "当月分享数", notes = "")
    Integer smShareCnt;
    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "当月访客数", notes = "")
    Integer smVisitCnt;

    @ApiModelProperty(value = "区域编码", notes = "")
    String areaCode;

    @ApiModelProperty("片区编码")
    List<String> districtCodeList;

    @ApiModelProperty("分支编码")
    String bchCode;
}
