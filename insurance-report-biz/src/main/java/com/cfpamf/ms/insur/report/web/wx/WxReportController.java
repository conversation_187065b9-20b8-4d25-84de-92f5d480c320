package com.cfpamf.ms.insur.report.web.wx;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.enums.QuerySortTypeEnum;
import com.cfpamf.ms.insur.report.enums.ReportType;
import com.cfpamf.ms.insur.report.pojo.query.WxReportQuery;
import com.cfpamf.ms.insur.report.pojo.vo.WxOrgBusinessVO;
import com.cfpamf.ms.insur.report.pojo.vo.WxPersonalBusinessVO;
import com.cfpamf.ms.insur.report.pojo.vo.WxRegionBusinessVO;
import com.cfpamf.ms.insur.report.service.wx.WxReportService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@Api(value = "保险微信端业务报表接口", tags = {"保险微信端业务报表接口"})
@RequestMapping(BaseConstants.WX_VERSION + "/report/2021")
public class WxReportController {
    @Autowired
    private WxReportService wxReportService;
    /**
     * 查询微信个人业绩报表 2021
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询微信个人业绩报表2021")
    @PostMapping("/businessDetails/personal")
    public PageInfo<WxPersonalBusinessVO> listWxPersonalBusinessDetailsByPage(@RequestBody WxReportQuery query) {
        return   wxReportService.listWxPersonalBusinessDetailsByPage(query);

    }

    /**
     * 查询微信机构业绩报表 2021
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询微信机构业绩报表2021")
    @PostMapping("/businessDetails/org")
    public PageInfo<WxOrgBusinessVO> listWxOrgBusinessDetailsByPage(@RequestBody WxReportQuery query) {
        return   wxReportService.listWxOrgBusinessDetailsByPage(query);

    }

    /**
     * 查询微信区域业绩报表 2021
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询微信区域业绩报表2021")
    @PostMapping("/businessDetails/region")
    public PageInfo<WxRegionBusinessVO> listWxRegionBusinessDetailsByPage(@RequestBody WxReportQuery query) {
        return   wxReportService.listWxRegionBusinessDetailsByPage(query);

    }

    /**
     * 查询微信端业绩报表排序类型 2021
     *
     * @param reportType
     * @return
     */
    @ApiOperation(value = "查询微信端业绩报表排序类型2021")
    @PostMapping("/businessDetails/{reportType}/sortType")
    public List<QuerySortTypeEnum> getBusinessDetailsSortType(@PathVariable("reportType") String reportType) {
        if(Objects.equals(reportType, ReportType.ORG.getCode())) {
            return QuerySortTypeEnum.getWxOrgSortType();
        }else if(Objects.equals(reportType,ReportType.PERSONAL.getCode())){
            return QuerySortTypeEnum.getWxPersonalSortType();
        }else if(Objects.equals(reportType,ReportType.REGION.getCode())){
            return QuerySortTypeEnum.getWxRegionSortType();
        }else{
            throw new MSBizNormalException("9999999","微信端业绩报表排序类型没有该类型"+reportType);
        }
    }

}
