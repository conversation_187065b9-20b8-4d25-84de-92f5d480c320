package com.cfpamf.ms.insur.report.dao.safepg;

import com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo;
import com.cfpamf.ms.insur.report.service.diagnosis.DiagnosisAndConclusionService;
import com.cfpamf.ms.insur.report.service.diagnosis.InsuranceAmountRateSummaryService;
import com.cfpamf.ms.insur.report.service.diagnosis.RetentionRateSummaryService;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 保险助手区域集市;(ads_insurance_area_marketing_progress_dfp)表数据库访问层
 *
 * <AUTHOR> wanghao
 * @date : 2024-2-28
 */
@Mapper
public interface AdsInsuranceAreaMarketingProgressDfpMapper {
    /**
     * 查询排名信息
     *
     * @param pt                           分区
     * @param assessConvertInsuranceAmtTag 标准保费是否达标标识
     * @param offlineLoanInsuranceRateTag  异业保费配比是否达标标识
     * @param insuranceRetentionRateTag
     * @return 排名信息结果
     */
    List<DiagnosisRankInfoVo> queryRankInfo(String pt, boolean assessConvertInsuranceAmtTag, boolean offlineLoanInsuranceRateTag, boolean insuranceRetentionRateTag);

    /**
     * 查询诊断数据
     *
     * @param pt       分区
     * @param orgCodes 组织编码
     * @return 诊断数据
     */
    DiagnosisAndConclusionService.DiagnosisAndConclusionVo queryDiagnosisData(String pt, List<String> orgCodes);

    /**
     * 查询业绩达标小结的排名信息
     *
     * @param pt                           分区
     * @param assessConvertInsuranceAmtTag 业绩是否达标的标识
     * @return 排名信息
     */
    List<DiagnosisRankInfoVo> queryAssessConvertInsuranceAmtSummaryRankInfo(String pt, Boolean assessConvertInsuranceAmtTag);

    /**
     * 查询管理助手小结-业绩目标是否达标
     *
     * @param pt       分区
     * @param orgCodes 区域编码
     * @return 目标是否达标
     */
    Boolean queryAssessConvertInsuranceAmtSummary(String pt, List<String> orgCodes);

    /**
     * 查询诊断数据
     *
     * @param pt       分区
     * @param orgCodes 组织编码
     * @return 诊断数据
     */
    InsuranceAmountRateSummaryService.InsuranceAmountRateSummaryVo queryInsuranceAmountRateSummary(String pt, List<String> orgCodes);

    /**
     * 查询异业保费配比小结的排名信息
     *
     * @param pt                     分区
     * @param insuranceAmountRateTag 异业保费配比是否达标的标识
     * @return 排名信息
     */
    List<DiagnosisRankInfoVo> queryInsuranceAmountRateSummaryRankInfo(String pt, boolean insuranceAmountRateTag);

    /**
     * 查询管理助手小结-客户留存率是否达标
     *
     * @param pt       分区
     * @param orgCodes 区域编码
     * @return
     */
    RetentionRateSummaryService.RetentionRateSummaryVo queryRetentionRateSummary(String pt, List<String> orgCodes);

    /**
     * 查询客户留存率小结的排名信息
     *
     * @param pt               分区
     * @param retentionRateTag 客户留存率是否达标的标识
     * @return 排名信息
     */
    List<DiagnosisRankInfoVo> queryRetentionRateSummaryRankInfo(String pt, boolean retentionRateTag);
}
