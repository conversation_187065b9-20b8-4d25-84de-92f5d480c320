package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@ApiModel(value = "保险销售端推广指标排名对象", description = "")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantSalerPromotionRankDTO {

    @ApiModelProperty(value = "我的当月排名", notes = "")
    String mineSmRank="50";

    @ApiModelProperty(value = "我的当月排名", notes = "")
    String mineSmComparativeQuarterlyRank;

    @ApiModelProperty(value = "0 不升不降, 1 上升, 2 下降", notes = "")
    Integer rankStatus;

    @ApiModelProperty(value = "当月排名列表", notes = "")
    List<AssistantSalerPromotionRankInfo> assistantSalerPromotionRankInfoList;

    @ApiModelProperty(value = "头像地址", notes = "")
    String url;

    @ApiModelProperty(value = "分享/访客排名", notes = "")
    private Integer rank;

    /**
     * 数据类型 share 分享, visit
     */
    @ApiModelProperty(value = "数据类型 share 分享, visit")
    String dataType;

    @ApiModelProperty("nation 全国, area 区域, district 片区, bch 分支")
    String levelType;
}
