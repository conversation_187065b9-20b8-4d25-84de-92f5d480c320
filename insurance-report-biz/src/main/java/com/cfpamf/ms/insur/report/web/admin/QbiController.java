package com.cfpamf.ms.insur.report.web.admin;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.service.QuickBiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(value = "QBI报表接口", tags = {"QBI报表接口"})
@RequestMapping(BaseConstants.ADMIN_VERSION + "/Qbi")
public class QbiController {

    @Autowired
    QuickBiService quickBiService;

    @ApiOperation(value = "获取配置接口鉴权的系统")
    @GetMapping(value = "genarateQbiUrl")
    public Result<String> genarateQbiUrl(
                                         @RequestParam(value = "PageId") String PageId,
                                         @RequestParam(value = "isDashboard") Integer isDashboard) {
        return new Result<String>(true, null, quickBiService.generateQbiUrl(PageId,isDashboard));
    }
    @ApiOperation(value = "提供给小鲸")
    @GetMapping(value = "genarateQbiUrlXiaoJin")
    public Result<String> genarateQbiUrlXiaoJin(
            @RequestParam(value = "PageId") String PageId,
            @RequestParam(value = "isDashboard") Integer isDashboard) {
        return new Result<String>(true, null, quickBiService.generateQbiUrl1(PageId,isDashboard));
    }
}
