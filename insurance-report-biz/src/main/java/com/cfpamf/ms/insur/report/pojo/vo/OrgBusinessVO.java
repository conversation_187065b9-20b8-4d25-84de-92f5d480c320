package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import com.cfpamf.ms.insur.report.pojo.vo.EmployeeStatisiticVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class OrgBusinessVO extends EmployeeStatisiticVO implements Serializable {
    @ExportField(name = "分支机构",order = 3)
    @ApiModelProperty("分支机构")
    private String orgName;


}
