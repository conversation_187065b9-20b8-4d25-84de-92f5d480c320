package com.cfpamf.ms.insur.report.pojo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/15 14:51
 */
@Data
@ApiModel("保险销售助手-管理报表查询对象")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantManagerPromotionLevelQuery extends PromotionBaseQuery{

    @ApiModelProperty("区域编码")
    String areaCode;

    @ApiModelProperty("片区编码")
    List<String> districtCodeList;

    @ApiModelProperty("分支编码")
    String bchCode;

    @ApiModelProperty("nation 全国, area 区域, district 片区, bch 分支")
    String levelType;

    @ApiModelProperty("时间分区")
    String pt;
}
