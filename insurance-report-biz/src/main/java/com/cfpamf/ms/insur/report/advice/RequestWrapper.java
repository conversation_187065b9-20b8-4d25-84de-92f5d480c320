package com.cfpamf.ms.insur.report.advice;

import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.util.Objects;

/**
 * 查写HttpServletRequest
 * 前端参数传"", 后端应该读取null
 *
 * <AUTHOR>
 */
public class RequestWrapper extends HttpServletRequestWrapper {

    public RequestWrapper(HttpServletRequest request) {
        super(request);
    }

    /**
     * 覆盖  getParameter
     *
     * @param name
     * @return
     */
    @Override
    public String getParameter(String name) {
        String value = super.getParameter(name);
        return StringUtils.isEmpty(value) ? null : value;
    }

    /**
     * 覆盖  getAttribute
     *
     * @param name
     * @return
     */
    @Override
    public Object getAttribute(String name) {
        Object att = super.getAttribute(name);
        if (Objects.equals(att, "")) {
            return null;
        }
        return att;
    }

    /**
     * 覆盖  getParameterValues
     *
     * @param name
     * @return
     */
    @Override
    public String[] getParameterValues(String name) {
        String[] values = super.getParameterValues(name);
        if (values != null && values.length > 0) {
            values[0] = StringUtils.isEmpty(values[0]) ? null : values[0];
        }
        return values;
    }
}
