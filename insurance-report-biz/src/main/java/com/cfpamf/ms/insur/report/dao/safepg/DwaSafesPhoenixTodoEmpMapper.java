package com.cfpamf.ms.insur.report.dao.safepg;

import java.util.List;

import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveBasicDTO;
import com.cfpamf.ms.insur.report.pojo.po.DwaSafesPhoenixTodoEmp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

/**
 * 员工待办指标;(dwa_safes_phoenix_todo_emp)表数据库访问层
 * <AUTHOR> zxk
 * @Date : 2024-2-29
 */
@Mapper
public interface DwaSafesPhoenixTodoEmpMapper{
    /**
     * 通过ID查询单条数据
     *
     * @param empCode 员工编号
     * @param pt 分区
     * @return 实例对象
     */
    DwaSafesPhoenixTodoEmp queryByEmpCode(String empCode,String pt);

    /**
     * 根据员工列表和分区查询员工待办指标
     * @param empCodes 员工工号列表
     * @param bchCode 分支
     * @param pt 分区
     * @return DailyRetrospectiveBasicDTO
     */
    List<DailyRetrospectiveBasicDTO> getCustomerFollowMetrics(List<String> empCodes,String bchCode,String pt);

}