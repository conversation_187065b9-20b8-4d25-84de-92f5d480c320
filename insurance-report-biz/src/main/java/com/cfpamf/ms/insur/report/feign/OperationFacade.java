package com.cfpamf.ms.insur.report.feign;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.report.feign.req.CustomerConversionQuery;
import com.cfpamf.ms.insur.report.feign.req.CustomerFollowCntQuery;
import com.cfpamf.ms.insur.report.feign.req.QueryCustomerConvertPremium;
import com.cfpamf.ms.insur.report.feign.resp.dto.CustomerConversionDto;
import com.cfpamf.ms.insur.report.feign.resp.dto.CustomerConvertPremiumDto;
import com.cfpamf.ms.insur.report.feign.resp.dto.CustomerFollowCntDto;
import com.cfpamf.ms.insur.report.feign.resp.dto.PolicyConversionAmtDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "insurance-operation", url = "${insurance-operation.url}")
public interface OperationFacade {

    @ApiOperation(value = "获取客户跟进数")
    @PostMapping("/back/customer/getCustomerFollowCnt")
    public Result<List<CustomerFollowCntDto>> getCustomerFollowCnt(@RequestBody CustomerFollowCntQuery query);

    @ApiOperation(value = "获取客户经理业绩指标")
    @PostMapping("/back/customer/getCustomerConversion")
    public Result<List<CustomerConversionDto>> getCustomerConversion(@RequestBody CustomerConversionQuery query);

    @ApiOperation(value = "获取客户保单标准保费")
    @PostMapping("/back/customer/getPolicyListForConversionAmt")
    public Result<List<PolicyConversionAmtDto>> getPolicyListForConversionAmt(@RequestBody CustomerConversionQuery query);

    @ApiOperation(value = "获取客户保单标准保费")
    @PostMapping("/back/retrospective/customer/getCustomerConvertPremium")
    Result<List<CustomerConvertPremiumDto>> getCustomerConvertPremium(@RequestBody QueryCustomerConvertPremium query);
}
