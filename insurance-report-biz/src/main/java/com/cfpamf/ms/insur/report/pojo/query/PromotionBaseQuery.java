package com.cfpamf.ms.insur.report.pojo.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PromotionBaseQuery {

    /**
     * 第几页
     */
    @ApiModelProperty(value = "-1查所有数据,默认值 1 第一页")
    Integer page = 1;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量 默认值20")
    Integer size = 20;

    @ApiModelProperty("排序字段")
    private OrderByClause orderType;
}
