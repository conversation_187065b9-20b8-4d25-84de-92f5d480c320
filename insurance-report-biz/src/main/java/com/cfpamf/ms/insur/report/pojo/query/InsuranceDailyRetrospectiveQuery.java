package com.cfpamf.ms.insur.report.pojo.query;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("管理助手查询对象")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class InsuranceDailyRetrospectiveQuery {
    @ApiModelProperty("员工工号列表,与分支督导编号不同同时为空")
    private List<String> empCodes;

    @ApiModelProperty("员工姓名列表,与分支督导编号不同同时为空")
    private List<String> empNames;

    @ApiModelProperty(value = "日期分区",required = true)
    private String date;

    @ApiModelProperty("分支编码")
    private String bchCode;

    @ApiModelProperty("分支督导编号，与员工列表不同同时为空")
    private String supervisorCode;
}
