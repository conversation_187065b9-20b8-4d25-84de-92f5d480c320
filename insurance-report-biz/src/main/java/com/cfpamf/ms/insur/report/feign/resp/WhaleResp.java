package com.cfpamf.ms.insur.report.feign.resp;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> 2022/3/16 10:28
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WhaleResp<T>  {

    Integer code;

    String msg;

    Boolean success;

    T data;

    /**
     * 序列化原文
     */
    String content;

    public boolean isSuccess() {
        return StringUtils.startsWith(code + "", "200");
    }

    public String errorMsg() {
        return msg;
    }


}

