package com.cfpamf.ms.insur.report.pojo.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 保险租售员工集市;
 * <AUTHOR> zxk
 * @date : 2024-2-26
 */
@Data
@ApiModel(value = "保险租售员工集市",description = "")
public class AdsInsuranceEmpMarketingProgressDfp implements Serializable,Cloneable{
    /** 员工姓名 */
    @ApiModelProperty(name = "员工姓名",notes = "")
    private String empName ;
    /** 工号 */
    @ApiModelProperty(name = "工号",notes = "")
    private String empCode ;
    /** 分支 */
    @ApiModelProperty(name = "分支",notes = "")
    private String bchName ;
    /** 分支编码 */
    @ApiModelProperty(name = "分支编码",notes = "")
    private String bchCode ;
    /** 片区 */
    @ApiModelProperty(name = "片区",notes = "")
    private String districtName ;
    /** 片区编码 */
    @ApiModelProperty(name = "片区编码",notes = "")
    private String districtCode ;
    /** 区域 */
    @ApiModelProperty(name = "区域",notes = "")
    private String areaName ;
    /** 区域编码 */
    @ApiModelProperty(name = "区域编码",notes = "")
    private String areaCode ;
    /** 当月标准保费 */
    @ApiModelProperty(name = "当月标准保费",notes = "")
    private Double smAssessConvertInsuranceAmt ;
    /** 当年标准保费 */
    @ApiModelProperty(name = "当年标准保费",notes = "")
    private Double syAssessConvertInsuranceAmt ;
    /** 当月标准保费目标 */
    @ApiModelProperty(name = "当月标准保费目标",notes = "")
    private Double smAssessConvertInsuranceAmtTarget ;
    /** 当年标准保费目标 */
    @ApiModelProperty(name = "当年标准保费目标",notes = "")
    private Double syAssessConvertInsuranceAmtTarget ;
    /** 当月标准保费达成率 */
    @ApiModelProperty(name = "当月标准保费达成率",notes = "")
    private Double smAssessConvertInsuranceAmtAchieveRate ;
    /** 当年标准保费达成率 */
    @ApiModelProperty(name = "当年标准保费达成率",notes = "")
    private Double syAssessConvertInsuranceAmtAchieveRate ;
    /** 当月异业保费配比 */
    @ApiModelProperty(name = "当月异业保费配比",notes = "")
    private Double smOfflineLoanInsuranceRate ;
    /** 当年异业保费配比 */
    @ApiModelProperty(name = "当年异业保费配比",notes = "")
    private Double syOfflineLoanInsuranceRate ;
    /** 当月异业保费配比目标 */
    @ApiModelProperty(name = "当月异业保费配比目标",notes = "")
    private Double smOfflineLoanInsuranceRateTarget ;
    /** 当年异业保费配比目标 */
    @ApiModelProperty(name = "当年异业保费配比目标",notes = "")
    private Double syOfflineLoanInsuranceRateTarget ;
    /** 当月留存率 */
    @ApiModelProperty(name = "当月留存率",notes = "")
    private Double smInsuranceRetentionRate ;
    /** 当年留存率 */
    @ApiModelProperty(name = "当年留存率",notes = "")
    private Double syInsuranceRetentionRate ;
    /** 当月信贷客户留存率 */
    @ApiModelProperty(name = "当月信贷客户留存率",notes = "")
    private Double smLoanInsuranceRetentionRate ;
    /** 当年信贷客户留存率 */
    @ApiModelProperty(name = "当年信贷客户留存率",notes = "")
    private Double syLoanInsuranceRetentionRate ;
    /** 当月非信贷客户留存率 */
    @ApiModelProperty(name = "当月非信贷客户留存率",notes = "")
    private Double smUnloanInsuranceRetentionRate ;
    /** 当年非信贷客户留存率 */
    @ApiModelProperty(name = "当年非信贷客户留存率",notes = "")
    private Double syUnloanInsuranceRetentionRate ;
    /** 当月信贷客户转化率 */
    @ApiModelProperty(name = "当月信贷客户转化率",notes = "")
    private Double smLoanCustTransRate ;
    /** 当年信贷客户转化率 */
    @ApiModelProperty(name = "当年信贷客户转化率",notes = "")
    private Double syLoanCustTransRate ;
    /** 当月信贷客户转化率目标 */
    @ApiModelProperty(name = "当月信贷客户转化率目标",notes = "")
    private Double smLoanCustTransRateTarget ;
    /** 当年信贷客户转化率目标 */
    @ApiModelProperty(name = "当年信贷客户转化率目标",notes = "")
    private Double syLoanCustTransRateTarget ;

    @ApiModelProperty(name = "个人标准保费在分支排行",notes = "")
    private Integer rankInBch ;

    @ApiModelProperty(name = "个人标准保费在分支排行与前一名的差距",notes = "")
    private Double gapWithPreviousInBch ;

    @ApiModelProperty(name = "个人标准保费在区域排行",notes = "")
    private Integer rankInArea ;

    @ApiModelProperty(name = "个人标准保费在区域排行与前一名的差距",notes = "")
    private Double gapWithPreviousInArea ;

    @ApiModelProperty(name = "个人标准保费在全国排行",notes = "")
    private Integer rankInCountry ;

    @ApiModelProperty(name = "个人标准保费在全国排行与前一名的差距",notes = "")
    private Double gapWithPreviousInCountry ;

    //sm_insurance_retention_rate_target
    @ApiModelProperty(name = "当月留存率",notes = "")
    private Double smInsuranceRetentionRateTarget ;
    //sy_insurance_retention_rate_target
    @ApiModelProperty(name = "当年留存率",notes = "")
    private Double syInsuranceRetentionRateTarget ;

    @ApiModelProperty(name = "分区",notes = "")
    private String pt ;

    /** 当日标准保费 */
    @ApiModelProperty(name = "当日标准保费",notes = "")
    private Double cdAssessConvertInsuranceAmt ;
    @ApiModelProperty(value = "当周标准保费",notes = "")
    private Double swAssessConvertInsuranceAmt ;
    /** 当月信贷放款金额 */
    @ApiModelProperty(name = "当月信贷放款金额",notes = "")
    private Double smOfflineLoanAmt ;
    /** 上月信贷放款金额 */
    @ApiModelProperty(name = "上月信贷放款金额",notes = "")
    private Double lmOfflineLoanAmt ;
    /** 当月信贷相关规模保费 */
    @ApiModelProperty(name = "当月信贷相关规模保费",notes = "")
    private Double smLoanInsuranceAmt ;
    /** 上月信贷相关规模保费 */
    @ApiModelProperty(name = "上月信贷相关规模保费",notes = "")
    private Double lmLoanInsuranceAmt ;
    /** 当日信贷放款金额 */
    @ApiModelProperty(name = "当日信贷放款金额",notes = "")
    private Double cdOfflineLoanAmt ;
    /** 当日信贷相关规模保费 */
    @ApiModelProperty(name = "当日信贷相关规模保费",notes = "")
    private Double cdLoanInsuranceAmt ;
    /** 当日信贷相关标准保费 */
    @ApiModelProperty(name = "当日信贷相关标准保费",notes = "")
    private Double cdLoanAssessConvertInsuranceAmt ;
    /** 当月信贷相关标准保费 */
    @ApiModelProperty(name = "当月信贷相关标准保费",notes = "")
    private Double smLoanAssessConvertInsuranceAmt ;
    /** 上月信贷相关标准保费 */
    @ApiModelProperty(name = "上月信贷相关标准保费",notes = "")
    private Double lmLoanAssessConvertInsuranceAmt ;
    /** 当日非贷标准保费 */
    @ApiModelProperty(name = "当日非贷标准保费",notes = "")
    private Double cdUnloanAssessConvertInsuranceAmt ;
    /** 当月非贷标准保费 */
    @ApiModelProperty(name = "当月非贷标准保费",notes = "")
    private Double smUnloanAssessConvertInsuranceAmt ;
    /** 上月非贷标准保费 */
    @ApiModelProperty(name = "上月非贷标准保费",notes = "")
    private Double lmUnloanAssessConvertInsuranceAmt ;
    /** 信贷相关标准保费月环比 */
    @ApiModelProperty(name = "信贷相关标准保费月环比",notes = "")
    private Double loanAssessConvertInsuranceAmtMom;
    //yd_loan_assess_convert_insurance_amt
    @ApiModelProperty(name = "昨日信贷相关标准保费",notes = "")
    private Double ydLoanAssessConvertInsuranceAmt ;
    //loan_assess_convert_insurance_amt_dod
    @ApiModelProperty(name = "信贷相关标准保费日对比",notes = "")
    private Double loanAssessConvertInsuranceAmtDod ;
    //unloan_assess_convert_insurance_amt_mom
    @ApiModelProperty(name = "非贷相关标准保费月环比",notes = "")
    private Double unloanAssessConvertInsuranceAmtMom ;
    //yd_unloan_assess_convert_insurance_amt
    @ApiModelProperty(name = "昨日非贷相关标准保费",notes = "")
    private Double ydUnloanAssessConvertInsuranceAmt ;
    //unloan_assess_convert_insurance_amt_dod
    @ApiModelProperty(name = "非贷相关标准保费日对比",notes = "")
    private Double unloanAssessConvertInsuranceAmtDod ;
    //cd_offline_loan_insurance_rate
    @ApiModelProperty(name = "当日信贷相关保费占比",notes = "")
    private Double cdOfflineLoanInsuranceRate ;


    /** 当月推广费费 */
    @ApiModelProperty(name = "当月推广费费",notes = "")
    private Double smGrantAmount ;
    /** 当年推广费费 */
    @ApiModelProperty(name = "当年推广费费",notes = "")
    private Double syGrantAmount ;
    /** 当日推广费费 */
    @ApiModelProperty(name = "当日推广费费",notes = "")
    private Double cdGrantAmount ;
}