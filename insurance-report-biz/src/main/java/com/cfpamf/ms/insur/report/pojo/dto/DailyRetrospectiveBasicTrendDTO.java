package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "日复盘趋势指标", description = "")
public class DailyRetrospectiveBasicTrendDTO {
    @ApiModelProperty(value = "日期",notes = "")
    String date;
    @ApiModelProperty(value = "值",notes = "")
    Double value;
    @ApiModelProperty(value = "员工编码",notes = "")
    String empCode;
}
