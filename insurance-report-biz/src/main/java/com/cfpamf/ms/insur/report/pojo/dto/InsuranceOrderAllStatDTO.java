package com.cfpamf.ms.insur.report.pojo.dto;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 数据中心日报数据
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class InsuranceOrderAllStatDTO {

    private Date rptDate;
    /**
     * 昨日客户数
     */
    private Integer insuredCustDay;
    /**
     * 昨日保单数
     */
    private Integer insuredCntDay;
    /**
     * 昨日保费
     */
    private BigDecimal insuredAmtDay;

    /**
     * 截止T-1当月客户数
     */
    private Integer insuredCustMonth;
    /**
     * 截止T-1当月保单数
     */
    private Integer insuredCntMonth;
    /**
     * 截止T-1当月保费
     */
    private BigDecimal insuredAmtMonth;

    /**
     * 截止T-1当年客户数
     */
    private Integer insuredCustYear;
    /**
     * 截止T-1当年保单数
     */
    private Integer insuredCntYear;
    /**
     * 截止T-1当年保费
     */
    private BigDecimal insuredAmtYear;

    /**
     * 截止T-1累计客户数
     */
    private Integer insuredCustAll;
    /**
     * 截止T-1累计保单数
     */
    private Integer insuredCntAll;
    /**
     * 截止T-1累计保费
     */
    private BigDecimal insuredAmtAll;
}
