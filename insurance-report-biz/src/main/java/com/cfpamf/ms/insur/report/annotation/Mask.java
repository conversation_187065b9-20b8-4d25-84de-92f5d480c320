package com.cfpamf.ms.insur.report.annotation;

import java.lang.annotation.*;

/**
 * 需要脱敏数据注解
 *
 * <AUTHOR>
 */

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
@Documented
public @interface Mask {

    /**
     * 数据类型
     *
     * @return
     */
    DataType dataType() default DataType.NULL;

    /**
     * 数据类型
     * 添加 修改 删除 查询
     */
    enum DataType {

        /**
         * 空
         */
        NULL,

        /**
         * 手机号
         */
        MOBILE,

        /**
         * 邮箱
         */
        EMAIL,

        /**
         * 身份证
         */
        ID_CARD
    }
}
