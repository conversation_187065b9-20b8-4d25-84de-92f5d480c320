package com.cfpamf.ms.insur.report.feign;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.report.pojo.query.InsuranceDailyReviewFinanceDataQuery;
import com.cfpamf.ms.insur.report.pojo.vo.InsuranceDailyReviewFinanceDataVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/21
 * 信贷统一数据服务facade类
 */
@FeignClient(name = "finance-unified-data", url = "${finance-unified-data.url}")
public interface FinanceUnifiedDataFacade {

    /**
     *
     * @param request request
     * @return
     */
    @ApiOperation(value = "根据客户id查询客户基本信息")
    @RequestMapping(value = {"/finance/unifieddata/insurance/queryInsuranceDailyReviewFinanceData"}, method = {RequestMethod.GET})
    Result<List<InsuranceDailyReviewFinanceDataVo>> queryInsuranceDailyReviewFinanceData(@RequestBody InsuranceDailyReviewFinanceDataQuery request);

}
