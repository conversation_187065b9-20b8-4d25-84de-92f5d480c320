package com.cfpamf.ms.insur.report.pojo.vo.excel;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 个人数据统计Excel实体
 *
 * <AUTHOR>
 * @date 2021/3/30 15:14
 */
@Data
public class PersonnelStatisticsExcelVo {
    /**
     * 区域
     */
    @ExportField(order = 0)
    private String region;

    /**
     * 工号
     */
    @ExportField(order = 2)
    private String jobNumber;

    /**
     * 机构/组织
     */
    @ExportField(order = 1)
    private String organizationName;

    /**
     * 姓名
     */
    @ExportField(order = 3)
    private String name;

    /**
     * 入职日期
     */
    @ExportField(order = 4)
    private LocalDateTime entryDate;

    /**
     * 岗位
     */
    @ExportField(order = 5)
    private String jobName;


    /**
     * 日 单量
     */
    @ExportField(order = 6, type = "Integer")
    private Integer dayOrderQuantity;

    /**
     * 月 单量
     */
    @ExportField(order = 7, type = "Integer")
    private Integer monthOrderQuantity;

    /**
     * 年 单量
     */
    @ExportField(order = 8, type = "Integer")
    private Integer yearOrderQuantity;

    /**
     * 日 保费
     */
    @ExportField(order = 9, type = "money")
    private BigDecimal dayPremium;

    /**
     * 月 保费
     */
    @ExportField(order = 10, type = "money")
    private BigDecimal monthPremium;

    /**
     * 年 保费
     */
    @ExportField(order = 11, type = "money")
    private BigDecimal yearPremium;

    /**
     * 非信贷客户当月保费
     */
    @ExportField(order = 12, type = "money")
    BigDecimal monthPremiumNoLoaner;

    /**
     * 非信贷客户当年保费
     */
    @ExportField(order = 13, type = "money")
    private BigDecimal yearPremiumNoLoaner;

    /**
     * 非信贷客户当月保费占比
     */
    @ExportField(order = 14, type = "percentage")
    private BigDecimal monthPremiumProportionNoLoaner;

    /**
     * 非信贷客户当年保费占比
     */
    @ExportField(order = 15, type = "percentage")
    private BigDecimal yearPremiumProportionNoLoaner;

    /**
     * 当月非信贷相关客户占比
     */
    @ExportField(order = 18, type = "percentage")
    private BigDecimal currentMonthProportionNoLoaner;

    /**
     * 信贷客户转化率
     */
    @ExportField(order = 16, type = "percentage")
    private BigDecimal loanerConversionRate;

    /**
     * 信贷相关客户转化率
     */
    @ExportField(order = 17, type = "percentage")
    private BigDecimal loanerRelevantConversionRate;

    /**
     * 年续保率
     */
    @ExportField(order = 19, type = "percentage")
    private BigDecimal yearRenewInsuranceRate;

    /**
     * 月续保率
     */
    @ExportField(order = 20, type = "percentage")
    private BigDecimal monthRenewInsuranceRate;

}
