package com.cfpamf.ms.insur.report.pojo.query;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/18
 * 查询保险日复盘信贷数据request类
 */
@Data
public class InsuranceDailyReviewFinanceDataQuery {


    @ApiModelProperty(value = "客户经理编码列表")
    @NotEmpty(message = "客户经理编码不能为空")
    private List<String> loanManagerIds;

    @ApiModelProperty(value = "分支编码")
    @NotEmpty(message = "分支编码不能为空")
    private String branchCode;

    @ApiModelProperty(value = "查询日期，yyyy-MM-dd")
    @Pattern(regexp="^\\d{4}-\\d{2}-\\d{2}$", message="日期格式必须为yyyy-MM-dd")
    private String queryDate;

    //"queryAuthorizationCode":"EC7CE59E78160B54BBE19E016C688F3B"
    private String queryAuthorizationCode = "EC7CE59E78160B54BBE19E016C688F3B";
}
