package com.cfpamf.ms.insur.report.pojo.vo.promotion;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "保险销售端推广指标对象", description = "")
public class AssistantSalerPromotionSummaryVO {
    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "当月分享数", notes = "")
    String smShareCnt;
    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "当月访客数", notes = "")
    String smVisitCnt;
    /**
     * 当月转化保单数
     */
    @ApiModelProperty(value = "当月转化保单数", notes = "")
    String smTransformPolicyCnt;
    /**
     * 当日分享数
     */
    @ApiModelProperty(value = "当日分享数", notes = "")
    String sdShareCnt;
    /**
     * 当日访客数
     */
    @ApiModelProperty(value = "当日访客数", notes = "")
    String sdVisitCnt;

    /**
     * 访客全国排名
     */
    @ApiModelProperty(value = "当月分享榜首员工姓名", notes = "")
    String monthShareTopName;

    /**
     * 访客区域排名
     */
    @ApiModelProperty(value = "当月分享榜首员工姓名", notes = "")
    String monthVisitTopEmpName;

    /**
     * 未跟进访客数
     */
    @ApiModelProperty(value = "未跟进访客数", notes = "")
    String unfollowCnt;

    /**
     * 当月转换客户数
     */
    @ApiModelProperty(value = "当月转换客户数", notes = "")
    String smTransformCustomerCnt;
}
