package com.cfpamf.ms.insur.report.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.feign.OperationFacade;
import com.cfpamf.ms.insur.report.feign.req.CustomerConversionQuery;
import com.cfpamf.ms.insur.report.feign.req.CustomerFollowCntQuery;
import com.cfpamf.ms.insur.report.feign.req.QueryCustomerConvertPremium;
import com.cfpamf.ms.insur.report.feign.resp.dto.CustomerConversionDto;
import com.cfpamf.ms.insur.report.feign.resp.dto.CustomerConvertPremiumDto;
import com.cfpamf.ms.insur.report.feign.resp.dto.CustomerFollowCntDto;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/2/22 16:58
 */
@Service
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class OperationService {
    OperationFacade operationFacade;


    /**
     * 获取客户跟进数
     * @return CustomerFollowCntDto
     */
    public List<CustomerFollowCntDto> getCustomerFollowCnt(CustomerFollowCntQuery query) {
        Result<List<CustomerFollowCntDto>> commonResult = operationFacade.getCustomerFollowCnt(query);

        // 如果调用不成功
        if (!commonResult.isSuccess()) {
            // 抛出异常，表示获取数据失败
            throw new MSBizNormalException(ExcptEnum.OPERATION_CENTER_ERROR.getCode(), "获取客户跟进数据失败:" + commonResult.getMessage());
        }

        // 返回对象
        log.info("获取客户跟进数据成功:{}", JSON.toJSONString(commonResult.getData()));
        return commonResult.getData();
    }

    /**
     * 获取客户经理业绩指标
     * @return CustomerConversionDto
     */
    public List<CustomerConversionDto> getCustomerConversion(CustomerConversionQuery query) {
        Result<List<CustomerConversionDto>> commonResult = operationFacade.getCustomerConversion(query);

        // 如果调用不成功
        if (!commonResult.isSuccess()) {
            // 抛出异常，表示获取数据失败
            throw new MSBizNormalException(ExcptEnum.OPERATION_CENTER_ERROR.getCode(), "获取客户经理业绩指标失败:" + commonResult.getMessage());
        }

        // 返回对象
        return commonResult.getData();
    }


    public List<CustomerConvertPremiumDto> getCustomerConvertPremium(QueryCustomerConvertPremium query){
        Result<List<CustomerConvertPremiumDto>> customerConvertPremium = operationFacade.getCustomerConvertPremium(query);
        if (!customerConvertPremium.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.OPERATION_CENTER_ERROR.getCode(), "获取客户保费数据失败:" + customerConvertPremium.getMessage());
        }
        return customerConvertPremium.getData();
    }
}
