package com.cfpamf.ms.insur.report.pojo.vo.promotion;

import com.cfpamf.ms.insur.report.pojo.dto.AssistantManagerPromotionLevelDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AssistantManagerPromotionLevelVO {
    @ApiModelProperty(value = "当月层级详细列表", notes = "")
    List<AssistantManagerPromotionLevelDetail> levelDetailList;

    @ApiModelProperty("nation 全国, area 区域, district 片区, bch 分支")
    String levelType;

    @ApiModelProperty("总数")
    Integer total;
}
