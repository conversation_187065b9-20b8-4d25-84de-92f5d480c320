package com.cfpamf.ms.insur.report.constant;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/2/28 14:55
 */
public interface AssistantConstants {

    /**
     * 排序相关值
     */
    Set<String> VALID_ORDER_BY_CLAUSE = Sets.newHashSet("desc", "asc");


    /**
     * 数仓相关字段
     */
    Set<String> VALID_COL_NAME_DW = Sets.newHashSet(
            // 当月标准保费
            "sm_assess_convert_insurance_amt",
            // 当年标准保费
            "sy_assess_convert_insurance_amt",
            // 当月标准保费目标
            "sm_assess_convert_insurance_amt_target",
            // 当年标准保费目标
            "sy_assess_convert_insurance_amt_target",
            // 当月标准保费达成率
            "sm_assess_convert_insurance_amt_achieve_rate",
            // 当年标准保费达成率
            "sy_assess_convert_insurance_amt_achieve_rate",
            // 当月异业保费配比
            "sm_offline_loan_insurance_rate",
            // 当年异业保费配比
            "sy_offline_loan_insurance_rate",
            // 当月异业保费配比目标
            "sm_offline_loan_insurance_rate_target",
            // 当年异业保费配比目标
            "sy_offline_loan_insurance_rate_target",
            // 当月留存率
            "sm_insurance_retention_rate",
            // 当年留存率
            "sy_insurance_retention_rate",
            // 当月留存率目标
            "sm_insurance_retention_rate_target",
            // 当年留存率目标
            "sy_insurance_retention_rate_target",
            // 当月信贷客户留存率
            "sm_loan_insurance_retention_rate",
            // 当年信贷客户留存率
            "sy_loan_insurance_retention_rate",
            // 当月非信贷客户留存率
            "sm_unloan_insurance_retention_rate",
            // 当年非信贷客户留存率
            "sy_unloan_insurance_retention_rate",
            // 当月信贷客户转化率
            "sm_loan_cust_trans_rate",
            // 当年信贷客户转化率
            "sy_loan_cust_trans_rate",
            // 上月标准保费
            "lm_assess_convert_insurance_amt",
            // 月 人均保费
            "sm_assess_convert_insurance_amt_avg",
            // 年人均保费
            "sy_assess_convert_insurance_amt_avg"
    );

    /**
     * 待办相关字段
     */
    Set<String> VALID_COL_NAME_TDO = Sets.newHashSet(
            // 当年断保待办任务数
            "sy_interruption_todo_cnt",
            // 当年续保待办任务数
            "sy_renew_short_todo_cnt",
            // 当年断保待办任务跟进率
            "sy_interruption_todo_follow_rate",
            // 当年续保待办任务跟进率
            "sy_renew_short_todo_follow_rate",
            // 当年断保待办任务激活率
            "sy_interruption_todo_conversion_rate",
            // 当年续保待办任务激活率
            "sy_renew_short_todo_conversion_rate",
            // 当年断保待办任务激活规模保费
            "sy_interruption_todo_conversion_amt",
            // 当年续保待办任务激活规模保费
            "sy_renew_short_todo_conversion_amt",
            // 当年断保待办跟进数
            "sy_interruption_todo_follow_cnt",
            // 当年断保待办激活数
            "sy_interruption_tdo_follow_cust",
            // 当年续保待办跟进数
            "sy_tdo_short_follow_cnt",
            // 当年续保待办激活数
            "sy_tdo_short_follow_policy"
    );

}


