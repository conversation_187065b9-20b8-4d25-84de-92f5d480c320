package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@ApiModel(value = "保险管理端推广指标对象", description = "")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantManagerPromotionSummaryDTO {
    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "当月分享数", notes = "")
    String smShareCnt="1.9万";
    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "当月访客数", notes = "")
    String smVisitCnt="20万";
    /**
     * 当月转化保单数
     */
    @ApiModelProperty(value = "当月转化保单数", notes = "")
    String smTransformPolicyCnt="200";
    /**
     * 当日分享数
     */
    @ApiModelProperty(value = "当日分享数", notes = "")
    String sdShareCnt="1,000";
    /**
     * 当日访客数
     */
    @ApiModelProperty(value = "当日访客数", notes = "")
    String sdVisitCnt="500";
}
