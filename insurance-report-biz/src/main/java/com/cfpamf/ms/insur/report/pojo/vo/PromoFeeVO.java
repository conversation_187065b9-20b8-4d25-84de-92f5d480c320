package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PromoFeeVO {
    /**
     * 统计日期（始） 时间格式 yyyy-MM-dd
     */
    @ExportField(name = "统计日期（始）",order = 0)
    @ApiModelProperty("统计日期（始）*")
    private String startDate;

    @ExportField(name = "统计日期（止）",order = 1)
    @ApiModelProperty("统计日期（止）*")
    private String endDate;

    @ExportField(name = "区域" ,order = 2)
    @ApiModelProperty("区域")
    private String regionName;

    @ExportField(name = "机构" ,order = 3)
    @ApiModelProperty("机构")
    private String orgName;

    @ExportField(name = "岗位" ,order = 6)
    @ApiModelProperty("岗位")
    private String jobName;

    @ExportField(name = "所属公司主体" ,order = 7)
    @ApiModelProperty("所属公司主体")
    private String financeOrgName;

    @ExportField(name = "单量" ,order = 8,type = "integer")
    @ApiModelProperty("单量")
    private Integer qty;

    @ExportField(name = "保费" ,order = 9,type = "money")
    @ApiModelProperty("保费")
    private BigDecimal amount;


}
