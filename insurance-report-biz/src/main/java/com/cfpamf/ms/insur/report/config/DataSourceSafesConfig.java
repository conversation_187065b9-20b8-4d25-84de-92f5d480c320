package com.cfpamf.ms.insur.report.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.MybatisProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
@EnableConfigurationProperties(MybatisProperties.class)
@MapperScan(basePackages = "com.cfpamf.ms.insur.report.dao.safes",
        sqlSessionFactoryRef = "sqlSessionFactorySafes",
        sqlSessionTemplateRef = "sqlSessionTemplateSafes")
public class DataSourceSafesConfig {
    static final String PACKAGE = "com.cfpamf.ms.insur.report.dao.safes";
    static final String MAPPER_LOCATION = "classpath*:mapper/safes/*.xml";

    static final String PREFIX = "safes";


    final
    MybatisProperties properties;

    @Autowired(required = false)
    public DataSourceSafesConfig(MybatisProperties properties) {
        this.properties = properties;
    }

    @ConfigurationProperties(prefix = "spring.datasource.safes")
    @Bean(name = "dataSourceSafes")
    public DataSource safesDataSource() {
        return new DruidDataSource();
    }

    @Bean(name = "sqlSessionFactorySafes")
    public SqlSessionFactory sqlSessionFactorySafes(@Qualifier("dataSourceSafes") DataSource dataSource) throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        applyConfiguration(sessionFactory);
        Resource[] resources = new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION);
        sessionFactory.setMapperLocations(resources);
//        sessionFactory.setVfs(SpringBootVFS.class);
        return sessionFactory.getObject();
    }

    @Bean(name = "dataSourceTransactionManagerSafes")
    public DataSourceTransactionManager dataSourceTransactionManagerSafes(@Qualifier("dataSourceSafes") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "sqlSessionTemplateSafes")
    public SqlSessionTemplate sqlSessionTemplateSafes(@Qualifier("sqlSessionFactorySafes") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    private void applyConfiguration(SqlSessionFactoryBean factory) {
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        factory.setConfiguration(configuration);
    }
}
