package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BusinessRankVO implements Serializable {
    /**
     * 统计日期（始） 时间格式 yyyy-MM-dd
     */
    @ExportField(name = "统计日期（始）",order = 0)
    @ApiModelProperty("统计日期（始）*")
    private String startDate;

    @ExportField(name = "统计日期（止）",order = 1)
    @ApiModelProperty("统计日期（止）*")
    private String endDate;

    @ExportField(name = "区域", order = 2)
    @ApiModelProperty("区域")
    private String regionName;
    @ExportField(name = "分支机构" , order = 3)
    @ApiModelProperty("分支机构")
    private String orgName;

    @ExportField(name = "负责人姓名",order = 4)
    @ApiModelProperty("负责人姓名")
    private String userName;

    @ExportField(name = "负责人工号",order = 5)
    @ApiModelProperty("负责人工号")
    private String userId;

    @ExportField(name = "在保客户数",order = 6,type="integer")
    @ApiModelProperty("在保客户数")
    private Integer validInsuredCnt;

    @ExportField(name = "非信贷相关在保客户数",order = 7,type="integer")
    @ApiModelProperty("非信贷相关在保客户数")
    private Integer unloanValidInsuredCnt;

    @ExportField(name = "非信贷及相关客户占比",order = 8,type="money")
    @ApiModelProperty("非信贷及相关客户占比")
    private BigDecimal unloanValidInsuredPer;

    @ExportField(name = "续保率",order = 9,type="money")
    @ApiModelProperty("续保率")
    private BigDecimal renewalRate;

    @ExportField(name = "续保率排名",order = 10,type="integer")
    @ApiModelProperty("续保率排名")
    private Integer renewalRateRank;

    @ExportField(name = "入围排名",order = 11)
    @ApiModelProperty("入围排名")
    private String cadidationRank;
    @ExportField(name = "机构单量",order = 12,type="integer")
    @ApiModelProperty("机构单量")
    private Integer insuranceCntMonthly;

    @ExportField(name = "机构保费",order = 13,type="money")
    @ApiModelProperty("机构保费")
    private BigDecimal insuranceAmtMonthly;

    @ExportField(name = "在职人员数",order = 14,type="integer")
    @ApiModelProperty("在职人员数")
    private Integer validEmployeeCnt;

    @ExportField(name = "月度人均保费",order = 15,type="money")
    @ApiModelProperty("月度人均保费")
    private BigDecimal avgInsuranceAmtMonthly;

    @ExportField(name = "累计月度人均保费",order = 16,type="money")
    @ApiModelProperty("累计月度人均保费")
    private BigDecimal acmAvgInsuranceAmtMonthly;


    @ApiModelProperty("是否达到续保率要求")
    private Integer reachRenewal;

    @ApiModelProperty("是否达到保费要求")
    private Integer reachInsuranceAmt;

    @ExportField(name = "津贴发放比例",order = 17,type="money")
    @ApiModelProperty("津贴发放比例")
    private BigDecimal allowancePer;

}
