package com.cfpamf.ms.insur.report.feign.resp.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 断保客户
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerConversionDto {

    @ApiModelProperty(name="员工工号")
    String empCode;

    @ApiModelProperty(name="员工姓名")
    String empName;

    @ApiModelProperty(name="员工标准保费")
    BigDecimal conversionAmt;
}
