package com.cfpamf.ms.insur.report.pojo.vo.excel;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 数据统计Excel实体
 *
 * <AUTHOR>
 * @date 2021/3/30 15:14
 */
@Data
public class RegionStatisticsExcelVo {
    /**
     * 区域
     */
    @ExportField(order = 0)
    private String region;

    /**
     * 编制人数
     */
    @ExportField(order = 1, type = "Integer")
    private Integer organizationStaffCount;

    /**
     * 当月在职员工人数
     */
    @ExportField(order = 2, type = "Integer")
    private Integer monthJobStaffCount;

    /**
     * 日 单量
     */
    @ExportField(order = 3, type = "Integer")
    private Integer dayOrderQuantity;

    /**
     * 月 单量
     */
    @ExportField(order = 4, type = "Integer")
    private Integer monthOrderQuantity;

    /**
     * 年 单量
     */
    @ExportField(order = 5, type = "Integer")
    private Integer yearOrderQuantity;

    /**
     * 日 保费
     */
    @ExportField(order = 6, type = "money")
    private BigDecimal dayPremium;

    /**
     * 月 保费
     */
    @ExportField(order = 7, type = "money")
    private BigDecimal monthPremium;

    /**
     * 年 保费
     */
    @ExportField(order = 8, type = "money")
    private BigDecimal yearPremium;

    /**
     * 当月保费目标
     */
    @ExportField(order = 9, type = "money")
    private BigDecimal currentMonthPremiumTarget;

    /**
     * 截至当月保费目标
     */
    @ExportField(order = 10, type = "money")
    private BigDecimal presentCurrentMonthPremiumTarget;

    /**
     * 当年保费目标
     */
    @ExportField(order = 11, type = "money")
    private BigDecimal currentYearPremiumTarget;

    /**
     * 当月保费目标完成率
     */
    @ExportField(order = 12, type = "percentage")
    private BigDecimal currentMonthCompletionRate;

    /**
     * 截至当月保费目标完成率
     */
    @ExportField(order = 13, type = "percentage")
    private BigDecimal presentCurrentMonthCompletionRate;

    /**
     * 当年保费目标完成率
     */
    @ExportField(order = 14, type = "percentage")
    private BigDecimal currentYearCompletionRate;

    /**
     * 当月在职员工月度人均保费
     */
    @ExportField(order = 15, type = "money")
    private BigDecimal currentMonthJobStaffPerCapitaPremium;

    /**
     * 当年在职员工月度人均保费
     */
    @ExportField(order = 16, type = "money")
    private BigDecimal currentYearJobStaffPerCapitaPremium;

    /**
     * 当月编制月度人均保费
     */
    @ExportField(order = 17, type = "money")
    private BigDecimal currentMonthOrganizationStaffPerCapitaPremium;

    /**
     * 当年编制月度人均保费
     */
    @ExportField(order = 18, type = "money")
    private BigDecimal currentYearOrganizationStaffPerCapitaPremium;


    /**
     * 非信贷客户当月保费
     */
    @ExportField(order = 19, type = "money")
    BigDecimal monthPremiumNoLoaner;

    /**
     * 非信贷客户当年保费
     */
    @ExportField(order = 20, type = "money")
    private BigDecimal yearPremiumNoLoaner;

    /**
     * 非信贷客户当月保费占比
     */
    @ExportField(order = 21, type = "percentage")
    private BigDecimal monthPremiumProportionNoLoaner;

    /**
     * 非信贷客户当年保费占比
     */
    @ExportField(order = 22, type = "percentage")
    private BigDecimal yearPremiumProportionNoLoaner;

    /**
     * 信贷客户转化率
     */
    @ExportField(order = 23, type = "percentage")
    private BigDecimal loanerConversionRate;

    /**
     * 信贷相关客户转化率
     */
    @ExportField(order = 24, type = "percentage")
    private BigDecimal loanerRelevantConversionRate;

    /**
     * 当月非信贷相关客户占比
     */
    @ExportField(order = 25, type = "percentage")
    private BigDecimal currentMonthProportionNoLoaner;

    /**
     * 年续保率
     */
    @ExportField(order = 26, type = "percentage")
    private BigDecimal yearRenewInsuranceRate;

    /**
     * 月续保率
     */
    @ExportField(order = 27, type = "percentage")
    private BigDecimal monthRenewInsuranceRate;

    /**
     * 线下当月放款金额
     */
    @ExportField(order = 28, type = "money")
    private BigDecimal currentMonthOffline;

    /**
     * 线下当年放款金额
     */
    @ExportField(order = 29, type = "money")
    private BigDecimal currentYearOffline;

    /**
     * 当月 保费/线下放款金额
     */
    @ExportField(order = 30, type = "percentage")
    private BigDecimal currentMonthOfflinePremiumProportion;

    /**
     * 当年 保费/线下放款金额
     */
    @ExportField(order = 31, type = "percentage")
    private BigDecimal currentYearOfflinePremiumProportion;


    /**
     * 当月放款金额
     */
    @ExportField(order = 32, type = "money")
    private BigDecimal currentMonth;

    /**
     * 当年放款金额
     */
    @ExportField(order = 33, type = "money")
    private BigDecimal currentYear;

    /**
     * 当月 保费/放款金额
     */
    @ExportField(order = 34, type = "percentage")
    private BigDecimal currentMonthPremiumProportion;

    /**
     * 当年 保费/放款金额
     */
    @ExportField(order = 35, type = "percentage")
    private BigDecimal currentYearPremiumProportion;


}
