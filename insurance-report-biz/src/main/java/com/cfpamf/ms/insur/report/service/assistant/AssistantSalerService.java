package com.cfpamf.ms.insur.report.service.assistant;

import com.cfpamf.ms.insur.report.enums.DateTypeEnum;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerBasicDTO;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerSingleTrendDTO;
import com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceEmpMarketingProgressDfp;

import java.util.List;

public interface AssistantSalerService {
    public AssistantSalerBasicDTO getAssistantSalerData(String empCode, String pt);

    public AssistantSalerBasicDTO getAssistantSalerRank(String empCode,String bchCode,String areaCode, String pt, DateTypeEnum dateTypeEnum);

    AssistantSalerSingleTrendDTO queryEmpAmtTrend(String empCode, List<String> ptListCurrentYear);

    AssistantSalerSingleTrendDTO queryEmpATypeCustTransRateTrend(String empCode, List<String> ptListCurrentYear);

    AssistantSalerSingleTrendDTO queryEmpATypeCustInsuranceRateTrend(String empCode, List<String> ptListCurrentYear);

    AssistantSalerSingleTrendDTO queryEmpRetentionRate(String empCode, List<String> ptListCurrentYear);
}
