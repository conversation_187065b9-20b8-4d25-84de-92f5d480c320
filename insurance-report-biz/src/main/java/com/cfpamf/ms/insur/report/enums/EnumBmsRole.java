package com.cfpamf.ms.insur.report.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;

/**
 * Create By zhengjing on 2020/2/18 11:40
 */
@AllArgsConstructor
@Getter
public enum EnumBmsRole {

    //    admin:R0001
//    bizManager:R0002
//    bizSupport:R0003
//    claimClerk:R0004
//    branchBuManager: R0023
//    regionManager: R0005
//    zoneManager: R0024
//    regionSupervise:R0006
//    branchManager:R0007
//    branchSupervise:R0008
//    branchBiz:R0009
//    insBiz:R0010
//    riskBiz:R0011
//    operBiz:R0012
//    dataBiz:R0013
//    bizCheck:
//
//    R0014
    ADMIN("R0001", "管理员"),
    BIZ_MANAGER("R0002", "业务管理"),
    BIZ_SUPPORT("R0003", "业务支持"),
    CLAIM_CLERK("R0004","理赔管理"),
    REGION_MANAGER("R0005","区域负责人"),
    REGION_SUPERVISE("R0006","区域督导"),
    BRANCH_MANAGER("R0007","分支负责人"),
    BRANCH_SUPERVISE("R0008","分支督导"),
    BRANCH_BIZ("R0009","分支内务"),
    ORG_ADMIN("R0010", "机构对接人"),
    RISK_BIZ("R0011","风险管理"),
    OPER_BIZ("R0012","运营管理"),
    DATA_BIZ("R0013","数据支持"),
    BIZ_CHECK("R0014","业务监测"),
    FINANCE_BIZ("R0021","保险财务"),
    SUB_ADMIN("R0022","子管理员"),
    BRANCH_BU_MANAGER("R0023","事业部负责人"),
    ZONE_MANAGER("R0024","片区负责人"),
    AUDIT_BIZ("R0025","审计"),
    BRANCH_BU_CONTACT("R0028","事业部对接人"),
    CUSTOMER_MANAGER("R0029","客户经理"),
    YONG_TONG_MANAGER("R0030","永通保代负责人"),
    YONG_TONG_BIZ("R0031","永通保代业务支持"),
    INSUR_REGION_SUPER("R0032","保险区域督导"),
    AUDIT_BIZ_2("R0033","内审2"),
    AUDIT_BIZ_3("R0034","内审3"),
    PRODUCT_MANAGER("R0035","产品管理"),
    PERFORMANCE_MANAGER("R0036","绩效管理"),
    FINANCE_SETTLEMENT("R0044","财务结算"),
    INSURANCE_ASSISTANT("R0045","保险助理"),
    DATA_CENTER("R0039","数据中心"),
    PCO("R0049","渠道PCO"),
    ;

    private String code;
    private String name;


    public static String[] getCancelRoles() {
        return new String[]{BIZ_SUPPORT.code};
    }


}
