package com.cfpamf.ms.insur.report.feign.resp.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OrganizationVO extends OrganizationBaseVO implements Serializable {

    private static final long serialVersionUID = -6155131318397608992L;

    private String batchNo;

    private String postCode;

    private String province;

    private String city;

    private String address;

    private String telephone;

    private int orderNo;

    private int orgStatus;

    private int createBy;

    private Date createTime;

    private int updateBy;

    private Date updateTime;
    /**
     * 设立日期
     */
    private Date establishDate;
    /**
     * 生效日期
     */
    private Date startDate;
    /**
     * 失效日期
     */
    private Date stopDate;
    /**
     * 变更日期
     */
    private Date changeDate;

    private Integer parentOrgId;

    private Integer parentHrOrgId;

    private Integer parentHrParentId;

    private String parentOrgCode;

    private String parentOrgName;

    private String hrbpName;

    private String personInChargeName;

    private String zoneOrgCode;

    private String parentTreePath;
}
