package com.cfpamf.ms.insur.report.service.retrospective;

import com.cfpamf.ms.insur.report.feign.resp.vo.UserVo;
import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveBasicDTO;
import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveBasicTrendDTO;
import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveFinanceStatisticsDTO;
import com.cfpamf.ms.insur.report.pojo.vo.EmployeeVo;

import java.util.List;

public interface InsuranceDailyRetrospective {

    /**
     * 异业客户转化指标-日复盘用
     *
     * @param empList 员工编码列表
     * @param bchCode 分支编码
     * @param pt 分区日期
     * @return 实例对象
     */
    List<DailyRetrospectiveBasicDTO> getLoanCustomerConvertMetrics(List<EmployeeVo> empList, String bchCode, String pt);
    /**
     * 信贷与非贷标保情况-日复盘用
     *
     * @param empCodes 员工编码列表
     * @param bchCode 分支编码
     * @param pt 分区日期
     * @return 实例对象
     */
    List<DailyRetrospectiveBasicDTO> getLoanAndUnloanMetrics(List<EmployeeVo> empCodes, String bchCode, String pt);

    /**
     * 客户留存情况-日复盘用
     *
     * @param empCodes 员工编码列表
     * @param bchCode 分支编码
     * @param pt 分区日期
     * @return 实例对象
     */
    List<DailyRetrospectiveBasicDTO> getCustomerRetentionMetrics(List<EmployeeVo> emps,String bchCode,String pt);

    List<DailyRetrospectiveBasicTrendDTO> getTrendInsuranceRate(String empCode, String bchCode, String date);

    List<UserVo> getUserInfoBySupervisor(String supervisorCode, String bchCode);

    /**
     * 客户跟进情况-日复盘用
     *
     * @param empCodes 员工编码列表
     * @param bchCode 分支编码
     * @param pt 分区日期
     * @return 实例对象
     */
    List<DailyRetrospectiveBasicDTO> getCustomerFollowMetrics(List<EmployeeVo> empCodes,String bchCode,String pt,String date);

    /**
     *
     * @param empCodes 员工编码列表
     * @param bchCode 分支编码
     * @param pt 分区日期
     * @return DailyRetrospectiveBasicDTO
     */
    List<DailyRetrospectiveBasicDTO> getCustomerConvertInsuranceMetrics(List<EmployeeVo> empCodes, String bchCode, String pt,String date,String monday);

    /**
     * 查询客户经理异业保费转化情况
     * @param empCode 员工工号
     * @param bchCode 分支编码
     * @param date 分区日期
     */
    DailyRetrospectiveFinanceStatisticsDTO getCustomerConvertPremium(String empCode, String bchCode, String date);
}
