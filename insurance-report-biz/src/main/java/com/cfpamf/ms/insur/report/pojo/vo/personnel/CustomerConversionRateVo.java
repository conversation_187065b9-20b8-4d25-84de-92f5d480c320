package com.cfpamf.ms.insur.report.pojo.vo.personnel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 客户转化率
 *
 * <AUTHOR>
 * @date 2021/3/30 11:27
 */
@Data
public class CustomerConversionRateVo {

    /**
     * 信贷客户转化率
     */
    @ApiModelProperty(value = "信贷客户转化率")
    private BigDecimal loaner;

    /**
     * 信贷相关客户转化率
     */
    @ApiModelProperty(value = "信贷相关客户转化率")
    private BigDecimal loanerRelevant;
}
