package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class EmployeeStatisiticVO extends SmBusinessDetailsVO implements Serializable {
    @ExportField(name = "员工人数",order = 6,type = "integer")
    @ApiModelProperty("员工人数")
    private Integer empCnt;

    @ExportField(name = "编制人数",order = 7,type = "integer")
    @ApiModelProperty("编制人数(即计划人数)")
    private Integer planEmpCnt;

    @ExportField(name = "人均保费",order = 10 ,type = "money")
    @ApiModelProperty("人均保费")
    private BigDecimal empAverageAmt;

    @ExportField(name = "编制人均保费",order = 11,type = "money")
    @ApiModelProperty("编制人均保费")
    private BigDecimal planEmpAverageAmt;
}
