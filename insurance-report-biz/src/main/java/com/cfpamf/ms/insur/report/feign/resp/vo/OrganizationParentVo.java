package com.cfpamf.ms.insur.report.feign.resp.vo;

import lombok.Data;

import java.util.List;

/**
 * 组织具备父信息的VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024年3月3日
 */
@Data
public class OrganizationParentVo extends OrganizationBaseVO {

    /**
     * 组织类型，已知：4为区域，6为片区，7为分支
     */
    private Integer orgCategory;

    /**
     * 上级组织对象
     */
    private List<OrganizationParentVo> superOrganizations;
}
