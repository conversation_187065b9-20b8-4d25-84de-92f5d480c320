package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AssistantManagerPromotionLevelDetail {
    public AssistantManagerPromotionLevelDetail(Integer rank){
        this.rank = rank;
    }
    public AssistantManagerPromotionLevelDetail(){
    }

    @ApiModelProperty(value = "层级名称", notes = "")
    String name;

    @ApiModelProperty(value = "当月分享数", notes = "")
    String smShareCnt;

    @ApiModelProperty(value = "当月分访客数", notes = "")
    String smVisitCnt;


    @ApiModelProperty(value = "当日分享", notes = "")
    String sdShareCnt;

    @ApiModelProperty(value = "当月访客数", notes = "")
    String sdVisitCnt;


    @ApiModelProperty(value = "排名", notes = "")
    Integer rank;

    @ApiModelProperty(value = "昨日分享数", notes = "")
    String cdShareCnt;

    @ApiModelProperty(value = "昨日分访客数", notes = "")
    String cdVisitCnt;
}
