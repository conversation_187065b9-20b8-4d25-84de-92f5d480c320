package com.cfpamf.ms.insur.report.service.retrospective.impl;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper;
import com.cfpamf.ms.insur.report.dao.safepg.DwaSafesPhoenixTodoEmpMapper;
import com.cfpamf.ms.insur.report.feign.BmsBizconfigFacade;
import com.cfpamf.ms.insur.report.feign.FinanceUnifiedDataFacade;
import com.cfpamf.ms.insur.report.feign.OperationFacade;
import com.cfpamf.ms.insur.report.feign.enums.EnumTodoBizType;
import com.cfpamf.ms.insur.report.feign.req.CustomerConversionQuery;
import com.cfpamf.ms.insur.report.feign.req.CustomerFollowCntQuery;
import com.cfpamf.ms.insur.report.feign.req.QueryCustomerConvertPremium;
import com.cfpamf.ms.insur.report.feign.resp.dto.CustomerConversionDto;
import com.cfpamf.ms.insur.report.feign.resp.dto.CustomerConvertPremiumDto;
import com.cfpamf.ms.insur.report.feign.resp.dto.CustomerFollowCntDto;
import com.cfpamf.ms.insur.report.feign.resp.dto.PolicyConversionAmtDto;
import com.cfpamf.ms.insur.report.feign.resp.vo.UserVo;
import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveBasicDTO;
import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveBasicTrendDTO;
import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveFinanceDataDTO;
import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveFinanceStatisticsDTO;
import com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceEmpMarketingProgressDfp;
import com.cfpamf.ms.insur.report.pojo.query.InsuranceDailyReviewFinanceDataQuery;
import com.cfpamf.ms.insur.report.pojo.vo.BorrowerInfoVo;
import com.cfpamf.ms.insur.report.pojo.vo.EmployeeVo;
import com.cfpamf.ms.insur.report.pojo.vo.InsuranceDailyReviewFinanceDataVo;
import com.cfpamf.ms.insur.report.service.BmsService;
import com.cfpamf.ms.insur.report.service.OperationService;
import com.cfpamf.ms.insur.report.service.retrospective.InsuranceDailyRetrospective;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

@Service
@Slf4j
public class InsuranceDailyRetrospectiveImpl implements InsuranceDailyRetrospective {
    @Autowired
    AdsInsuranceEmpMarketingProgressDfpMapper adsInsuranceEmpMarketingProgressDfpMapper;
    @Autowired
    BmsBizconfigFacade bmsBizconfigFacade;
    @Autowired
    OperationService phoenixService;
    @Autowired
    DwaSafesPhoenixTodoEmpMapper dwaSafesPhoenixTodoEmpMapper;

    @Autowired
    FinanceUnifiedDataFacade financeUnifiedDataFacade;

    @Autowired
    BmsService bmsService;

    @Autowired
    OperationFacade operationFacade;

    @Override
    public List<DailyRetrospectiveBasicDTO> getLoanCustomerConvertMetrics(List<EmployeeVo> empList, String bchCode, String pt) {
        List<String> empCodes = empList.stream().map(EmployeeVo::getEmpCode).collect(Collectors.toList());
        List<AdsInsuranceEmpMarketingProgressDfp> result = this.adsInsuranceEmpMarketingProgressDfpMapper.getLoanCustomerConvertMetrics(empCodes, bchCode, pt);
        List<DailyRetrospectiveBasicDTO> dtoList = empList.stream().map(emp -> {
            DailyRetrospectiveBasicDTO bean = new DailyRetrospectiveBasicDTO();
            BeanUtils.copyProperties(emp,bean);
            bean.setBchCode(bchCode);
            return bean;
        }).collect(Collectors.toList());
        LocalDate paramPt = LocalDate.parse(pt, BaseConstants.FMT_YYYYMMDD);
        if(paramPt.format(BaseConstants.FMT_YYYY_MM_DD).equals(LocalDate.now().toString())){
            List<DailyRetrospectiveBasicDTO> resultList = buildTodayLoanCustomerConvertMetrics(empList,bchCode,pt);
            dtoList.forEach(dto -> {
                List<DailyRetrospectiveBasicDTO> collect = resultList.stream().filter(vo -> vo.getEmpCode().equals(dto.getEmpCode())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(collect)){
                    BeanUtils.copyProperties(collect.get(0),dto,"empCode","empName","bchCode");
                }
            });
        }else{
            dtoList.forEach(dto -> {
                List<AdsInsuranceEmpMarketingProgressDfp> collect = result.stream().filter(vo -> vo.getEmpCode().equals(dto.getEmpCode())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(collect)){
                    this.convertPOToDTO(collect.get(0),dto);
                }
            });
        }
        return dtoList;
    }

    private List<DailyRetrospectiveBasicDTO> buildTodayLoanCustomerConvertMetrics(List<EmployeeVo> empList,String bchCode, String pt){
        LocalDate now = LocalDate.now();
        LocalDate paramPt = LocalDate.parse(pt, BaseConstants.FMT_YYYYMMDD);
        if(!paramPt.format(BaseConstants.FMT_YYYY_MM_DD).equals(now.toString())){
            return Lists.newArrayList();
        }
        if(CollectionUtils.isEmpty(empList)){
            return Lists.newArrayList();
        }
        List<String> empCodes = empList.stream().map(EmployeeVo::getEmpCode).collect(Collectors.toList());
        //查询t-1数据
        String pt1 = paramPt.minusDays(1).format(BaseConstants.FMT_YYYYMMDD);
        List<AdsInsuranceEmpMarketingProgressDfp> progressDfpList = this.adsInsuranceEmpMarketingProgressDfpMapper.getLoanCustomerConvertMetrics(empCodes, bchCode, pt1);
        //查询信贷授信金额
        InsuranceDailyReviewFinanceDataQuery financeDataQuery = new InsuranceDailyReviewFinanceDataQuery();
        financeDataQuery.setQueryDate(paramPt.format(BaseConstants.FMT_YYYY_MM_DD));
        financeDataQuery.setBranchCode(bchCode);
        financeDataQuery.setLoanManagerIds(empCodes);
        Result<List<InsuranceDailyReviewFinanceDataVo>> listResult = financeUnifiedDataFacade.queryInsuranceDailyReviewFinanceData(financeDataQuery);
        List<CustomerConvertPremiumDto> customerConvertPremium = new ArrayList<>();
        if(listResult.isSuccess()&&!CollectionUtils.isEmpty(listResult.getData())){
            Set<String> idCardSet = buildFinanceIdCardList(listResult.getData());
            if(!CollectionUtils.isEmpty(idCardSet)){
                customerConvertPremium = getCustomerConvertPremium(paramPt.format(BaseConstants.FMT_YYYY_MM_DD), idCardSet);
            }
        }

        List<DailyRetrospectiveBasicDTO> dtoList = Lists.newArrayList();
        for (EmployeeVo employeeVo : empList) {
            DailyRetrospectiveBasicDTO daily = new DailyRetrospectiveBasicDTO();
            AdsInsuranceEmpMarketingProgressDfp dfg = progressDfpList.stream().filter(a -> a.getEmpCode().equals(employeeVo.getEmpCode())).findFirst().orElse(null);
            if(Objects.nonNull(dfg)){
                BeanUtils.copyProperties(dfg, daily);
            }else{
                BeanUtils.copyProperties(employeeVo, daily);
            }
            //特殊处理每月1号数据
            boolean isFirst = false;
            if (LocalDate.now().isEqual(paramPt) && paramPt.getDayOfMonth() == 1) {
                isFirst = true;
            }
            List<InsuranceDailyReviewFinanceDataVo> collect = listResult.getData().stream().filter(vo -> daily.getEmpCode().equals(vo.getLoanManagerId())).collect(Collectors.toList());
            //当日放款金额
            BigDecimal appAmount = collect.stream().map(vo -> new BigDecimal(vo.getApplAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
            daily.setCdOfflineLoanAmt(appAmount.doubleValue());
            //当月放款金额
            BigDecimal smOfflineLoanAmt = appAmount.add(getInsuranceAmtBigDecimal(daily.getSmOfflineLoanAmt()));
            if(!isFirst){
                daily.setSmOfflineLoanAmt(smOfflineLoanAmt.doubleValue());
            }else{
                daily.setSmOfflineLoanAmt(appAmount.doubleValue());
            }
            //保费
            Set<String> filterCardSet = buildFinanceIdCardList(collect);
            List<CustomerConvertPremiumDto> filterCustomerConvertPremium = customerConvertPremium.stream().filter(vo -> filterCardSet.contains(vo.getIdCard())).filter(vo -> Objects.nonNull(vo.getConversionAmt()) && vo.getConversionAmt().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            //当日规模保费
            BigDecimal conversionAmt = filterCustomerConvertPremium.stream().map(CustomerConvertPremiumDto::getConversionAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            daily.setCdLoanInsuranceAmt(conversionAmt.doubleValue());
            //当月规模保费
            BigDecimal smLoanInsuranceAmt = conversionAmt.add(getInsuranceAmtBigDecimal(daily.getSmLoanInsuranceAmt()));
            if(!isFirst){
                daily.setSmLoanInsuranceAmt(smLoanInsuranceAmt.doubleValue());
            }else{
                daily.setSmLoanInsuranceAmt(conversionAmt.doubleValue());
            }
            //当日异业保费配比
            BigDecimal cdOfflineLoanInsuranceRate = BigDecimal.ZERO;
            if(getInsuranceAmtBigDecimal(daily.getCdOfflineLoanAmt()).compareTo(BigDecimal.ZERO) != 0){
                cdOfflineLoanInsuranceRate = getInsuranceAmtBigDecimal(daily.getCdLoanInsuranceAmt()).multiply(new BigDecimal(10000)).divide(getInsuranceAmtBigDecimal(daily.getCdOfflineLoanAmt()), 2, BigDecimal.ROUND_HALF_UP);
            }
            daily.setCdOfflineLoanInsuranceRate(cdOfflineLoanInsuranceRate.doubleValue());
            //本月异业保费配比
            BigDecimal smOfflineLoanInsuranceRate = BigDecimal.ZERO;
            if(getInsuranceAmtBigDecimal(daily.getSmOfflineLoanAmt()).compareTo(BigDecimal.ZERO) != 0){
                smOfflineLoanInsuranceRate = getInsuranceAmtBigDecimal(daily.getSmLoanInsuranceAmt()).multiply(new BigDecimal(10000)).divide(getInsuranceAmtBigDecimal(daily.getSmOfflineLoanAmt()), 2, BigDecimal.ROUND_HALF_UP);
            }
            daily.setSmOfflineLoanInsuranceRate(smOfflineLoanInsuranceRate.doubleValue());
            dtoList.add(daily);
        }
        //构建返回内容
        return dtoList;
    }

    private BigDecimal getInsuranceAmtBigDecimal(Double amt){
        if(amt == null){
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(amt);
    }

    @Override
    public List<DailyRetrospectiveBasicDTO> getLoanAndUnloanMetrics(List<EmployeeVo> emps, String bchCode, String pt) {
        List<DailyRetrospectiveBasicDTO> result = emps.stream().map(emp -> {
            DailyRetrospectiveBasicDTO dto =new DailyRetrospectiveBasicDTO();
            dto.setEmpCode(emp.getEmpCode());
            dto.setEmpName(emp.getEmpName());
            dto.setBchCode(bchCode);
            return dto;
        }).collect(Collectors.toList());
        List<String> empCodes = emps.stream().map(EmployeeVo::getEmpCode).collect(Collectors.toList());
        LocalDate paramPt = LocalDate.parse(pt, BaseConstants.FMT_YYYYMMDD);
        if(paramPt.isEqual(LocalDate.now())){
            Map<String, List<PolicyConversionAmtDto>> groupConversionAmt = getPolicyInCurrent(empCodes, paramPt);
            //获取当日信贷数据
            List<InsuranceDailyReviewFinanceDataVo> listResult = getFinanceDataInCurrent(empCodes, bchCode, paramPt);
            Set<String> loanCustomerIds = getAllLoanCustomerIdNos(listResult);

            // 初始化存放有贷款客户保险政策的列表和非贷款客户保险政策的列表
            List<PolicyConversionAmtDto> loanPolicies = new ArrayList<>();
            List<PolicyConversionAmtDto> unloanPolicies = new ArrayList<>();
            initLoanAndunLoanPolicyList(groupConversionAmt, loanCustomerIds, loanPolicies, unloanPolicies);

            //计算员工信贷与非贷标准保费
            Map<String,BigDecimal> loanConversionAmtEmp = new HashMap<>();
            Map<String,BigDecimal> unloanConversionAmtEmp = new HashMap<>();
            calcLoanAndUnLoanAmt(loanPolicies, loanConversionAmtEmp, unloanPolicies, unloanConversionAmtEmp);

            // 获取当前日期的前一天数仓指标
            List<DailyRetrospectiveBasicDTO> dtoList = getDailyRetrospectiveBasicDTOS(empCodes, bchCode, paramPt.minusDays(1).format(BaseConstants.FMT_YYYYMMDD), paramPt);
            // 将前一天的指标信息放入Map，按照员工编号分组
            Map<String ,DailyRetrospectiveBasicDTO> dtoMap = dtoList.stream().collect(toMap(DailyRetrospectiveBasicDTO::getEmpCode, dto -> dto));
            //组装指标对象
            result.forEach(dto->{
                calcCurrentDayMetrics(dto, loanConversionAmtEmp, dtoMap, unloanConversionAmtEmp);
                calcCurrentMonthMetrics(dto, loanConversionAmtEmp, dtoMap, unloanConversionAmtEmp,paramPt);
            });
            return result;
        }else if(paramPt.isBefore(LocalDate.now())){
            return getDailyRetrospectiveBasicDTOS(empCodes, bchCode, pt, paramPt);
        }else {
            return result;
        }

    }

    /**
     * 计算信贷和非贷当日标准保费
     */
    private void calcLoanAndUnLoanAmt(List<PolicyConversionAmtDto> loanPolicies, Map<String, BigDecimal> loanConversionAmtEmp, List<PolicyConversionAmtDto> unloanPolicies, Map<String, BigDecimal> unloanConversionAmtEmp) {
        loanPolicies.stream().collect(Collectors.groupingBy(PolicyConversionAmtDto::getEmpCode)).forEach((k, v)->{
            BigDecimal loanConversionAmt = v.stream().map(PolicyConversionAmtDto::getConversionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            loanConversionAmtEmp.put(k, loanConversionAmt);
        });

        unloanPolicies.stream().collect(Collectors.groupingBy(PolicyConversionAmtDto::getEmpCode)).forEach((k, v)->{
            BigDecimal unloanConversionAmt = v.stream().map(PolicyConversionAmtDto::getConversionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            unloanConversionAmtEmp.put(k, unloanConversionAmt);
        });
    }

    /**
     * 将保单分组初始化为信贷保单和非贷保单
     */
    private void initLoanAndunLoanPolicyList(Map<String, List<PolicyConversionAmtDto>> groupConversionAmt, Set<String> loanCustomerIds, List<PolicyConversionAmtDto> loanPolicies, List<PolicyConversionAmtDto> unloanPolicies) {
        // 遍历转换金额组，其中key为订单ID，value为订单列表
        groupConversionAmt.forEach((orderId, orderList)->{
            // 使用HashSet存放出现过的身份证号码，以去重
            Set<String> idNumbers = new HashSet<>();
            // 遍历订单列表中的每个项目，收集身份证号码
            orderList.forEach(item->{
                // 如果身份证号码不为空，则添加到idNumbers中
                if(!StringUtils.isEmpty(item.getIdNumber())){
                    idNumbers.add(item.getIdNumber());
                }
                // 如果投保人的身份证号码为空，也添加到idNumbers中
                if (StringUtils.isEmpty(item.getInsuredIdNumber())){
                    idNumbers.add(item.getIdNumber());
                }
            });
            // 判断idNumbers中是否包含任何贷款客户ID，如果是则将该订单列表加入到贷款客户保险政策列表中，否则加入到无贷款客户保险政策列表中
            if(CollectionUtils.containsAny(idNumbers, loanCustomerIds)){
                //订单存在多个被保人的情况所以保费均取第一个
                loanPolicies.add(orderList.get(0));
            }else{
                //订单存在多个被保人的情况所以保费均取第一个
                unloanPolicies.add(orderList.get(0));
            }
        });
    }

    /**
     * 计算当前日期的月度指标
     */
    private void calcCurrentMonthMetrics(DailyRetrospectiveBasicDTO dto, Map<String, BigDecimal> loanConversionAmtEmp, Map<String, DailyRetrospectiveBasicDTO> dtoMap, Map<String, BigDecimal> unloanConversionAmtEmp, LocalDate paramPt) {
        if (paramPt.isEqual(paramPt.with(TemporalAdjusters.firstDayOfMonth()))){
            //当前传参正好是当月第一天，此时也是当前日期。（数仓是T-1的数据）数仓指标没有本月指标，本月标准保费即为当日标准保费，数仓的当月指标为上月的指标。
            dto.setSmLoanAssessConvertInsuranceAmt(Optional.ofNullable(loanConversionAmtEmp.get(dto.getEmpCode())).orElse(BigDecimal.ZERO).doubleValue());
            //信贷相关指标
            if(dtoMap.get(dto.getEmpCode())==null){
                dto.setLmLoanAssessConvertInsuranceAmt(0.0);
            }else{
                dto.setLmLoanAssessConvertInsuranceAmt(Optional.ofNullable(dtoMap.get(dto.getEmpCode()).getSmLoanAssessConvertInsuranceAmt()).orElse(0.0));
            }
            if(dto.getLmLoanAssessConvertInsuranceAmt() != 0.0){
                dto.setLoanAssessConvertInsuranceAmtMom((dto.getSmLoanAssessConvertInsuranceAmt()- dto.getLmLoanAssessConvertInsuranceAmt())/ dto.getLmLoanAssessConvertInsuranceAmt());
            }
            //非贷相关指标
            dto.setSmUnloanAssessConvertInsuranceAmt(Optional.ofNullable(unloanConversionAmtEmp.get(dto.getEmpCode())).orElse(BigDecimal.ZERO).doubleValue());
            if(dtoMap.get(dto.getEmpCode())==null){
                dto.setLmUnloanAssessConvertInsuranceAmt(0.0);
            }else{
                dto.setLmUnloanAssessConvertInsuranceAmt(Optional.ofNullable(dtoMap.get(dto.getEmpCode()).getSmUnloanAssessConvertInsuranceAmt()).orElse(0.0));
            }
            if(dto.getLmUnloanAssessConvertInsuranceAmt() != 0.0){
                dto.setUnloanAssessConvertInsuranceAmtMom((dto.getSmUnloanAssessConvertInsuranceAmt()- dto.getLmUnloanAssessConvertInsuranceAmt())/ dto.getLmUnloanAssessConvertInsuranceAmt());
            }

        }else{
            //信贷相关指标
            if(dtoMap.get(dto.getEmpCode())==null){
                dto.setSmLoanAssessConvertInsuranceAmt(Optional.ofNullable(loanConversionAmtEmp.get(dto.getEmpCode())).orElse(BigDecimal.ZERO).doubleValue());
                dto.setLmLoanAssessConvertInsuranceAmt(0.0);
            }else{
                //离线指标不为空是当月信贷标准保费为loanConversionAmtEmp + dtoMap
                dto.setSmLoanAssessConvertInsuranceAmt(Optional.ofNullable(loanConversionAmtEmp.get(dto.getEmpCode())).orElse(BigDecimal.ZERO).doubleValue()+Optional.ofNullable(dtoMap.get(dto.getEmpCode()).getSmLoanAssessConvertInsuranceAmt()).orElse(0.0));
                dto.setLmLoanAssessConvertInsuranceAmt(Optional.ofNullable(dtoMap.get(dto.getEmpCode()).getLmLoanAssessConvertInsuranceAmt()).orElse(0.0));
                if(dto.getLmLoanAssessConvertInsuranceAmt() != 0.0){
                    dto.setLoanAssessConvertInsuranceAmtMom((dto.getSmLoanAssessConvertInsuranceAmt()- dto.getLmLoanAssessConvertInsuranceAmt())/ dto.getLmLoanAssessConvertInsuranceAmt());
                }
            }
            //非贷相关指标
            if(dtoMap.get(dto.getEmpCode())==null){
                dto.setSmUnloanAssessConvertInsuranceAmt(Optional.ofNullable(unloanConversionAmtEmp.get(dto.getEmpCode())).orElse(BigDecimal.ZERO).doubleValue());
                dto.setLmUnloanAssessConvertInsuranceAmt(0.0);
            }else{
                //离线指标不为空是当月非贷标准保费为unloanConversionAmtEmp + dtoMap
                dto.setSmUnloanAssessConvertInsuranceAmt(Optional.ofNullable(unloanConversionAmtEmp.get(dto.getEmpCode())).orElse(BigDecimal.ZERO).doubleValue()+Optional.ofNullable(dtoMap.get(dto.getEmpCode()).getSmUnloanAssessConvertInsuranceAmt()).orElse(0.0));
                dto.setLmUnloanAssessConvertInsuranceAmt(Optional.ofNullable(dtoMap.get(dto.getEmpCode()).getLmUnloanAssessConvertInsuranceAmt()).orElse(0.0));
                if(dto.getLmUnloanAssessConvertInsuranceAmt() != 0.0){
                    dto.setUnloanAssessConvertInsuranceAmtMom((dto.getSmUnloanAssessConvertInsuranceAmt()- dto.getLmUnloanAssessConvertInsuranceAmt())/ dto.getLmUnloanAssessConvertInsuranceAmt());
                }
            }
        }
    }

    /**
     * 计算当前日期的日度指标
     */
    private static void calcCurrentDayMetrics(DailyRetrospectiveBasicDTO dto, Map<String, BigDecimal> loanConversionAmtEmp, Map<String, DailyRetrospectiveBasicDTO> dtoMap, Map<String, BigDecimal> unloanConversionAmtEmp) {
        dto.setCdLoanAssessConvertInsuranceAmt(Optional.ofNullable(loanConversionAmtEmp.get(dto.getEmpCode())).orElse(BigDecimal.ZERO).doubleValue());
        if(dtoMap.get(dto.getEmpCode())==null){
            dto.setYdLoanAssessConvertInsuranceAmt(0.0);
        }else{
            dto.setYdLoanAssessConvertInsuranceAmt(Optional.ofNullable(dtoMap.get(dto.getEmpCode()).getYdLoanAssessConvertInsuranceAmt()).orElse(0.0));
        }
        if(dto.getYdLoanAssessConvertInsuranceAmt() != 0.0){
            dto.setLoanAssessConvertInsuranceAmtDod((dto.getCdLoanAssessConvertInsuranceAmt()- dto.getYdLoanAssessConvertInsuranceAmt())/ dto.getYdLoanAssessConvertInsuranceAmt());
        }
        dto.setCdUnloanAssessConvertInsuranceAmt(Optional.ofNullable(unloanConversionAmtEmp.get(dto.getEmpCode())).orElse(BigDecimal.ZERO).doubleValue());
        if(dtoMap.get(dto.getEmpCode())==null){
            dto.setYdUnloanAssessConvertInsuranceAmt(0.0);
        }else{
            dto.setYdUnloanAssessConvertInsuranceAmt(Optional.ofNullable(dtoMap.get(dto.getEmpCode()).getYdUnloanAssessConvertInsuranceAmt()).orElse(0.0));
        }
        if(dto.getYdUnloanAssessConvertInsuranceAmt() != 0.0){
            dto.setUnloanAssessConvertInsuranceAmtDod((dto.getCdUnloanAssessConvertInsuranceAmt()- dto.getYdUnloanAssessConvertInsuranceAmt())/ dto.getYdUnloanAssessConvertInsuranceAmt());
        }
    }

    /**
     * 获取数仓员工指标
     */
    private List<DailyRetrospectiveBasicDTO> getDailyRetrospectiveBasicDTOS(List<String> empCodes, String bchCode, String pt, LocalDate paramPt) {
        String yesterdayPt = paramPt.minusDays(1).format(BaseConstants.FMT_YYYYMMDD);
        String maxPt = "";
        if(LocalDate.now().getMonthValue() <= paramPt.getMonthValue()){
            maxPt = LocalDate.now().minusDays(1).format(BaseConstants.FMT_YYYYMMDD);
        }else{
            maxPt = paramPt.with(TemporalAdjusters.lastDayOfMonth()).format(BaseConstants.FMT_YYYYMMDD);
        }
        List<AdsInsuranceEmpMarketingProgressDfp> result = this.adsInsuranceEmpMarketingProgressDfpMapper.getLoanAndUnloanMetrics(empCodes, bchCode, pt, yesterdayPt, maxPt);
        List<DailyRetrospectiveBasicDTO> dtoList= Optional.ofNullable(result).orElse(new ArrayList<>()).stream().map(this::convertPOToDTO).collect(Collectors.toList());
        return dtoList;
    }

    /**
     * 获取所有信贷相关人员身份证列表
     */
    private Set<String> getAllLoanCustomerIdNos(List<InsuranceDailyReviewFinanceDataVo> listResult) {
        Set<String> loanCustomerIds = new HashSet<>();
        listResult.forEach(item->{
            if(item.getBorrowerInfoVo() != null){
                loanCustomerIds.add(item.getBorrowerInfoVo().getIdCardNo());
            }
            if(item.getCoborrowerVoList() != null){
                item.getCoborrowerVoList().forEach(coborrower->{
                    loanCustomerIds.add(coborrower.getIdCardNo());
                });
            }
            if(item.getBondsmanVoList() != null){
                item.getBondsmanVoList().forEach(bondsman->{
                    loanCustomerIds.add(bondsman.getIdCardNo());
                });
            }
        });
        return loanCustomerIds;
    }

    /**
     * 获取当日信贷放款记录信息
     */
    private List<InsuranceDailyReviewFinanceDataVo> getFinanceDataInCurrent(List<String> empCodes, String bchCode, LocalDate paramPt) {
        InsuranceDailyReviewFinanceDataQuery query = new InsuranceDailyReviewFinanceDataQuery();
        query.setQueryDate(paramPt.format(BaseConstants.FMT_YYYY_MM_DD));
        query.setBranchCode(bchCode);
        query.setLoanManagerIds(empCodes);
        List<InsuranceDailyReviewFinanceDataVo> listResult = financeUnifiedDataFacade.queryInsuranceDailyReviewFinanceData(query).getData();
        return listResult;
    }

    /**
     * 获取当日保险数据
     */
    private Map<String, List<PolicyConversionAmtDto>> getPolicyInCurrent(List<String> empCodes, LocalDate paramPt) {
        //获取当日保险数据
        CustomerConversionQuery conversionQuery = CustomerConversionQuery.builder().empCodes(empCodes)
                .startDate(paramPt.atStartOfDay()).endDate(paramPt.plusDays(1).atStartOfDay()).build();
        List<PolicyConversionAmtDto> conversionAmtList =operationFacade.getPolicyListForConversionAmt(conversionQuery).getData();
        Map<String,List<PolicyConversionAmtDto>> groupConversionAmt = Optional.ofNullable(conversionAmtList).orElse(new ArrayList<>())
                .stream().collect(Collectors.groupingBy(PolicyConversionAmtDto::getOrderId));
        return groupConversionAmt;
    }

    @Override
    public List<DailyRetrospectiveBasicDTO> getCustomerRetentionMetrics(List<EmployeeVo> emps, String bchCode, String pt) {
        List<DailyRetrospectiveBasicDTO> resultDto = new ArrayList<>();
        emps.forEach(employeeVo -> {
            DailyRetrospectiveBasicDTO dto = new DailyRetrospectiveBasicDTO();
            dto.setBchCode(bchCode);
            dto.setEmpName(employeeVo.getEmpName());
            dto.setEmpCode(employeeVo.getEmpCode());
            resultDto.add(dto);
        });
        List<String> empCodes = emps.stream().map(EmployeeVo::getEmpCode).collect(Collectors.toList());
        List<AdsInsuranceEmpMarketingProgressDfp> result = this.adsInsuranceEmpMarketingProgressDfpMapper.getCustomerRetentionMetrics(empCodes, bchCode, pt);
        resultDto.forEach(dto ->{
            List<AdsInsuranceEmpMarketingProgressDfp> temp = result.stream().filter(item->item.getEmpCode().equals(dto.getEmpCode())).limit(1).collect(Collectors.toList());
            if (!temp.isEmpty()){
                this.convertPOToDTO(temp.get(0),dto);
            }
        });
        return resultDto;
    }

    public List<DailyRetrospectiveBasicTrendDTO> getTrendInsuranceRate(String empCode,String bchCode, String date){
        List<String> pts = getAllPtsInMonth(date);
        List<AdsInsuranceEmpMarketingProgressDfp> result = this.adsInsuranceEmpMarketingProgressDfpMapper.getTrendInsuranceRateWithBch(empCode,bchCode,pts);
        List<DailyRetrospectiveBasicTrendDTO> dtoList =  result.stream().map(item->{
            DailyRetrospectiveBasicTrendDTO dto =new DailyRetrospectiveBasicTrendDTO();
            dto.setEmpCode(empCode);
            if(item.getSyOfflineLoanInsuranceRate() == null){
                dto.setValue(null);
            }else{
                dto.setValue(BigDecimal.valueOf(item.getSyOfflineLoanInsuranceRate()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            dto.setDate(LocalDate.parse(item.getPt(), BaseConstants.FMT_YYYYMMDD).format(BaseConstants.FMT_YYYY_MM_DD));
            return dto;
        }).collect(Collectors.toList());
        return dtoList;
    }

    private List<String> getAllPtsInMonth(String date){
        LocalDate firstdayOffMonth = LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastDayOffMonth = LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD).with(TemporalAdjusters.lastDayOfMonth());
        //localDate 获取当月的所有日期列表
        Long gapDays = ChronoUnit.DAYS.between(firstdayOffMonth,lastDayOffMonth);
        List<String> pts = new ArrayList<>();
        for(int i = 0; i <= gapDays; i++){
            pts.add(firstdayOffMonth.plusDays(i).format(BaseConstants.FMT_YYYYMMDD));
        }
        return pts;
    }

    @Override
    public List<UserVo> getUserInfoBySupervisor(String supervisorCode, String bchCode) {
        String authorization = bmsService.getToken();
        return bmsBizconfigFacade.getUserInfoBySupervisor(authorization, bchCode, supervisorCode).getData();
    }

    private DailyRetrospectiveBasicDTO convertPOToDTO(AdsInsuranceEmpMarketingProgressDfp po){
        DailyRetrospectiveBasicDTO dto = new DailyRetrospectiveBasicDTO();
        BeanUtils.copyProperties(po,dto);
        return dto;
    }

    private DailyRetrospectiveBasicDTO convertPOToDTO(AdsInsuranceEmpMarketingProgressDfp po, DailyRetrospectiveBasicDTO dto){
        BeanUtils.copyProperties(po,dto,"empCode","empName","bchCode");
        return dto;
    }

    @Override
    public List<DailyRetrospectiveBasicDTO> getCustomerFollowMetrics(List<EmployeeVo> empVos, String bchCode, String pt,String date) {
        List<String> empCodes = empVos.stream().map(EmployeeVo::getEmpCode).collect(Collectors.toList());
        List<DailyRetrospectiveBasicDTO> result = empVos.stream().map(emp -> {
            DailyRetrospectiveBasicDTO dto =new DailyRetrospectiveBasicDTO();
            dto.setEmpCode(emp.getEmpCode());
            dto.setEmpName(emp.getEmpName());
            dto.setBchCode(bchCode);
            return dto;
        }).collect(Collectors.toList());

        CustomerFollowCntQuery query = new CustomerFollowCntQuery();
        query.setEmpCodes(empCodes);
        query.setStartDate(LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD).atStartOfDay());
        query.setEndDate(LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD).atTime(23,59,59));

        //查询当日指标
        List<DailyRetrospectiveBasicDTO> cdList = initCdFollowMetrics(query);
        Map<String,DailyRetrospectiveBasicDTO> cdMap = cdList.stream()
                .collect(Collectors.toMap(DailyRetrospectiveBasicDTO::getEmpCode, obj -> obj));

        //查询历史分区指标
        List<DailyRetrospectiveBasicDTO> dtoList = initCustomerFollowMetrics(empCodes,bchCode,pt,cdMap,date);
        if (CollectionUtils.isEmpty(dtoList)) {
            result.forEach(item -> {
                if (cdMap.containsKey(item.getEmpCode())) {
                    BeanUtils.copyProperties(cdMap.get(item.getEmpCode()), item,"empCode","empName","bchCode");
                    item.setBchCode(bchCode);
                }
            });
        } else {
            Map<String,DailyRetrospectiveBasicDTO> dtoMap = dtoList.stream()
                    .collect(Collectors.toMap(DailyRetrospectiveBasicDTO::getEmpCode, obj -> obj));
            result.forEach(item -> {
                if (dtoMap.containsKey(item.getEmpCode())) {
                    BeanUtils.copyProperties(dtoMap.get(item.getEmpCode()), item,"empCode","empName","bchCode");
                }
            });
        }
        return result;
    }

    private List<DailyRetrospectiveBasicDTO> initCustomerFollowMetrics(List<String> empCodes, String bchCode, String pt, Map<String, DailyRetrospectiveBasicDTO> cdMap,String date) {
        List<DailyRetrospectiveBasicDTO> dtoList = dwaSafesPhoenixTodoEmpMapper.getCustomerFollowMetrics(empCodes,bchCode,pt);
        if (!CollectionUtils.isEmpty(dtoList)) {
            return dtoList.stream().peek(item -> {
                //如果参数日期为当天且是当月第一天，则将本月指标置为0
                LocalDate dateFormat = LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD);
                if (LocalDate.now().isEqual(dateFormat) && dateFormat.getDayOfMonth() == 1) {
                    item.setSmInterruptionTodoFollowCnt(0);
                    item.setSmTdoShortFollowCnt(0);
                    item.setSmTdoLongFollowCnt(0);
                    item.setSmInterruptionTdoFollowCust(0);
                    item.setSmInterruptionTodoConversionAmt(BigDecimal.ZERO.doubleValue());
                    item.setSmTdoShortFollowPolicy(0);
                    item.setSmRenewShortTodoConversionAmt(BigDecimal.ZERO.doubleValue());
                    item.setSmTdoLongFollowPolicy(0);
                    item.setSmRenewLongTodoConversionAmt(BigDecimal.ZERO.doubleValue());
                }

                DailyRetrospectiveBasicDTO cdDto = cdMap.containsKey(item.getEmpCode())?cdMap.get(item.getEmpCode()):new DailyRetrospectiveBasicDTO();

                Integer cdInterruptionTodoFollowCnt = cdMap.containsKey(item.getEmpCode())?cdMap.get(item.getEmpCode()).getCdInterruptionTodoFollowCnt():0;
                Integer cdTodoShortFollowCnt = cdMap.containsKey(item.getEmpCode())?cdMap.get(item.getEmpCode()).getCdTodoShortFollowCnt():0;
                Integer cdTodoLongFollowCnt = cdMap.containsKey(item.getEmpCode())?cdMap.get(item.getEmpCode()).getCdTodoLongFollowCnt():0;

                item.setCdInterruptionTodoFollowCnt(cdInterruptionTodoFollowCnt);
                item.setCdTodoShortFollowCnt(cdTodoShortFollowCnt);
                item.setCdTodoLongFollowCnt(cdTodoLongFollowCnt);
                item.setCdInterruptionTdoFollowCust(Objects.isNull(cdDto.getCdInterruptionTdoFollowCust())?0:cdDto.getCdInterruptionTdoFollowCust());
                item.setCdTdoShortFollowPolicy(Objects.isNull(cdDto.getCdTdoShortFollowPolicy())?0:cdDto.getCdTdoShortFollowPolicy());
                item.setCdTdoLongFollowPolicy(Objects.isNull(cdDto.getCdTdoLongFollowPolicy())?0:cdDto.getCdTdoLongFollowPolicy());
                item.setCdInterruptionTodoConversionAmt(Objects.isNull(cdDto.getCdInterruptionTodoConversionAmt())?BigDecimal.ZERO.doubleValue():cdDto.getCdInterruptionTodoConversionAmt());
                item.setCdRenewShortTodoConversionAmt(Objects.isNull(cdDto.getCdRenewShortTodoConversionAmt())?BigDecimal.ZERO.doubleValue():cdDto.getCdRenewShortTodoConversionAmt());
                item.setCdRenewLongTodoConversionAmt(Objects.isNull(cdDto.getCdRenewLongTodoConversionAmt())?BigDecimal.ZERO.doubleValue():cdDto.getCdRenewLongTodoConversionAmt());

                if (LocalDate.now().isEqual(dateFormat)) {
                    item.setSmInterruptionTodoFollowCnt(item.getSmInterruptionTodoFollowCnt() + cdInterruptionTodoFollowCnt);
                    item.setSmTdoShortFollowCnt(item.getSmTdoShortFollowCnt() + cdTodoShortFollowCnt);
                    item.setSmTdoLongFollowCnt(item.getSmTdoLongFollowCnt() + cdTodoLongFollowCnt);
                    item.setSmInterruptionTdoFollowCust(item.getSmInterruptionTodoFollowCnt()+item.getCdInterruptionTdoFollowCust());
                    item.setSmTdoShortFollowPolicy(item.getSmTdoShortFollowPolicy()+item.getCdTdoShortFollowPolicy());
                    item.setSmTdoLongFollowPolicy(item.getSmTdoLongFollowPolicy()+item.getCdTdoLongFollowPolicy());
                    item.setSmInterruptionTodoConversionAmt(item.getSmInterruptionTodoConversionAmt()+item.getCdInterruptionTodoConversionAmt());
                    item.setSmRenewShortTodoConversionAmt(item.getSmRenewShortTodoConversionAmt()+item.getCdRenewShortTodoConversionAmt());
                    item.setSmRenewLongTodoConversionAmt(item.getSmRenewLongTodoConversionAmt()+item.getCdRenewLongTodoConversionAmt());
                }
            }).collect(Collectors.toList());
        }
        return dtoList;
    }

    /**
     * 初始化当日数据
     * @param query 查询参数
     * @return DailyRetrospectiveBasicDTO
     */
    private List<DailyRetrospectiveBasicDTO> initCdFollowMetrics(CustomerFollowCntQuery query) {
        query.setBizType(EnumTodoBizType.INTERRUPTION);
        List<CustomerFollowCntDto> interruptionFollowList = phoenixService.getCustomerFollowCnt(query);

        query.setBizType(EnumTodoBizType.RENEW_SHORT);
        List<CustomerFollowCntDto> shortFollowList = phoenixService.getCustomerFollowCnt(query);

        query.setBizType(EnumTodoBizType.RENEW_LONG);
        List<CustomerFollowCntDto> longFollowList = phoenixService.getCustomerFollowCnt(query);

        List<CustomerFollowCntDto> allFollowList = new ArrayList<>();
        allFollowList.addAll(interruptionFollowList);
        allFollowList.addAll(shortFollowList);
        allFollowList.addAll(longFollowList);

        if (!CollectionUtils.isEmpty(allFollowList)) {
            return allFollowList.stream()
                    .collect(Collectors.groupingBy(CustomerFollowCntDto::getEmpCode))
                    .values().stream()
                    .map(list -> {
                        int interruptionFollowCnt = 0;
                        int shortFollowCnt = 0;
                        int longFollowCnt = 0;
                        DailyRetrospectiveBasicDTO resultDto = new DailyRetrospectiveBasicDTO();

                        for (CustomerFollowCntDto dto : list) {
                            resultDto.setEmpCode(dto.getEmpCode());
                            resultDto.setEmpName(dto.getEmpName());
                            resultDto.setBchCode(dto.getBchCode());
                            resultDto.setBchName(dto.getBchName());

                            if (EnumTodoBizType.INTERRUPTION.name().equals(dto.getBizType())) {
                                interruptionFollowCnt = Objects.isNull(dto.getFollowCnt())?0:dto.getFollowCnt();
                                resultDto.setCdInterruptionTdoFollowCust(Objects.isNull(dto.getConversionCnt())?0:dto.getConversionCnt());
                                resultDto.setCdInterruptionTodoConversionAmt(Objects.isNull(dto.getConversionAmt())?BigDecimal.ZERO.doubleValue():dto.getConversionAmt().doubleValue());
                            } else if (EnumTodoBizType.RENEW_SHORT.name().equals(dto.getBizType())) {
                                shortFollowCnt = Objects.isNull(dto.getFollowCnt())?0:dto.getFollowCnt();
                                resultDto.setCdTdoShortFollowPolicy(Objects.isNull(dto.getConversionCnt())?0:dto.getConversionCnt());
                                resultDto.setCdRenewShortTodoConversionAmt(Objects.isNull(dto.getConversionAmt())?BigDecimal.ZERO.doubleValue():dto.getConversionAmt().doubleValue());
                            } else if (EnumTodoBizType.RENEW_LONG.name().equals(dto.getBizType())) {
                                longFollowCnt = Objects.isNull(dto.getFollowCnt())?0:dto.getFollowCnt();
                                resultDto.setCdTdoLongFollowPolicy(Objects.isNull(dto.getConversionCnt())?0:dto.getConversionCnt());
                                resultDto.setCdRenewLongTodoConversionAmt(Objects.isNull(dto.getConversionAmt())?BigDecimal.ZERO.doubleValue():dto.getConversionAmt().doubleValue());
                            }
                        }

                        resultDto.setCdInterruptionTodoFollowCnt(interruptionFollowCnt);
                        resultDto.setCdTodoShortFollowCnt(shortFollowCnt);
                        resultDto.setCdTodoLongFollowCnt(longFollowCnt);
                        return resultDto;
                    })
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public List<DailyRetrospectiveBasicDTO> getCustomerConvertInsuranceMetrics(List<EmployeeVo> empVos, String bchCode, String pt,String date,String monday) {
        List<DailyRetrospectiveBasicDTO> result = empVos.stream().map(emp -> {
            DailyRetrospectiveBasicDTO dto =new DailyRetrospectiveBasicDTO();
            dto.setEmpCode(emp.getEmpCode());
            dto.setEmpName(emp.getEmpName());
            dto.setBchCode(bchCode);
            return dto;
        }).collect(Collectors.toList());

        List<String> empCodes = empVos.stream().map(EmployeeVo::getEmpCode).collect(Collectors.toList());

        List<AdsInsuranceEmpMarketingProgressDfp> adsInsuranceEmpMarketingProgressDfps = this.adsInsuranceEmpMarketingProgressDfpMapper.getCustomerConvertInsuranceMetrics(empCodes, bchCode, pt,date,monday);
        List<DailyRetrospectiveBasicDTO> dtoList= Optional.ofNullable(adsInsuranceEmpMarketingProgressDfps).orElse(new ArrayList<>()).stream().map(this::convertPOToDTO).collect(Collectors.toList());
        Map<String,DailyRetrospectiveBasicDTO> dtoMap = dtoList.stream()
                .collect(Collectors.toMap(DailyRetrospectiveBasicDTO::getEmpCode, obj -> obj));

        if (LocalDate.now().isEqual(LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD))) {
            CustomerConversionQuery conversionQuery = CustomerConversionQuery
                    .builder()
                    .endDate(LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD).atTime(23,59,59))
                    .startDate(LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD).atStartOfDay())
                    .empCodes(empCodes)
                    .build();
            Map<String, Double> conversionMap = phoenixService.getCustomerConversion(conversionQuery)
                    .stream()
                    .collect(toMap(
                            CustomerConversionDto::getEmpCode,
                            customerConversionDto -> customerConversionDto.getConversionAmt().doubleValue()));

            if (CollectionUtils.isEmpty(dtoList) && !CollectionUtils.isEmpty(conversionMap)) {
                result.forEach(item -> {
                    if (conversionMap.containsKey(item.getEmpCode())) {
                        item.setCdAssessConvertInsuranceAmt(conversionMap.get(item.getEmpCode()));
                        item.setSwAssessConvertInsuranceAmt(conversionMap.get(item.getEmpCode()));
                        item.setSmAssessConvertInsuranceAmt(conversionMap.get(item.getEmpCode()));
                        item.setSyAssessConvertInsuranceAmt(conversionMap.get(item.getEmpCode()));
                    }
                });
                return result;
            }

            dtoList.forEach(item -> {
                Double conversionAmt = conversionMap.getOrDefault(item.getEmpCode(), BigDecimal.ZERO.doubleValue());

                //如果传递日期为周一、每月初一或每年01-01 则将对应指标置为0
                if (LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD).getDayOfWeek().getValue() == 1) {
                    item.setSwAssessConvertInsuranceAmt(BigDecimal.ZERO.doubleValue());
                }
                if (LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD).getDayOfMonth() == 1) {
                    item.setSmAssessConvertInsuranceAmt(BigDecimal.ZERO.doubleValue());
                }
                if (LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD).getMonthValue() == 1
                        && LocalDate.parse(date, BaseConstants.FMT_YYYY_MM_DD).getDayOfMonth() == 1) {
                    item.setSyAssessConvertInsuranceAmt(BigDecimal.ZERO.doubleValue());
                }

                item.setCdAssessConvertInsuranceAmt(conversionAmt);
                item.setSwAssessConvertInsuranceAmt(item.getSwAssessConvertInsuranceAmt() + conversionAmt);
                item.setSmAssessConvertInsuranceAmt(item.getSmAssessConvertInsuranceAmt() + conversionAmt);
                item.setSyAssessConvertInsuranceAmt(item.getSyAssessConvertInsuranceAmt() + conversionAmt);
            });
        }

        result.forEach(item -> {
            if (dtoMap.containsKey(item.getEmpCode())) {
                BeanUtils.copyProperties(dtoMap.get(item.getEmpCode()), item,"empCode","empName","bchCode");
            }
        });
        return result;
    }

    private Map<String, List<InsuranceDailyReviewFinanceDataVo>> groupFinanceResults(List<InsuranceDailyReviewFinanceDataVo> result){
        return result.stream().collect(Collectors.groupingBy(InsuranceDailyReviewFinanceDataVo::getLoanManagerId));
    }

    @Override
    public DailyRetrospectiveFinanceStatisticsDTO getCustomerConvertPremium(String empCode, String bchCode, String date) {
        InsuranceDailyReviewFinanceDataQuery query = new InsuranceDailyReviewFinanceDataQuery();
        query.setQueryDate(date);
        query.setBranchCode(bchCode);
        query.setLoanManagerIds(Arrays.asList(empCode));
        Result<List<InsuranceDailyReviewFinanceDataVo>> listResult = financeUnifiedDataFacade.queryInsuranceDailyReviewFinanceData(query);
        log.info("通过客户经理工号:{},日期:{},获取到的信贷客户数据为:{}",empCode,date,listResult);
        if(!listResult.isSuccess()){
            return null;
        }
        if(CollectionUtils.isEmpty(listResult.getData())){
            return null;
        }
        Set<String> idCardList = buildFinanceIdCardList(listResult.getData());
        if(CollectionUtils.isEmpty(idCardList)){
            return null;
        }
        //查询前后15日 保单情况
        List<CustomerConvertPremiumDto> customerConvertPremium = getCustomerConvertPremium(date, idCardList);
        //处理异业保费配比
        List<DailyRetrospectiveFinanceDataDTO> outList= listResult.getData().stream().map(a -> {
            DailyRetrospectiveFinanceDataDTO bean = new DailyRetrospectiveFinanceDataDTO();
            BeanUtils.copyProperties(a,bean);
            Set<String> allCardList = Sets.newHashSet();
            //主借人转化数
            if(Objects.nonNull(a.getBorrowerInfoVo())){
                long count = customerConvertPremium.stream().filter(b -> b.getIdCard().equals(a.getBorrowerInfoVo().getIdCardNo()) && b.isConversionPerson()).count();
                allCardList.add(a.getBorrowerInfoVo().getIdCardNo());
                bean.setBorrowerConvertNum(Integer.valueOf(String.valueOf(count)));
            }
            //共借人转化数
            if(!CollectionUtils.isEmpty(a.getCoborrowerVoList())){
                List<String> coborrowerCardList = a.getCoborrowerVoList().stream().map(BorrowerInfoVo::getIdCardNo).collect(Collectors.toList());
                allCardList.addAll(coborrowerCardList);
                long count = customerConvertPremium.stream().filter(b -> coborrowerCardList.contains(b.getIdCard())).filter(CustomerConvertPremiumDto::isConversionPerson).count();
                bean.setCoborrowerConvertNum(Integer.valueOf(String.valueOf(count)));
            }
            //担保人转化数
            if(!CollectionUtils.isEmpty(a.getBondsmanVoList())){
                List<String> bondsmanCardList = a.getBondsmanVoList().stream().map(BorrowerInfoVo::getIdCardNo).collect(Collectors.toList());
                allCardList.addAll(bondsmanCardList);
                long count = customerConvertPremium.stream().filter(b -> bondsmanCardList.contains(b.getIdCard())).filter(CustomerConvertPremiumDto::isConversionPerson).count();
                bean.setBondsmanConvertNum(Integer.valueOf(String.valueOf(count)));
            }
            //转化保费和配比
            BigDecimal conversionAmt = BigDecimal.ZERO;
            List<CustomerConvertPremiumDto> collect = customerConvertPremium.stream().filter(b -> allCardList.contains(b.getIdCard())).filter(b -> Objects.nonNull(b.getConversionAmt())&&b.getConversionAmt().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(collect)){
                conversionAmt = collect.stream().map(CustomerConvertPremiumDto::getConversionAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                bean.setConversionAmt(conversionAmt);
                bean.setConversionRate(conversionAmt.multiply(new BigDecimal(10000)).divide(new BigDecimal(bean.getApplAmount()), 2, BigDecimal.ROUND_HALF_UP));
            }else{
                bean.setConversionAmt(BigDecimal.ZERO.setScale(0));
                bean.setConversionRate(BigDecimal.ZERO.setScale(0));
            }
            return bean;
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(outList)){
            return null;
        }
        List<String> empCodes = Lists.newArrayList();
        empCodes.add(empCode);
        List<AdsInsuranceEmpMarketingProgressDfp> list = this.adsInsuranceEmpMarketingProgressDfpMapper.getLoanCustomerConvertMetrics(empCodes, bchCode, date);
        return buildFinanceStatisticsDTO(outList,list);
    }

    private DailyRetrospectiveFinanceStatisticsDTO buildFinanceStatisticsDTO(List<DailyRetrospectiveFinanceDataDTO> outList,List<AdsInsuranceEmpMarketingProgressDfp> list){
        DailyRetrospectiveFinanceStatisticsDTO out = new DailyRetrospectiveFinanceStatisticsDTO();
        out.setList(outList);
        //全国标准水平的异业保费配比值
        if(!CollectionUtils.isEmpty(list)){
            out.setSmOfflineLoanInsuranceRateTarget(list.get(0).getSmOfflineLoanInsuranceRateTarget());
        }
        for (DailyRetrospectiveFinanceDataDTO bean : outList) {
            BigDecimal applAmount = getInsuranceAmtStringBigDecimal(bean.getApplAmount()).add(getInsuranceAmtStringBigDecimal(out.getApplAmountSum()));
            out.setApplAmountSum(applAmount.setScale(2, RoundingMode.DOWN).toString());
            BigDecimal conversionAmtSum = bean.getConversionAmt().add(out.getConversionAmtSum());
            out.setConversionAmtSum(conversionAmtSum.setScale(2, RoundingMode.DOWN));
            BigDecimal conversionRateSum = bean.getConversionRate().add(out.getConversionRateSum());
            out.setConversionRateSum(conversionRateSum.setScale(2, RoundingMode.DOWN));
            if(StringUtils.isEmpty(out.getApplTimeSum())){
               out.setApplTimeSum(bean.getApplTime());
            }
            out.setBorrowerConvertSum(out.getBorrowerConvertSum()+bean.getBorrowerConvertNum());
            out.setCoborrowerConvertSum(out.getCoborrowerConvertSum()+bean.getCoborrowerConvertNum());
            out.setBondsmanConvertSum(out.getBondsmanConvertSum()+bean.getBondsmanConvertNum());
            if(StringUtils.isEmpty(out.getBusinessTypeSum())){
                out.setBusinessTypeSum(bean.getBusinessType());
            }
        }
        return out;
    }

    private BigDecimal getInsuranceAmtStringBigDecimal(String value){
        if(StringUtils.isEmpty(value)){
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(Double.valueOf(value));
    }

    private Set<String> buildFinanceIdCardList(List<InsuranceDailyReviewFinanceDataVo> listResult){
        Set<String> idCardList = Sets.newHashSet();
        for (InsuranceDailyReviewFinanceDataVo datum : listResult) {
            //借款人
            if(Objects.nonNull(datum.getBorrowerInfoVo())&& !StringUtils.isEmpty(datum.getBorrowerInfoVo().getIdCardNo())){
                idCardList.add(datum.getBorrowerInfoVo().getIdCardNo());
            }
            //共借人
            Set<String> collect = datum.getBondsmanVoList().stream().filter(b -> !StringUtils.isEmpty(b.getIdCardNo())).map(BorrowerInfoVo::getIdCardNo).collect(Collectors.toSet());
            idCardList.addAll(collect);
            //担保人
            Set<String> collect1 = datum.getCoborrowerVoList().stream().filter(b -> !StringUtils.isEmpty(b.getIdCardNo())).map(BorrowerInfoVo::getIdCardNo).collect(Collectors.toSet());
            idCardList.addAll(collect1);
        }
        return idCardList;
    }

    private List<CustomerConvertPremiumDto> getCustomerConvertPremium(String date, Set<String> idCardList) {
        List<CustomerConvertPremiumDto> list = new ArrayList<>();
        //个险
        QueryCustomerConvertPremium queryCustomerConvertPremium = new QueryCustomerConvertPremium();
        queryCustomerConvertPremium.setDate(date);
        queryCustomerConvertPremium.setIdCardList(new ArrayList<>(idCardList));
        queryCustomerConvertPremium.setProductAttrCode("person");
        List<CustomerConvertPremiumDto> customerConvertPremium = phoenixService.getCustomerConvertPremium(queryCustomerConvertPremium);
        list.addAll(customerConvertPremium);
        //团险
        QueryCustomerConvertPremium queryGroupCustomerConvertPremium = new QueryCustomerConvertPremium();
        queryGroupCustomerConvertPremium.setDate(date);
        queryGroupCustomerConvertPremium.setIdCardList(new ArrayList<>(idCardList));
        queryGroupCustomerConvertPremium.setProductAttrCode("group");
        List<CustomerConvertPremiumDto> customerConvertPremium1 = phoenixService.getCustomerConvertPremium(queryGroupCustomerConvertPremium);
        list.addAll(customerConvertPremium1);
        return list;
    }
}
