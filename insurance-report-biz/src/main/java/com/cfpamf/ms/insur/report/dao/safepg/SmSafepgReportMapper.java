package com.cfpamf.ms.insur.report.dao.safepg;

import com.cfpamf.ms.insur.report.pojo.dto.InsuranceOrderAllStatDTO;
import com.cfpamf.ms.insur.report.pojo.query.ReportQuery;
import com.cfpamf.ms.insur.report.pojo.vo.SmOrgCommissionReportVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SmSafepgReportMapper {
    List<SmOrgCommissionReportVO> listOrgCommission(ReportQuery query);

    InsuranceOrderAllStatDTO getInsuranceOrderAllStat();

    String getMaxAvailablePtForTables(@Param("tableNameList") List<String> tableNameList);
}
