package com.cfpamf.ms.insur.report.pojo.query;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 单个日期查询
 *
 * <AUTHOR>
 * @date 2021/3/30 10:09
 */
@Data
public class SingleDateQuery extends SortPage {

    /**
     * 单日查询
     */
    @ApiModelProperty("单日查询  'yyyy-mm-dd'")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate singleDate;
}
