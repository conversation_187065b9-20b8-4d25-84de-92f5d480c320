package com.cfpamf.ms.insur.report.pojo.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 报表查询 公共VO
 *
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ReportQuery extends DataPermissionQuery {
    @ApiModelProperty("开始日期 dateType=year 'yyyy',dateType= month 'yyyy-mm',dateType=day 'yyyy-mm-dd'")
    private String startDateStr;
    @ApiModelProperty("结束日期 dateType=year 'yyyy',dateType= month 'yyyy-mm',dateType=day 'yyyy-mm-dd'")
    private String endDateStr;
    @ApiModelProperty("日期类型 年：year，月 month 日 day")
    private String dateType;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间" ,hidden=true)
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value="结束时间",hidden=true)
    private LocalDate endDate;

    /**
     * 排序类型
     * 业绩报表排序类型：
     *    按单量insuredCnt， 按保费insuredAmt ，按个意险保费insuredAmtAccidentPerson，
     *    按团意险保费insuredAmtAccidentGroup，按医疗险保费insuredAmtMedical，按责任险保费insuredAmtLiability，
     *    按年金险保费insuredAmtAnnuity，按重疾险保费insuredAmtIllness，人均保费empAverageAmt
     *    编制人均保费planEmpAverageAmt
     * 续保率报表排序类型：
     *    按续保客户数renewalCnt
     *    按客户续保率renewalRate，按续保客户保费占比renewalAmountRate，按信贷客户续保率loanerRenewalRate
     *    按信贷相关客户续保率loancustRenewalRate，按非信贷客户续保率noLoanRenewalRate，按个意险续保率personRenewalRate
     *    按医疗险续保率medicalRenewalRate，按团意险续保率groupAccidentRenewalRate，按责任险续保率liabilityRenewalRate
     */
    @ApiModelProperty("排序类型 业绩报表排序类型：\n" +
            "     *    按单量insuredCnt， 按保费insuredAmt ，按个意险保费insuredAmtAccidentPerson，\n" +
            "     *    按团意险保费insuredAmtAccidentGroup，按医疗险保费insuredAmtMedical，按责任险保费insuredAmtLiability，\n" +
            "     *    按年金险保费insuredAmtAnnuity，按重疾险保费insuredAmtIllness，人均保费empAverageAmt\n" +
            "     *    编制人均保费planEmpAverageAmt\n" +
            "     * 续保率报表排序类型：\n" +
            "     *    按续保客户数renewalCnt\n" +
            "     *    按客户续保率renewalRate，按续保客户保费占比renewalAmountRate，按信贷客户续保率loanerRenewalRate\n" +
            "     *    按信贷相关客户续保率loancustRenewalRate，按非信贷客户续保率noLoanRenewalRate，按个意险续保率personRenewalRate\n" +
            "     *    按医疗险续保率medicalRenewalRate，按团意险续保率groupAccidentRenewalRate，按责任险续保率liabilityRenewalRate")
    private String sortType;

    /**
     * 排序方式（1升序 2降序（默认））
     */
    @ApiModelProperty("排序方式（1升序 2降序（默认））")
    private String sortMode;

}
