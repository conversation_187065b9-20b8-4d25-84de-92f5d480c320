package com.cfpamf.ms.insur.report.feign.resp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户兼职数据对象
 */
@Data
public class UserPartTimerVO {

    @ApiModelProperty(value = "序号")
    private Integer rowNo;

    @ApiModelProperty(value = "用户Id")
    private Integer userId;

    @ApiModelProperty(value = "员工Id")
    private Integer employeeId;

    @ApiModelProperty(value = "北森用户Id")
    private Integer hrUserId;

    @ApiModelProperty(value = "行政上级/直线经理Id")
    private String userAdminId;

    @ApiModelProperty(value = "行政上级/直线经理")
    private String userAdminName;

    @ApiModelProperty(value = "业务上级/虚线经理Id")
    private String userMasterId;

    @ApiModelProperty(value = "业务上级/虚线经理")
    private String userMasterName;

    @ApiModelProperty(value = "所属岗位postId")
    private Integer postId;

    @ApiModelProperty(value = "所属岗位(职务)")
    private String postName;

    @ApiModelProperty(value = "北森岗位Id")
    private Integer hrPostId;

    @ApiModelProperty("所属岗位(职务)状态：0:停用,1:启用)")
    private Integer postStatus;

    @ApiModelProperty("所属组织(Id)")
    private Integer orgId;

    @ApiModelProperty("所属组织(Code)")
    private String orgCode;

    @ApiModelProperty("所属组织(名称)")
    private String orgName;

    @ApiModelProperty("机构表的hrOrgId")
    private Integer hrOrgId;

    @ApiModelProperty("所属组织层级结构")
    private String hrOrgTreePath;

    @ApiModelProperty("所属组织兼职工号，如果没有就是主职工号")
    private String jobNumber;

    @ApiModelProperty(value = "用户主职工号")
    private String masterJobNumber;

    @ApiModelProperty(value = "HR任职记录code,任职唯一识别编码")
    private String jobCode;

    @ApiModelProperty("任职类型（0:正职、1:兼职）")
    private Integer serviceType;
}
