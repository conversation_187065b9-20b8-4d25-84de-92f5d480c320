package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@ApiModel(value = "保险管理端推广指标排名对象", description = "")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantManagerPromotionRankDTO {

    @ApiModelProperty(value = "层级编码", notes = "")
    String currentName;

    @ApiModelProperty(value = "层级编码", notes = "")
    String currentRank;

    @ApiModelProperty(value = "当月分享排名", notes = "")
    List<AssistantManagerPromotionRankDetail> rankDetailList;

    /**
     * 数据类型 share 分享, visit
     */
    @ApiModelProperty(value = "数据类型 share 分享, visit")
    String dataType;

    @ApiModelProperty("nation 全国, area 区域, district 片区, bch 分支")
    String levelType;
}
