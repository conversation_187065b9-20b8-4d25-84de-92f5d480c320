package com.cfpamf.ms.insur.report.feign.resp.vo;

import lombok.Data;

import java.util.List;

/**
 * 组织机构管理人员员工数等信息
 *
 * <AUTHOR>
 **/
@Data
public class FdOrgEmployeeVO {

    /**
     * 组织id
     */
    private String hrOrgId;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 父组织id
     */
    private String parentOrgId;

    /**
     * 父组织名称
     */
    private String parentOrgName;

    /**
     * 主任工号
     */
    private String directorJobNumber;

    /**
     * 主任名称
     */
    private String directorName;

    /**
     * 督导
     */
    private List<FdOrgUserVO> supervisorList;

    /**
     * 区域员工信息
     */
    private List<FdOrgUserVO> employeeList;

    /**
     * 员工数量
     */
    private Integer employeeNumber;
}
