package com.cfpamf.ms.insur.report.feign.resp.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 组织员工VO
 *
 * <AUTHOR>
 **/
@Data
public class FdOrgUserVO {

    /**
     * 组织Id
     */
    @JsonIgnore
    private String hrOrgId;

    /**
     * 组织名称
     */
    @JsonIgnore
    private String orgName;

    /**
     * 岗位code
     */
    private String postCode;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 员工工号
     */
    private String jobNumber;

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * HR任职记录code,任职唯一识别编码
     */
    private String jobCode;
}
