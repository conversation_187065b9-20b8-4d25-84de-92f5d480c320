package com.cfpamf.ms.insur.report.advice;

import com.cfpamf.ms.insur.report.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 全局response 处理器
 * 把参数统一用 CommonResult包装
 *
 * <AUTHOR>
 */
@ControllerAdvice(annotations = RestController.class)
@Slf4j
public class GlobalRespBodyHandler implements ResponseBodyAdvice {

    /**
     * 启用设置
     *
     * @param methodParameter
     * @param aClass
     * @return
     */
    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass) {
        return true;
    }

    /**
     * response返回前验证包装
     *
     * @param o
     * @param methodParameter
     * @param mediaType
     * @param aClass
     * @param serverHttpRequest
     * @param serverHttpResponse
     * @return
     */
    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        if (!mediaType.equals(MediaType.APPLICATION_JSON)) {
            return o;
        }
        if (o instanceof CommonResult) {
            return o;
        }
        return CommonResult.successResult(o);
    }
}
