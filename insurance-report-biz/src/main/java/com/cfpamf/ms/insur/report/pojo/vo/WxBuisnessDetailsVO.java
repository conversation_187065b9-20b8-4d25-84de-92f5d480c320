package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WxBuisnessDetailsVO implements Serializable {
    /**
     * 统计日期（始） 时间格式 yyyy-MM-dd
     */
    @ApiModelProperty("统计日期（始）*")
    private String startDate;

    @ApiModelProperty("统计日期（止）*")
    private String endDate;

    @ApiModelProperty("区域*")
    private String regionName;

    @ApiModelProperty("单量*")
    private Integer insuredCnt;

    @ApiModelProperty("保费*")
    private BigDecimal insuredAmt;

    @ApiModelProperty("到期客户数")
    private Integer endCnt;

    @ApiModelProperty("续保客户数")
    private Integer renewalCnt;

    @ApiModelProperty("续保率*")
    private BigDecimal renewalRate;
}
