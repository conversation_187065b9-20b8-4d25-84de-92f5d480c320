package com.cfpamf.ms.insur.report.dao.safepg;

import com.cfpamf.ms.insur.report.service.diagnosis.DiagnosisAndConclusionService;
import com.cfpamf.ms.insur.report.service.diagnosis.InsuranceAmountRateSummaryService;
import com.cfpamf.ms.insur.report.service.diagnosis.RetentionRateSummaryService;
import org.apache.ibatis.annotations.Mapper;


/**
 * 保险助手整体集市;(ads_insurance_cfpamf_marketing_progress_dfp)表数据库访问层
 *
 * <AUTHOR> wanghao
 * @date : 2024-2-28
 */
@Mapper
public interface AdsInsuranceCfpamfMarketingProgressDfpMapper {
    /**
     * 查询管理助手中全国的诊断数据
     *
     * @param pt
     * @return
     */
    DiagnosisAndConclusionService.DiagnosisAndConclusionVo queryDiagnosisData(String pt);

    /**
     * 查询管理助手小结-业绩目标是否达标
     *
     * @param pt 分区
     * @return
     */
    Boolean queryAssessConvertInsuranceAmtSummary(String pt);

    /**
     * 查询管理助手小结-异业保费信息
     *
     * @param pt 分区
     * @return
     */
    InsuranceAmountRateSummaryService.InsuranceAmountRateSummaryVo queryInsuranceAmountRateSummary(String pt);

    /**
     * 查询管理助手小结-客户留存率是否达标
     *
     * @param pt 分区
     * @return
     */
    RetentionRateSummaryService.RetentionRateSummaryVo queryRetentionRateSummary(String pt);
}
