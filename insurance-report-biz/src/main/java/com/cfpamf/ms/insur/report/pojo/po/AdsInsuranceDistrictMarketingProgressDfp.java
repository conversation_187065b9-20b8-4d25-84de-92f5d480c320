package com.cfpamf.ms.insur.report.pojo.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 保险助手片区集市;
 *
 * <AUTHOR> 王浩
 * @date : 2024-2-28
 */
@ApiModel(value = "保险营销片区粒度集市")
@Data
public class AdsInsuranceDistrictMarketingProgressDfp {

    @ApiModelProperty(value = "片区名称")
    private String districtName;

    @ApiModelProperty(value = "片区编码")
    private String districtCode;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("当月标准保费")
    private Double smAssessConvertInsuranceAmt;

    @ApiModelProperty("当年标准保费")
    private Double syAssessConvertInsuranceAmt;

    @ApiModelProperty("当月标准保费目标")
    private Double smAssessConvertInsuranceAmtTarget;

    @ApiModelProperty("当年标准保费目标")
    private Double syAssessConvertInsuranceAmtTarget;

    @ApiModelProperty("当月标准保费达成率")
    private Double smAssessConvertInsuranceAmtAchieveRate;

    @ApiModelProperty("当年标准保费达成率")
    private Double syAssessConvertInsuranceAmtAchieveRate;

    @ApiModelProperty("当月异业保费配比")
    private Double smOfflineLoanInsuranceRate;

    @ApiModelProperty("当年异业保费配比")
    private Double syOfflineLoanInsuranceRate;

    @ApiModelProperty("当月异业保费配比目标")
    private Double smOfflineLoanInsuranceRateTarget;

    @ApiModelProperty("当年异业保费配比目标")
    private Double syOfflineLoanInsuranceRateTarget;

    @ApiModelProperty("当月留存率")
    private Double smInsuranceRetentionRate;

    @ApiModelProperty("当年留存率")
    private Double syInsuranceRetentionRate;

    @ApiModelProperty("当月留存率目标")
    private Double smInsuranceRetentionRateTarget;

    @ApiModelProperty("当年留存率目标")
    private Double syInsuranceRetentionRateTarget;

    @ApiModelProperty("当月信贷规模保费")
    private Double sm_loan_insurance_amt;

    @ApiModelProperty("当年信贷规模保费")
    private Double sy_loan_insurance_amt;

    @ApiModelProperty("当月信贷线下放款金额")
    private Double sm_loan_offline_amt;

    @ApiModelProperty("当年信贷线下放款金额")
    private Double sy_loan_offline_amt;

    @ApiModelProperty("当月到期客户数")
    private Double sm_insurance_expire_cust_cnt;

    @ApiModelProperty("当年到期客户数")
    private Double sy_insurance_expire_cust_cnt;

    @ApiModelProperty("当月到期留存客户数")
    private Double sm_insurance_expire_retention_cust_cnt;

    @ApiModelProperty("当年到期留存客户数")
    private Double sy_insurance_expire_retention_cust_cnt;

    @ApiModelProperty("上月标准保费")
    private Double lmAssessConvertInsuranceAmt;

    @ApiModelProperty(value = "分区")
    private String pt;
}
