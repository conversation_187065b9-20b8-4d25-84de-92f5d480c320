package com.cfpamf.ms.insur.report.pojo.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class PromoFeeQuery extends ReportQuery {

    /**
     * 日期类型
     */
    @ApiModelProperty("日期类型 月：month")
    private String dateType;

    /**
     * 发放公司
     */
    @ApiModelProperty("发放公司")
    private String financeOrgCode;
    /**
     * 查询日期集合
     */
    private List<LocalDate> localDateList;
}
