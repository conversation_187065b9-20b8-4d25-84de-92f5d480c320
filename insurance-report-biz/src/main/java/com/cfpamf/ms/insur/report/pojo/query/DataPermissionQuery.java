package com.cfpamf.ms.insur.report.pojo.query;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 数据权限Query
 *
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DataPermissionQuery extends Pageable {



    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称" ,hidden = true)
    private String regionName;

    /**
     * 区域code
     */
    @ApiModelProperty("区域code")
    private String regionCode;

    /**
     * 片区名称（片区/直管）
     */
    @ApiModelProperty(value = "片区名称",hidden = true)
    private String zoneName;

    /**
     * 片区code（片区/直管）
     */
    @ApiModelProperty("片区code")
    private String zoneCode;

    /**
     * 分支名称
     */
    @ApiModelProperty(value = "分支名称",hidden = true)
    private String orgName;

    /**
     * 分支code
     */
    @ApiModelProperty("分支code")
    private String orgCode;

    /**
     * 分支路径
     */
    @ApiModelProperty(value = "分支路径")
    private String orgPath;
    /**
     * 分支id
     */
    @ApiModelProperty(value = "分支id")
    private Integer orgId;
    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    private String userId;

    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;


    /**
     * 是否创新业务对接人
     */
    @ApiModelProperty(value = "机构", hidden = true)
    private Boolean orgAdmin;

    @ApiModelProperty(value = "渠道", hidden = true)
    private String channel;

    /**
     * 是否创新业务对接人（机构保险事物专员）
     */
    @ApiModelProperty(value = "是否创新业务对接人", hidden = true)
    private Boolean picRole;

    @ApiModelProperty(value = "是否保险业务中心", hidden = true)
    public Boolean safeCenter;

    /**
     * 是否机构对接人 机构保险事物专员)
     *
     * @return
     */
    @JsonIgnore
    @ApiModelProperty(value = "是否创新业务对接人", hidden = true)
    public boolean isOrgSupport() {
        return Boolean.TRUE.equals(picRole);
    }

    /**
     * 用于有的报表只有机构编码字段，就需要根据片区、区域编码获取对应节点下所有的机构编码进行查询
     */
    @ApiModelProperty(value = "机构编码集合", hidden = true)
    private List<String> orgCodeList;

    @ApiModelProperty(value = "是否客户经理 0否，1是", hidden = true)
    private String isCustomerManager;

}
