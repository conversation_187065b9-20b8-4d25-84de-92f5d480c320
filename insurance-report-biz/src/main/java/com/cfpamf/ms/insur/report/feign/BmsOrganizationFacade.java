package com.cfpamf.ms.insur.report.feign;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.report.feign.constants.BmsConstantCore;
import com.cfpamf.ms.insur.report.feign.req.FdOrganizationQuery;
import com.cfpamf.ms.insur.report.feign.resp.dto.ElementTreeNodeDTO;
import com.cfpamf.ms.insur.report.feign.resp.vo.FdOrgEmployeeVO;
import com.cfpamf.ms.insur.report.feign.resp.vo.FdOrgVO;
import com.cfpamf.ms.insur.report.feign.resp.vo.OrganizationBaseVO;
import com.cfpamf.ms.insur.report.feign.resp.vo.OrganizationVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface BmsOrganizationFacade {



}
