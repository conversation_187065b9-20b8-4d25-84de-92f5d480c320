package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PersonalRenewalRateVO extends OrgRenewalRateVO implements Serializable {

    @ExportField(name = "员工姓名" , order = 4)
    @ApiModelProperty("员工姓名")
    private String userName;

    @ExportField(name = "员工工号" , order = 5)
    @ApiModelProperty("员工工号")
    private String userId;


}
