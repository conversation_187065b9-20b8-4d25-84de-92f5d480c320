package com.cfpamf.ms.insur.report.pojo.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;


/**
 * 保险助手
 *
 * <AUTHOR>
 * @since 2024/2/21 14:11
 */
@Data
@ApiModel("其他排行")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantRankVO {

    @ApiModelProperty("维度名称")
    String dimName;

    @ApiModelProperty("维度编码")
    String dimCode;

    @ApiModelProperty("排名")
    Integer rankNum;

    @ApiModelProperty("显示值 1")
    BigDecimal value1;

    @ApiModelProperty("显示值 2")
    BigDecimal value2;

    @ApiModelProperty("显示值3")
    BigDecimal value3;

    @ApiModelProperty("显示值4")
    BigDecimal value4;
}
