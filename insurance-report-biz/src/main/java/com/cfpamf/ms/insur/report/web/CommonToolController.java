package com.cfpamf.ms.insur.report.web;

import com.cfpamf.ms.insur.report.pojo.CommonResult;
import com.cfpamf.ms.insur.report.pojo.vo.MaxAvailablePtVO;
import com.cfpamf.ms.insur.report.service.wx.CommonToolService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Api(value = "CommonToolController", tags = "销售助手指标")
@RestController
@RequestMapping("/common")
public class CommonToolController {
    @Autowired
    private CommonToolService commonToolService;
    @PostMapping("getMaxAvailablePT")
    public CommonResult<String> getMaxAvailablePtForTables(@RequestBody MaxAvailablePtVO maxAvailablePtVO) {
        String pt = commonToolService.getMaxAvailablePtForTables(maxAvailablePtVO.getTableNameList());
        return CommonResult.successResult(pt);
    }
}
