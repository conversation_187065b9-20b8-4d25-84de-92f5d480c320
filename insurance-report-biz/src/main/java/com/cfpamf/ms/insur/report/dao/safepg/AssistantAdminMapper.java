package com.cfpamf.ms.insur.report.dao.safepg;

import com.cfpamf.ms.insur.report.pojo.query.AssistantAdminQuery;
import com.cfpamf.ms.insur.report.pojo.query.AssistantAdminRankQuery;
import com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantBchVO;
import com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantRankAmtVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/2/21 13:45
 */
@Mapper
public interface AssistantAdminMapper {
    /**
     * 获取分支数据
     *
     * @param pts 分区列表
     * @return
     */
    List<AssistantBchVO> listBchData(@Param("pts") List<String> pts, @Param("query") AssistantAdminQuery query);

    /**
     * 获取片区数据
     *
     * @param pts 分区列表
     * @return 包含行政区划数据的列表
     */
    List<AssistantBchVO> listDistrictData(@Param("pts") List<String> pts, @Param("query") AssistantAdminQuery query);

    /**
     * 获取区域数据
     *
     * @param pts 分区列表
     * @return 包含区域数据的对象
     */
    List<AssistantBchVO> listAreaData(@Param("pts") List<String> pts, @Param("query") AssistantAdminQuery query);

    /**
     * 获取所有数据
     *
     * @param pts 分区列表
     * @return 包含区域数据的对象
     */
    List<AssistantBchVO> listAllData(@Param("pts") List<String> pts, @Param("query") AssistantAdminQuery query);

    /**
     * 获取区域月度业绩排名
     *
     * @param assistantAdminQuery 查询对象
     * @return 包含区域月度业绩排名的数据
     */
    List<AssistantRankAmtVO> listAreaMonthAmtRank(@Param("pt") String pt, @Param("query") AssistantAdminQuery assistantAdminQuery);

    /**
     * 根据区域和年份查询助理排名金额数据
     *
     * @param pt                  区域
     * @param assistantAdminQuery 查询条件
     * @return 前台展示的助理排名金额数据
     */
    List<AssistantRankAmtVO> listAreaYearAmtRank(@Param("pt") String pt, @Param("query") AssistantAdminQuery assistantAdminQuery);


    /**
     * 获取指定表中指定列的排名金额列表
     *
     * @param pt        分页参数
     * @param tableName 表名
     * @param nameCol   列名
     * @param colSql    列值查询SQL
     * @param orderSql  排序SQL
     * @param query     查询参数
     * @return 排名金额列表
     */
    List<AssistantRankAmtVO> listAmtRank(@Param("pt") String pt, @Param("tableName") String tableName, @Param("nameCol") String nameCol, @Param("colSql") String colSql, @Param("orderSql") String orderSql, @Param("query") AssistantAdminQuery query);


    /**
     * 获取排行榜列表
     * @param pt 分页参数
     * @param tableName 表名
     * @param colSql 排序字段SQL
     * @param orderSql 排序方式SQL
     * @param query 查询参数
     * @return 排行榜列表
     */
    List<Map<String, Object>> listRank(@Param("pt") String pt,
                                       @Param("tableName") String tableName,
                                       @Param("colSql") String colSql,
                                       @Param("orderSql") String orderSql,
                                       @Param("query") AssistantAdminRankQuery query);

    /**
     * 获取分支数据
     *
     * @param pts 分区列表
     * @return
     */
    List<AssistantBchVO> listBchDataByBchCodeList(@Param("pts") List<String> pts, @Param("query") AssistantAdminQuery query);
}
