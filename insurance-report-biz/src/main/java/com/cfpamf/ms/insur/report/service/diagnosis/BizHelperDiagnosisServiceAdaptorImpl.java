package com.cfpamf.ms.insur.report.service.diagnosis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 保险业务助手业务诊断实现类的适配器类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/02/27
 */
@Service
@Primary
@Slf4j
public class BizHelperDiagnosisServiceAdaptorImpl implements BizHelperDiagnosisService, ApplicationContextAware {

    private Map<DiagnosisTypeEnum, AbstractBizHelperDiagnosisService> diagnosisServiceMap = new HashMap<>(16);

    /**
     * 进行业务诊断
     *
     * @return 诊断的结果，返回的格式是前端的html格式数据。
     * @throws IllegalArgumentException 当diagnosisTypeEnum为null或者无法找到真实的适配实现时抛出
     */
    @Override
    public String diagnosis(DiagnosisRequest request) {
        return this.adapt(request.getDiagnosisType()).diagnosis(request);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, AbstractBizHelperDiagnosisService> abstractBizHelperDiagnosisServiceMap = applicationContext.getBeansOfType(AbstractBizHelperDiagnosisService.class);
        abstractBizHelperDiagnosisServiceMap.forEach((k, v) -> {
            DiagnosisTypeEnum supportDiagnosisType = v.getSupportDiagnosisType();
            if (supportDiagnosisType != null) {
                if (diagnosisServiceMap.containsKey(supportDiagnosisType)) {
                    throw new IllegalStateException(k + "支持的诊断类型为" + supportDiagnosisType.name() + "，与已加载的实现有重复，请检查");
                }
                diagnosisServiceMap.put(supportDiagnosisType, v);
                log.info("{}支持的诊断类型为{},加载成功！", k, supportDiagnosisType);
                return;
            }
            log.warn("{}支持的诊断类型为null！", k);
        });
    }

    /**
     * 找到真实的适配实现
     *
     * @param diagnosisTypeEnum 诊断类型
     * @return 适配实现
     * @throws IllegalArgumentException 当diagnosisTypeEnum为null或者无法找到真实的适配实现时抛出
     */
    private BizHelperDiagnosisService adapt(DiagnosisTypeEnum diagnosisTypeEnum) {
        if (diagnosisTypeEnum == null) {
            throw new IllegalArgumentException("诊断类型为空，无法找到匹配的诊断方案实现！");
        }
        AbstractBizHelperDiagnosisService diagnosisService = diagnosisServiceMap.get(diagnosisTypeEnum);
        if (diagnosisService == null) {
            throw new IllegalArgumentException("诊断类型：" + diagnosisTypeEnum.name() + "无法找到匹配的诊断方案实现！");
        }
        return diagnosisService;
    }
}
