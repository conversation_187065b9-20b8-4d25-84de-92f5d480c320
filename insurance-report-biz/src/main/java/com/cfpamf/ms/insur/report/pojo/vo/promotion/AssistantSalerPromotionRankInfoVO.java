package com.cfpamf.ms.insur.report.pojo.vo.promotion;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@ApiModel(value = "保险销售端推广指标排名列表对象", description = "")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantSalerPromotionRankInfoVO {

    public AssistantSalerPromotionRankInfoVO(Integer rank){
        this.rank = rank;
    }

    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "员工姓名", notes = "")
    String empName="张三";

    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "当月分享/访客数", notes = "")
    String smCnt="1,000";

    @ApiModelProperty(value = "分享/访客排名环比", notes = "")
    String rankComparativeQuarterly="+6名";

    @ApiModelProperty(value = "0 不升不降, 1 上升, 2 下降", notes = "")
    Integer rankStatus;

    @ApiModelProperty(value = "分享/访客排名", notes = "")
    Integer rank;
}
