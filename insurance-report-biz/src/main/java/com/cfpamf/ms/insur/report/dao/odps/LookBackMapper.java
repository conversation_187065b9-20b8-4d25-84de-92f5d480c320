package com.cfpamf.ms.insur.report.dao.odps;

import com.cfpamf.ms.insur.report.pojo.dto.LookBackInstantDTO;
import com.cfpamf.ms.insur.report.pojo.vo.LookBackEventVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Create By zhengjing on 2020/8/21 10:10
 */
@Mapper
public interface LookBackMapper {

    /**
     * 获取某个订单的回溯信息
     *
     * @param orderId
     * @param start
     * @param end
     * @return
     */
    LookBackInstantDTO getInstByOrderId(@Param("orderId") String orderId,
                                        @Param("start") String start,
                                        @Param("end") String end);

    List<LookBackEventVO> events(@Param("start") String start,
                                 @Param("end") String end,
                                 @Param("endTime") Long endTime,
                                 @Param("userId") String userId,
                                 @Param("share") String share
                                 );
}
