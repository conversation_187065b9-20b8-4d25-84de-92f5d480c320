package com.cfpamf.ms.insur.report.pojo.po;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * 员工待办指标;
 * <AUTHOR> zxk
 * @date : 2024-2-29
 */
@ApiModel(value = "员工待办指标",description = "")
public class DwaSafesPhoenixTodoEmp implements Serializable,Cloneable{
    /** 员工名称 */
    @ApiModelProperty(name = "员工名称",notes = "")
    private String empName ;
    /** 员工工号 */
    @ApiModelProperty(name = "员工工号",notes = "")
    private String empCode ;
    /** 分支名称 */
    @ApiModelProperty(name = "分支名称",notes = "")
    private String bchName ;
    /** 分支编码 */
    @ApiModelProperty(name = "分支编码",notes = "")
    private String bchCode ;
    /** 片区名称 */
    @ApiModelProperty(name = "片区名称",notes = "")
    private String districtName ;
    /** 片区编码 */
    @ApiModelProperty(name = "片区编码",notes = "")
    private String districtCode ;
    /** 区域编码 */
    @ApiModelProperty(name = "区域编码",notes = "")
    private String areaCode ;
    /** 区域名称 */
    @ApiModelProperty(name = "区域名称",notes = "")
    private String areaName ;
    /** 年度断保代办数量 */
    @ApiModelProperty(name = "年度断保代办数量",notes = "")
    private Integer syInterruptionTodoCnt ;
    /** 年度续保代办数量 */
    @ApiModelProperty(name = "年度续保代办数量",notes = "")
    private Integer syRenewShortTodoCnt ;
    /** 年度A类代办数量 */
    @ApiModelProperty(name = "年度A类代办数量",notes = "")
    private Integer syLoanFirstTodoCnt ;
    /** 年度代办数量 */
    @ApiModelProperty(name = "年度代办数量",notes = "")
    private Integer syTodoCnt ;
    /** 年度断保待办跟进率 */
    @ApiModelProperty(name = "年度断保待办跟进率",notes = "")
    private Double syInterruptionTodoFollowRate ;
    /** 年度续保待办跟进率 */
    @ApiModelProperty(name = "年度续保待办跟进率",notes = "")
    private Double syRenewShortTodoFollowRate ;
    /** 年度断保待办转化率 */
    @ApiModelProperty(name = "年度断保待办转化率",notes = "")
    private Double syInterruptionTodoConversionRate ;
    /** 年度续保待办转化率 */
    @ApiModelProperty(name = "年度续保待办转化率",notes = "")
    private Double syRenewShortTodoConversionRate ;
    /** 年度断保待办转化保费 */
    @ApiModelProperty(name = "年度断保待办转化保费",notes = "")
    private Double syInterruptionTodoConversionAmt ;
    /** 年度喜报待办转化保费 */
    @ApiModelProperty(name = "年度喜报待办转化保费",notes = "")
    private Double syRenewShortTodoConversionAmt ;
    /**  */
    @ApiModelProperty(name = "",notes = "")
    private String pt ;

    //sm_interruption_todo_follow_cnt int 当月断保待办跟进数
    @ApiModelProperty(value ="当月断保待办跟进数")
    private Integer smInterruptionTodoFollowCnt;
    //sy_interruption_todo_follow_cnt int 当年断保待办跟进数
    @ApiModelProperty(value ="当年断保待办跟进数")
    private Integer syInterruptionTodoFollowCnt;
    //sy_interruption_tdo_follow_cust intn当年断保待办激活数
    @ApiModelProperty(value ="当年断保待办激活数")
    private Integer syInterruptionTdoFollowCust;
    //sm_interruption_tdo_follow_cust int 当月断保待办激活数
    @ApiModelProperty(value ="当月断保待办激活数")
    private Integer smInterruptionTdoFollowCust;
    //sy_tdo_short_follow_cnt int 当年续保待办跟进数
    @ApiModelProperty(value ="当年续保待办跟进数")
    private Integer syTdoShortFollowCnt;
    //sm_tdo_short_follow_cnt int 当月续保待办跟进数
    @ApiModelProperty(value ="当月续保待办跟进数")
    private Integer smTdoShortFollowCnt;
    //sm_tdo_short_follow_policy int 当月续保待办激活数
    @ApiModelProperty(value ="当月续保待办激活数")
    private Integer smTdoShortFollowPolicy;
    //sy_tdo_short_follow_policy int 当年续保待办激活数`
    @ApiModelProperty(value ="当年续保待办激活数")
    private Integer syTdoShortFollowPolicy;





    /** 员工名称 */
    public String getEmpName(){
        return this.empName;
    }
    /** 员工名称 */
    public void setEmpName(String empName){
        this.empName=empName;
    }
    /** 员工工号 */
    public String getEmpCode(){
        return this.empCode;
    }
    /** 员工工号 */
    public void setEmpCode(String empCode){
        this.empCode=empCode;
    }
    /** 分支名称 */
    public String getBchName(){
        return this.bchName;
    }
    /** 分支名称 */
    public void setBchName(String bchName){
        this.bchName=bchName;
    }
    /** 分支编码 */
    public String getBchCode(){
        return this.bchCode;
    }
    /** 分支编码 */
    public void setBchCode(String bchCode){
        this.bchCode=bchCode;
    }
    /** 片区名称 */
    public String getDistrictName(){
        return this.districtName;
    }
    /** 片区名称 */
    public void setDistrictName(String districtName){
        this.districtName=districtName;
    }
    /** 片区编码 */
    public String getDistrictCode(){
        return this.districtCode;
    }
    /** 片区编码 */
    public void setDistrictCode(String districtCode){
        this.districtCode=districtCode;
    }
    /** 区域编码 */
    public String getAreaCode(){
        return this.areaCode;
    }
    /** 区域编码 */
    public void setAreaCode(String areaCode){
        this.areaCode=areaCode;
    }
    /** 区域名称 */
    public String getAreaName(){
        return this.areaName;
    }
    /** 区域名称 */
    public void setAreaName(String areaName){
        this.areaName=areaName;
    }
    /** 年度断保代办数量 */
    public Integer getSyInterruptionTodoCnt(){
        return this.syInterruptionTodoCnt;
    }
    /** 年度断保代办数量 */
    public void setSyInterruptionTodoCnt(Integer syInterruptionTodoCnt){
        this.syInterruptionTodoCnt=syInterruptionTodoCnt;
    }
    /** 年度续保代办数量 */
    public Integer getSyRenewShortTodoCnt(){
        return this.syRenewShortTodoCnt;
    }
    /** 年度续保代办数量 */
    public void setSyRenewShortTodoCnt(Integer syRenewShortTodoCnt){
        this.syRenewShortTodoCnt=syRenewShortTodoCnt;
    }
    /** 年度A类代办数量 */
    public Integer getSyLoanFirstTodoCnt(){
        return this.syLoanFirstTodoCnt;
    }
    /** 年度A类代办数量 */
    public void setSyLoanFirstTodoCnt(Integer syLoanFirstTodoCnt){
        this.syLoanFirstTodoCnt=syLoanFirstTodoCnt;
    }
    /** 年度代办数量 */
    public Integer getSyTodoCnt(){
        return this.syTodoCnt;
    }
    /** 年度代办数量 */
    public void setSyTodoCnt(Integer syTodoCnt){
        this.syTodoCnt=syTodoCnt;
    }
    /** 年度断保待办跟进率 */
    public Double getSyInterruptionTodoFollowRate(){
        return this.syInterruptionTodoFollowRate;
    }
    /** 年度断保待办跟进率 */
    public void setSyInterruptionTodoFollowRate(Double syInterruptionTodoFollowRate){
        this.syInterruptionTodoFollowRate=syInterruptionTodoFollowRate;
    }
    /** 年度续保待办跟进率 */
    public Double getSyRenewShortTodoFollowRate(){
        return this.syRenewShortTodoFollowRate;
    }
    /** 年度续保待办跟进率 */
    public void setSyRenewShortTodoFollowRate(Double syRenewShortTodoFollowRate){
        this.syRenewShortTodoFollowRate=syRenewShortTodoFollowRate;
    }
    /** 年度断保待办转化率 */
    public Double getSyInterruptionTodoConversionRate(){
        return this.syInterruptionTodoConversionRate;
    }
    /** 年度断保待办转化率 */
    public void setSyInterruptionTodoConversionRate(Double syInterruptionTodoConversionRate){
        this.syInterruptionTodoConversionRate=syInterruptionTodoConversionRate;
    }
    /** 年度续保待办转化率 */
    public Double getSyRenewShortTodoConversionRate(){
        return this.syRenewShortTodoConversionRate;
    }
    /** 年度续保待办转化率 */
    public void setSyRenewShortTodoConversionRate(Double syRenewShortTodoConversionRate){
        this.syRenewShortTodoConversionRate=syRenewShortTodoConversionRate;
    }
    /** 年度断保待办转化保费 */
    public Double getSyInterruptionTodoConversionAmt(){
        return this.syInterruptionTodoConversionAmt;
    }
    /** 年度断保待办转化保费 */
    public void setSyInterruptionTodoConversionAmt(Double syInterruptionTodoConversionAmt){
        this.syInterruptionTodoConversionAmt=syInterruptionTodoConversionAmt;
    }
    /** 年度喜报待办转化保费 */
    public Double getSyRenewShortTodoConversionAmt(){
        return this.syRenewShortTodoConversionAmt;
    }
    /** 年度喜报待办转化保费 */
    public void setSyRenewShortTodoConversionAmt(Double syRenewShortTodoConversionAmt){
        this.syRenewShortTodoConversionAmt=syRenewShortTodoConversionAmt;
    }
    /**  */
    public String getPt(){
        return this.pt;
    }
    /**  */
    public void setPt(String pt){
        this.pt=pt;
    }

    public Integer getSmInterruptionTodoFollowCnt() {
        return smInterruptionTodoFollowCnt;
    }

    public void setSmInterruptionTodoFollowCnt(Integer smInterruptionTodoFollowCnt) {
        this.smInterruptionTodoFollowCnt = smInterruptionTodoFollowCnt;
    }

    public Integer getSyInterruptionTodoFollowCnt() {
        return syInterruptionTodoFollowCnt;
    }

    public void setSyInterruptionTodoFollowCnt(Integer syInterruptionTodoFollowCnt) {
        this.syInterruptionTodoFollowCnt = syInterruptionTodoFollowCnt;
    }

    public Integer getSyInterruptionTdoFollowCust() {
        return syInterruptionTdoFollowCust;
    }

    public void setSyInterruptionTdoFollowCust(Integer syInterruptionTdoFollowCust) {
        this.syInterruptionTdoFollowCust = syInterruptionTdoFollowCust;
    }

    public Integer getSmInterruptionTdoFollowCust() {
        return smInterruptionTdoFollowCust;
    }

    public void setSmInterruptionTdoFollowCust(Integer smInterruptionTdoFollowCust) {
        this.smInterruptionTdoFollowCust = smInterruptionTdoFollowCust;
    }

    public Integer getSyTdoShortFollowCnt() {
        return syTdoShortFollowCnt;
    }

    public void setSyTdoShortFollowCnt(Integer syTdoShortFollowCnt) {
        this.syTdoShortFollowCnt = syTdoShortFollowCnt;
    }

    public Integer getSmTdoShortFollowCnt() {
        return smTdoShortFollowCnt;
    }

    public void setSmTdoShortFollowCnt(Integer smTdoShortFollowCnt) {
        this.smTdoShortFollowCnt = smTdoShortFollowCnt;
    }

    public Integer getSmTdoShortFollowPolicy() {
        return smTdoShortFollowPolicy;
    }

    public void setSmTdoShortFollowPolicy(Integer smTdoShortFollowPolicy) {
        this.smTdoShortFollowPolicy = smTdoShortFollowPolicy;
    }

    public Integer getSyTdoShortFollowPolicy() {
        return syTdoShortFollowPolicy;
    }

    public void setSyTdoShortFollowPolicy(Integer syTdoShortFollowPolicy) {
        this.syTdoShortFollowPolicy = syTdoShortFollowPolicy;
    }
}
