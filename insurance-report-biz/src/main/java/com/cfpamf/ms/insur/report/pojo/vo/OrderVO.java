package com.cfpamf.ms.insur.report.pojo.vo;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * Create By zhengjing on 2020/9/15 17:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderVO {
    String insuredPersonName;


    String applicantPersonName;

    String fhOrderId;

    LocalDateTime createTime;

    String productName;
    String policyNo;

}
