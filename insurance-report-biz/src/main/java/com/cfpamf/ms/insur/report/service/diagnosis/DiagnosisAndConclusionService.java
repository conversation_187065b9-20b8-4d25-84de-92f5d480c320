package com.cfpamf.ms.insur.report.service.diagnosis;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.dao.safepg.*;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.enums.OrgLevelEnum;
import com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo;
import com.cfpamf.ms.insur.report.service.diagnosis.model.AbstractDiagnosisModel;
import com.cfpamf.ms.insur.report.service.diagnosis.model.DiagnosisAndConclusionModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 保险业务的诊断与建议
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/02/27
 */
@Service
public class DiagnosisAndConclusionService extends AbstractBizHelperDiagnosisService {

    @Resource
    private AdsInsuranceCfpamfMarketingProgressDfpMapper cfpamfMapper;

    @Resource
    private AdsInsuranceAreaMarketingProgressDfpMapper areaMapper;

    @Resource
    private AdsInsuranceDistrictMarketingProgressDfpMapper districtMapper;

    @Resource
    private AdsInsuranceBchMarketingProgressDfpMapper branchMapper;

    @Resource
    private AdsInsuranceEmpMarketingProgressDfpMapper personMapper;

    /**
     * 获取受支持的诊断类型
     *
     * @return 诊断类型的枚举类
     */
    @Override
    public DiagnosisTypeEnum getSupportDiagnosisType() {
        return DiagnosisTypeEnum.DIAGNOSIS_AND_CONCLUSION;
    }

    /**
     * 根据请求参数，创建诊断的数据模型
     *
     * @param request 请求参数
     * @return 诊断的数据模型
     */
    @Override
    protected AbstractDiagnosisModel createModel(DiagnosisRequest request) {
        DiagnosisAndConclusionModel model = new DiagnosisAndConclusionModel();
        String pt = request.getPt();
        OrgLevelEnum orgLevel = request.getOrgLevel();
        switch (orgLevel) {
            case COUNTRY: {
                DiagnosisAndConclusionVo diagnosisAndConclusionVo = cfpamfMapper.queryDiagnosisData(pt);
                if (diagnosisAndConclusionVo == null) {
                    return null;
                }
                boolean assessConvertInsuranceAmtTag = Optional.ofNullable(diagnosisAndConclusionVo.getSmAssessConvertInsuranceAmtAchieveRate()).orElse(0d) >= 1.0d;
                boolean offlineLoanInsuranceRateTag = true;
                boolean insuranceRetentionRateTag = true;
                if (!assessConvertInsuranceAmtTag) {
                    offlineLoanInsuranceRateTag = Optional.ofNullable(diagnosisAndConclusionVo.getSyOfflineLoanInsuranceRate()).orElse(0d) >= Optional.ofNullable(diagnosisAndConclusionVo.getSmOfflineLoanInsuranceRateTarget()).orElse(0d);
                    insuranceRetentionRateTag = Optional.ofNullable(diagnosisAndConclusionVo.getSyInsuranceRetentionRate()).orElse(0d) >= Optional.ofNullable(diagnosisAndConclusionVo.getSmInsuranceRetentionRateTarget()).orElse(0d);
                }
                List<DiagnosisRankInfoVo> rankInfoVos = areaMapper.queryRankInfo(pt, assessConvertInsuranceAmtTag, offlineLoanInsuranceRateTag, insuranceRetentionRateTag);
                constructModel(rankInfoVos, model, orgLevel, assessConvertInsuranceAmtTag, diagnosisAndConclusionVo, offlineLoanInsuranceRateTag, insuranceRetentionRateTag);
                break;
            }
            case AREA: {
                DiagnosisAndConclusionVo diagnosisAndConclusionVo = areaMapper.queryDiagnosisData(pt, request.getOrgCodes());
                if (diagnosisAndConclusionVo == null) {
                    return null;
                }
                boolean assessConvertInsuranceAmtTag = Optional.ofNullable(diagnosisAndConclusionVo.getSmAssessConvertInsuranceAmtAchieveRate()).orElse(0d) >= 1.0d;
                boolean offlineLoanInsuranceRateTag = true;
                boolean insuranceRetentionRateTag = true;
                if (!assessConvertInsuranceAmtTag) {
                    offlineLoanInsuranceRateTag = Optional.ofNullable(diagnosisAndConclusionVo.getSyOfflineLoanInsuranceRate()).orElse(0d) >= Optional.ofNullable(diagnosisAndConclusionVo.getSmOfflineLoanInsuranceRateTarget()).orElse(0d);
                    insuranceRetentionRateTag = Optional.ofNullable(diagnosisAndConclusionVo.getSyInsuranceRetentionRate()).orElse(0d) >= Optional.ofNullable(diagnosisAndConclusionVo.getSmInsuranceRetentionRateTarget()).orElse(0d);
                }
                List<DiagnosisRankInfoVo> rankInfoVos = null;
                if(request.isExistDistrict()){
                    //区域下有片区则获取片区数据
                    rankInfoVos = districtMapper.queryRankInfo(pt, assessConvertInsuranceAmtTag, offlineLoanInsuranceRateTag, insuranceRetentionRateTag, request.getOrgCodes(), orgLevel.name());
                }else{
                    //区域下无片区则获取分支数据
                    rankInfoVos = branchMapper.queryRankInfo(pt, assessConvertInsuranceAmtTag, offlineLoanInsuranceRateTag, insuranceRetentionRateTag, request.getOrgCodes(), orgLevel.name(),request.getBlackBchList());
                }
                //设置区域下是否有片区,后续freemaker模板中逻辑判断用
                model.setExistDistrict(request.isExistDistrict());
                constructModel(rankInfoVos, model, orgLevel, assessConvertInsuranceAmtTag, diagnosisAndConclusionVo, offlineLoanInsuranceRateTag, insuranceRetentionRateTag);
                break;
            }
            case DISTRICT: {
                DiagnosisAndConclusionVo diagnosisAndConclusionVo = districtMapper.queryDiagnosisData(pt, request.getOrgCodes());
                if (diagnosisAndConclusionVo == null) {
                    return null;
                }
                boolean assessConvertInsuranceAmtTag = Optional.ofNullable(diagnosisAndConclusionVo.getSmAssessConvertInsuranceAmtAchieveRate()).orElse(0d) >= 1.0d;
                boolean offlineLoanInsuranceRateTag = true;
                boolean insuranceRetentionRateTag = true;
                if (!assessConvertInsuranceAmtTag) {
                    offlineLoanInsuranceRateTag = Optional.ofNullable(diagnosisAndConclusionVo.getSyOfflineLoanInsuranceRate()).orElse(0d) >= Optional.ofNullable(diagnosisAndConclusionVo.getSmOfflineLoanInsuranceRateTarget()).orElse(0d);
                    insuranceRetentionRateTag = Optional.ofNullable(diagnosisAndConclusionVo.getSyInsuranceRetentionRate()).orElse(0d) >= Optional.ofNullable(diagnosisAndConclusionVo.getSmInsuranceRetentionRateTarget()).orElse(0d);
                }
                List<DiagnosisRankInfoVo> rankInfoVos = branchMapper.queryRankInfo(pt, assessConvertInsuranceAmtTag, offlineLoanInsuranceRateTag, insuranceRetentionRateTag, request.getOrgCodes(), orgLevel.name(),request.getBlackBchList());
                constructModel(rankInfoVos, model, orgLevel, assessConvertInsuranceAmtTag, diagnosisAndConclusionVo, offlineLoanInsuranceRateTag, insuranceRetentionRateTag);
                break;
            }
            case BRANCH: {
                DiagnosisAndConclusionVo diagnosisAndConclusionVo = branchMapper.queryDiagnosisData(pt, request.getOrgCodes());
                if (diagnosisAndConclusionVo == null) {
                    return null;
                }
                boolean assessConvertInsuranceAmtTag = Optional.ofNullable(diagnosisAndConclusionVo.getSmAssessConvertInsuranceAmtAchieveRate()).orElse(0d) >= 1.0d;
                boolean offlineLoanInsuranceRateTag = true;
                boolean insuranceRetentionRateTag = true;
                if (!assessConvertInsuranceAmtTag) {
                    offlineLoanInsuranceRateTag = Optional.ofNullable(diagnosisAndConclusionVo.getSyOfflineLoanInsuranceRate()).orElse(0d) >= Optional.ofNullable(diagnosisAndConclusionVo.getSmOfflineLoanInsuranceRateTarget()).orElse(0d);
                    insuranceRetentionRateTag = Optional.ofNullable(diagnosisAndConclusionVo.getSyInsuranceRetentionRate()).orElse(0d) >= Optional.ofNullable(diagnosisAndConclusionVo.getSmInsuranceRetentionRateTarget()).orElse(0d);
                }
                List<DiagnosisRankInfoVo> rankInfoVos = personMapper.queryRankInfo(pt, assessConvertInsuranceAmtTag, offlineLoanInsuranceRateTag, insuranceRetentionRateTag, request.getOrgCodes());
                constructModel(rankInfoVos, model, orgLevel, assessConvertInsuranceAmtTag, diagnosisAndConclusionVo, offlineLoanInsuranceRateTag, insuranceRetentionRateTag);
                break;
            }
            default:
                break;
        }
        return model;
    }

    /**
     * 根据查询到的统计数据，构造诊断的数据模型
     *
     * @param rankInfoVos                  排名信息
     * @param model                        模型信息
     * @param orgLevel                     组织机构级别
     * @param assessConvertInsuranceAmtTag 标准保费业绩达标标识
     * @param diagnosisAndConclusionVo     诊断数据
     * @param offlineLoanInsuranceRateTag  异业保费配比达标标识
     * @param insuranceRetentionRateTag    客户留存率达标标识
     */
    private static void constructModel(List<DiagnosisRankInfoVo> rankInfoVos, DiagnosisAndConclusionModel model, OrgLevelEnum orgLevel, boolean assessConvertInsuranceAmtTag, DiagnosisAndConclusionVo diagnosisAndConclusionVo, boolean offlineLoanInsuranceRateTag, boolean insuranceRetentionRateTag) {
        List<String> classicInsuranceAmountRankNames = new ArrayList<>();
        List<String> insuranceAmountRateRankNames = new ArrayList<>();
        List<String> insuranceRetentionRateRankNames = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rankInfoVos)) {
            for (DiagnosisRankInfoVo rankInfoVo : rankInfoVos) {
                if (StringUtils.isNotBlank(rankInfoVo.getSmAssessConvertInsuranceAmtAchieveRateRankName())) {
                    classicInsuranceAmountRankNames.add(rankInfoVo.getSmAssessConvertInsuranceAmtAchieveRateRankName());
                }
                if (StringUtils.isNotBlank(rankInfoVo.getSyOfflineLoanInsuranceRateRankName())) {
                    insuranceAmountRateRankNames.add(rankInfoVo.getSyOfflineLoanInsuranceRateRankName());
                }
                if (StringUtils.isNotBlank(rankInfoVo.getSyInsuranceRetentionRateRankName())) {
                    insuranceRetentionRateRankNames.add(rankInfoVo.getSyInsuranceRetentionRateRankName());
                }
            }
        }
        DiagnosisAndConclusionModel.Conclusion conclusion = new DiagnosisAndConclusionModel.Conclusion();
        model.setConclusion(conclusion);
        conclusion.setType(orgLevel.getLevel());
        conclusion.setFinished(assessConvertInsuranceAmtTag);
        conclusion.setClassicInsuranceAmountYearRate(DECIMAL_FORMAT.format(BigDecimal.valueOf(Optional.ofNullable(diagnosisAndConclusionVo.getSyAssessConvertInsuranceAmtAchieveRate()).orElse(0d)).multiply(BigDecimal.valueOf(100))));
        conclusion.setClassicInsuranceAmountMonthRate(DECIMAL_FORMAT.format(BigDecimal.valueOf(Optional.ofNullable(diagnosisAndConclusionVo.getSmAssessConvertInsuranceAmtAchieveRate()).orElse(0d)).multiply(BigDecimal.valueOf(100))));
        if (diagnosisAndConclusionVo.getSmAssessConvertInsuranceAmtMonthOnMonthRate() != null) {
            conclusion.setClassicInsuranceAmountMonthOnMonthHasValue(true);
            conclusion.setClassicInsuranceAmountMonthOnMonthTrend(diagnosisAndConclusionVo.getSmAssessConvertInsuranceAmtMonthOnMonthRate() >= 0);
            conclusion.setClassicInsuranceAmountMonthOnMonth(DECIMAL_FORMAT.format(BigDecimal.valueOf(diagnosisAndConclusionVo.getSmAssessConvertInsuranceAmtMonthOnMonthRate()).abs().multiply(BigDecimal.valueOf(100))));
        }
        conclusion.setClassicInsuranceAmountYearCountryRank(diagnosisAndConclusionVo.getSyAssessConvertInsuranceAmtAchieveRateCountryRank());
        conclusion.setClassicInsuranceAmountYearAreaRank(diagnosisAndConclusionVo.getSyAssessConvertInsuranceAmtAchieveRateAreaRank());
        conclusion.setRankNames(classicInsuranceAmountRankNames);
        List<DiagnosisAndConclusionModel.MajorItem> majorItems = new ArrayList<>(2);
        model.setMajorItems(majorItems);
        if (!offlineLoanInsuranceRateTag) {
            DiagnosisAndConclusionModel.MajorItem majorItem = new DiagnosisAndConclusionModel.MajorItem();
            majorItems.add(majorItem);
            majorItem.setType("INSURANCE_AMOUNT_RATE");
            majorItem.setInsuranceAmountRate(DECIMAL_FORMAT.format(BigDecimal.valueOf(Optional.ofNullable(diagnosisAndConclusionVo.getSyOfflineLoanInsuranceRate()).orElse(0.0d)).setScale(4, RoundingMode.HALF_UP)));
            majorItem.setInsuranceAmountRateTarget(DECIMAL_FORMAT.format(BigDecimal.valueOf(Optional.ofNullable(diagnosisAndConclusionVo.getSmOfflineLoanInsuranceRateTarget()).orElse(0d)).setScale(4, RoundingMode.HALF_UP)));
            majorItem.setRankNames(insuranceAmountRateRankNames);
        }
        if (!insuranceRetentionRateTag) {
            DiagnosisAndConclusionModel.MajorItem majorItem = new DiagnosisAndConclusionModel.MajorItem();
            majorItems.add(majorItem);
            majorItem.setType("RETENTION_RATE");
            majorItem.setRetentionRate(DECIMAL_FORMAT.format(BigDecimal.valueOf(Optional.ofNullable(diagnosisAndConclusionVo.getSyInsuranceRetentionRate()).orElse(0d)).multiply(BigDecimal.valueOf(100))));
            majorItem.setRetentionRateTarget(DECIMAL_FORMAT.format(BigDecimal.valueOf(Optional.ofNullable(diagnosisAndConclusionVo.getSmInsuranceRetentionRateTarget()).orElse(0d)).multiply(BigDecimal.valueOf(100))));
            majorItem.setRankNames(insuranceRetentionRateRankNames);
        }
    }

    /**
     * 获取诊断模板文件的地址
     *
     * @return 模板文件的地址
     */
    @Override
    protected String getTemplateFilePath() {
        return "biz_diagnosis_conclusion.ftl";
    }

    @Data
    public static class DiagnosisAndConclusionVo {

        /**
         * 当年标准保费达成率
         */
        private Double syAssessConvertInsuranceAmtAchieveRate;
        /**
         * 当月标准保费达成率
         */
        private Double smAssessConvertInsuranceAmtAchieveRate;
        /**
         * 当月标准保费月环比
         */
        private Double smAssessConvertInsuranceAmtMonthOnMonthRate;
        /**
         * 当年异业保费配比
         */
        private Double syOfflineLoanInsuranceRate;
        /**
         * 当月年累计异业保费配比目标值
         */
        private Double smOfflineLoanInsuranceRateTarget;
        /**
         * 当年客户留存率
         */
        private Double syInsuranceRetentionRate;
        /**
         * 当月年累计客户留存率目标值
         */
        private Double smInsuranceRetentionRateTarget;

        /**
         * 当年标准保费达成率全国排名
         */
        private Integer syAssessConvertInsuranceAmtAchieveRateCountryRank;

        /**
         * 当年标准保费达成率区域排名
         */
        private Integer syAssessConvertInsuranceAmtAchieveRateAreaRank;

    }

}
