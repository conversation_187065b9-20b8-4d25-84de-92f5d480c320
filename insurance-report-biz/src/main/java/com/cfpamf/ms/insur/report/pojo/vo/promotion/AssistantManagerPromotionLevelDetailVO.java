package com.cfpamf.ms.insur.report.pojo.vo.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AssistantManagerPromotionLevelDetailVO {
    public AssistantManagerPromotionLevelDetailVO(Integer rank){
        this.rank = rank;
    }

    @ApiModelProperty(value = "层级名称", notes = "")
    String name="名称";

    @ApiModelProperty(value = "当月分享数", notes = "")
    String smShareCnt="3000";

    @ApiModelProperty(value = "当月分访客数", notes = "")
    String smVisitCnt="3000";


    @ApiModelProperty(value = "当日分享", notes = "")
    String sdShareCnt="300";

    @ApiModelProperty(value = "当月访客数", notes = "")
    String sdVisitCnt="300";


    @ApiModelProperty(value = "排名", notes = "")
    Integer rank;
}
