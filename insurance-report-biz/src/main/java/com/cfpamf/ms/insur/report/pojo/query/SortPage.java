package com.cfpamf.ms.insur.report.pojo.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 排序
 *
 * <AUTHOR>
 * @date 2021/3/30 10:08
 */
@Data
public class SortPage extends Pageable {

    /**
     * 排序类型
     * 业绩报表排序类型：
     * 按单量insuredCnt， 按保费insuredAmt ，按个意险保费insuredAmtAccidentPerson，
     * 按团意险保费insuredAmtAccidentGroup，按医疗险保费insuredAmtMedical，按责任险保费insuredAmtLiability，
     * 按年金险保费insuredAmtAnnuity，按重疾险保费insuredAmtIllness，人均保费empAverageAmt
     * 编制人均保费planEmpAverageAmt
     * 续保率报表排序类型：
     * 按续保客户数renewalCnt
     * 按客户续保率renewalRate，按续保客户保费占比renewalAmountRate，按信贷客户续保率loanerRenewalRate
     * 按信贷相关客户续保率loancustRenewalRate，按非信贷客户续保率noLoanRenewalRate，按个意险续保率personRenewalRate
     * 按医疗险续保率medicalRenewalRate，按团意险续保率groupAccidentRenewalRate，按责任险续保率liabilityRenewalRate
     */
    @ApiModelProperty("排序类型" +
            "     *    按日单量dayOrderQuantity， 按日保费dayPremium" +
            "     *    按月单量monthOrderQuantity， 按月保费monthPremium" +
            "     *    按年单量yearOrderQuantity， 按年保费yearPremium"
    )
    private String sortType;

    /**
     * 排序方式（1升序 2降序（默认））
     */
    @ApiModelProperty("排序方式（1升序 2降序（默认））")
    private String sortMode;
}
