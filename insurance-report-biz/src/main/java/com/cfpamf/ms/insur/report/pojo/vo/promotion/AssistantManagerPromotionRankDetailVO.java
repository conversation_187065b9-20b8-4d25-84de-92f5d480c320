package com.cfpamf.ms.insur.report.pojo.vo.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AssistantManagerPromotionRankDetailVO {


    public AssistantManagerPromotionRankDetailVO(Integer rank){
        this.rank = rank;
    }
    @ApiModelProperty(value = "层级名称", notes = "")
    String name="名称";

    @ApiModelProperty(value = "当月分享数", notes = "")
    String smCnt="3,000";

    @ApiModelProperty(value = "当月人均分享数", notes = "")
    String smAvg="50";

    @ApiModelProperty(value = "排名", notes = "")
    Integer rank;
}
