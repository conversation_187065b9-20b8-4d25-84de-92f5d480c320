package com.cfpamf.ms.insur.report.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 需要脱敏的方法
 */
@Documented
@Inherited
@Retention(RUNTIME)
@Target({METHOD})
public @interface MaskMethod {
}
