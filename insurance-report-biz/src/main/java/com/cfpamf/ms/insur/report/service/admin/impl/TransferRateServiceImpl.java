package com.cfpamf.ms.insur.report.service.admin.impl;

import com.cfpamf.ms.insur.report.dao.dw.SmDcReportMapper;
import com.cfpamf.ms.insur.report.pojo.query.WxReportQuery;
import com.cfpamf.ms.insur.report.pojo.vo.personnel.PersonnelTransferRateVo;
import com.cfpamf.ms.insur.report.service.admin.BaseService;
import com.cfpamf.ms.insur.report.service.admin.TransferRateService;
import com.cfpamf.ms.insur.report.util.CommonUtil;
import com.cfpamf.ms.insur.report.util.LocalDateUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * 转换率业务接口实现
 *
 * <AUTHOR>
 * @date 2021/4/12 20:47
 */
@Service
public class TransferRateServiceImpl extends BaseService implements TransferRateService {

    private SmDcReportMapper smDcReportMapper;

    public TransferRateServiceImpl(SmDcReportMapper smDcReportMapper) {
        this.smDcReportMapper = smDcReportMapper;
    }


    @Override
    public PageInfo<PersonnelTransferRateVo> searchPersonnelTransferRate(WxReportQuery wxReportQuery) {
        String singleDateString = wxReportQuery.getSingleDateString();
        String dateType = wxReportQuery.getDateType();
        if (StringUtils.isBlank(singleDateString) || StringUtils.isBlank(dateType)) {
            //设置时间查询缺省值
            wxReportQuery.setSingleDate(LocalDate.now());
        } else {
            //设置查询时间
            singleDateString = CommonUtil.dateStrChange(dateType, singleDateString);
            wxReportQuery.setSingleDate(LocalDate.parse(singleDateString));

        }
        LocalDate singleDate = wxReportQuery.getSingleDate();
        //如果查询时间在现在时间之后  则返回空
        if (singleDate.isAfter(LocalDate.now())) {
            return new PageInfo<>(Lists.newArrayList());
        }
        //设置月份查询
        wxReportQuery.setStartDate(LocalDateUtil.getMonthStartDate(singleDate));
        wxReportQuery.setEndDate(LocalDateUtil.getMonthEndDate(singleDate));
        //分页处理
        handlePage(wxReportQuery);
        return new PageInfo<>(smDcReportMapper.searchPersonnelTransferRate(wxReportQuery));
    }
}
