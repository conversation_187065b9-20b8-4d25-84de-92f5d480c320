package com.cfpamf.ms.insur.report.pojo.vo;


import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 保险业务明细报表 共用VO
 *
 */
@Data
public class SmBusinessDetailsVO   implements Serializable {
    private static final long serialVersionUID = -2093834479655237670L;

    /**
     * 统计日期（始） 时间格式 yyyy-MM-dd
     */
    @ExportField(name = "统计日期（始）",order = 0)
    @ApiModelProperty("统计日期（始）*")
    private String startDate;

    @ExportField(name = "统计日期（止）",order = 1)
    @ApiModelProperty("统计日期（止）*")
    private String endDate;

    @ExportField(name = "区域",order = 2)
    @ApiModelProperty("区域*")
    private String regionName;

    @ExportField(name = "单量",order = 8,type = "integer")
    @ApiModelProperty("单量*")
    private Integer insuredCnt;

    @ExportField(name = "保费",order = 9,type = "money")
    @ApiModelProperty("保费*")
    private BigDecimal insuredAmt;

    @ExportField(name = "个意险保费",order = 12,type = "money")
    @ApiModelProperty("个意险保费*")
    private BigDecimal insuredAmtAccidentPerson;

    @ExportField(name = "团意险保费",order = 13,type = "money")
    @ApiModelProperty("团意险保费")
    private BigDecimal insuredAmtAccidentGroup;

    @ExportField(name = "医疗险保费",order = 14,type = "money")
    @ApiModelProperty("医疗险保费*")
    private BigDecimal insuredAmtMedical;

    @ExportField(name = "责任险保费",order = 15,type = "money")
    @ApiModelProperty("责任险保费*" )
    private BigDecimal insuredAmtLiability;

    @ExportField(name = "年金险保费",order = 16,type = "money")
    @ApiModelProperty("年金险保费")
    private BigDecimal insuredAmtAnnuity;

    @ExportField(name = "重疾险保费",order = 17,type = "money")
    @ApiModelProperty("重疾险保费*")
    private BigDecimal insuredAmtIllness;

    @ExportField(name = "＜1年期保费",order = 18,type = "money")
    @ApiModelProperty("＜1年期保费*")
    private BigDecimal insuredAmt1y;


}
