package com.cfpamf.ms.insur.report.aspect;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

@Aspect
@Component
@Slf4j
public class AssistantSalerAspect {

    @Pointcut("execution(* com.cfpamf.ms.insur.report.web.AssistantSalerController.*(..))")
    public void allMethods() {
        // 这里定义了一个切点，表示com.cfpamf.ms.insur.report.web.AssistantAdminController下的所有方法
    }

    @Before("allMethods()")
    public void before(JoinPoint joinPoint) {
       Object[] args =  joinPoint.getArgs();
       if (args!= null || args.length > 0){
           if (args[0] != null){
               String pt = (String) args[0];
               LocalDate ptDate = LocalDate.parse(pt, BaseConstants.FMT_YYYYMMDD);
               if (ptDate.isBefore(LocalDate.now().minusDays(7))){
                   throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"参数pt不能早于当前7天");
               }
           }
       }
    }

}
