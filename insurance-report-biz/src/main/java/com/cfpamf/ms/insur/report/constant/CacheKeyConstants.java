package com.cfpamf.ms.insur.report.constant;

/**
 * 缓存key
 */
public interface CacheKeyConstants {

    /**
     * 微信首页轮播图
     */
    String WX_LBT_IMAGE = "wx_lbt_image";

    /**
     * 微信首页推送
     */
    String WX_PUSH_SETTING = "wx_push_setting";

    /**
     * 产品详情
     */
    String PRODUCT_DETAIL = "product_detail";

    /**
     * 产品列表
     */
    String PRODUCT_LIST = "product_list";

    /**
     * 产品分类
     */
    String PRODUCT_CATEGORY = "product_category";

    /**
     * 产品计划
     */
    String PRODUCT_PLAN = "product_plan";

    /**
     * 产品计划价格因子
     */
    String PRODUCT_PLAN_FACTOR = "product_plan_factor";

    /**
     * 产品计划价格
     */
    String PRODUCT_PLAN_PRICE = "product_plan_price";

    /**
     * 产品表单
     */
    String PRODUCT_FORM_FIELD = "product_form_field";

    /**
     * 产品表单选项
     */
    String PRODUCT_FORM_OPTIONAL = "product_form_field_optional";

    /**
     * 产品表单职业
     */
    String PRODUCT_FORM_OCUP = "product_form_occupation";

    /**
     * 保险公司
     */
    String COMPANY_LIST = "company_list";

    /**
     * 保险公司区域
     */
    String COMPANY_FORM_REGION = "company_form_region";

    /**
     * CAPP保险入口显示
     */
    String CAPP_ENTRY_DISPLAY = "capp_entry_display";

    String OTHER_PREFIX = "insurance:";

    String USER = OTHER_PREFIX + "user";
    /**
     * 用户是否机构管理员
     */
    String USER_IS_ORG_ADMIN = OTHER_PREFIX + "user:isOrg";

    /**
     * 一个小时就过期的key
     */
    String HOUR_PREFIX = "ins:hour:";

    /**
     * 获取公用码值的code
     */
    String COMMON_CODE = HOUR_PREFIX + ":dict:commonCode";

    String APPLICANT_CODE = "order:applicant:";
}
