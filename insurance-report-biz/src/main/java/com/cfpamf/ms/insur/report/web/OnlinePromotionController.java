package com.cfpamf.ms.insur.report.web;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.report.feign.resp.vo.UserDetailVO;
import com.cfpamf.ms.insur.report.pojo.dto.*;
import com.cfpamf.ms.insur.report.pojo.query.AssistantManagerPromotionLevelQuery;
import com.cfpamf.ms.insur.report.pojo.query.AssistantManagerPromotionQuery;
import com.cfpamf.ms.insur.report.pojo.query.AssistantSalerPromotionQuery;
import com.cfpamf.ms.insur.report.pojo.vo.promotion.*;
import com.cfpamf.ms.insur.report.service.BmsService;
import com.cfpamf.ms.insur.report.service.DataAuthService;
import com.cfpamf.ms.insur.report.service.promotion.OnlinePromotionService;
import com.cfpamf.ms.insur.report.util.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(value = "OnlinePromotionController", tags = "在线推广-管理助手指标")
@RestController
@RequestMapping("/onlinePromotion")
public class OnlinePromotionController extends AbstractPromotionBase{

    @Autowired
    private OnlinePromotionService onlinePromotionService;

    @Autowired
    private BmsService bmsService;

    @Autowired
    private DataAuthService dataAuthService;

    @ApiOperation("首页查询员工在线推广基础指标")
    @GetMapping("/home/<USER>/summary")
    public AssistantSalerPromotionSummaryVO homeEmpSummary() {
        UserDetailVO userDetail= bmsService.getContextUserDetail();
        log.warn("userDetail========== {}",JSON.toJSONString(userDetail));
        String empId = userDetail.getJobNumber();
        String pt = getPtDay();
        log.warn("homeEmpSummary empId= {}; pt= {}",empId,pt);
        return onlinePromotionService.homeEmpSummary(pt,empId);
    }

    @ApiOperation("查询员工在线推广基础指标")
    @GetMapping("/index/saler/summary")
    public AssistantSalerPromotionSummaryVO indexEmpSummary() {
        UserDetailVO userDetail= bmsService.getContextUserDetail();
        log.warn("userDetail========== {}",JSON.toJSONString(userDetail));
        String empId = userDetail.getJobNumber();
        String pt = getPtDay();
        log.warn("homeEmpSummary empId= {}; pt= {}",empId,pt);
        return onlinePromotionService.indexEmpSummary(pt,empId);
    }

    @ApiOperation("查询员工在线推广指标排名")
    @GetMapping("/saler/rankList")
    public AssistantSalerPromotionRankVO empRankList(AssistantSalerPromotionQuery assistantSalerPromotionSummaryQuery) {
        UserDetailVO userDetail= bmsService.getContextUserDetail();
        log.warn("userDetail========== {}",JSON.toJSONString(userDetail));
        String empId = userDetail.getJobNumber();
        String pt = getPtDay();
        String lastMonthPt = DateUtils.getLastMonthDay(pt);
        log.info("empRankList pt= {}, lastMonthPt= {}",pt,lastMonthPt);
        assistantSalerPromotionSummaryQuery.setEmpId(empId);
        assistantSalerPromotionSummaryQuery.setPt(pt);
        assistantSalerPromotionSummaryQuery.setLastMonthPt(lastMonthPt);
        return onlinePromotionService.empRankList(assistantSalerPromotionSummaryQuery);
    }

    @ApiOperation("首页查询员工在线推广基础指标")
    @GetMapping("/manager/summary")
    public AssistantManagerPromotionSummaryVO managerSummary(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery) {
        UserDetailVO userDetail= bmsService.getContextUserDetail();
        log.warn("userDetail========== {}",JSON.toJSONString(userDetail));
        String empId = userDetail.getJobNumber();
        String pt = getPtDay();
        assistantManagerPromotionSummaryQuery.setPt(pt);
        return onlinePromotionService.managerSummary(empId,assistantManagerPromotionSummaryQuery);
    }

    @ApiOperation("管理端查询地区在线推广指标排名")
    @GetMapping("/manager/rankList")
    public AssistantManagerPromotionRankVO managerRankList(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery) {
        UserDetailVO userDetail= bmsService.getContextUserDetail();
        log.warn("userDetail========== {}",JSON.toJSONString(userDetail));
        String empId = userDetail.getJobNumber();
        String pt = getPtDay();
        assistantManagerPromotionSummaryQuery.setPt(pt);
        return onlinePromotionService.managerRankList(empId,assistantManagerPromotionSummaryQuery);
    }

    @ApiOperation("管理端查询地区下一级明细列表")
    @GetMapping("/manager/nextLevelDetailList")
    public AssistantManagerPromotionLevelVO nextLevelDetailList(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery) {
        UserDetailVO userDetail= bmsService.getContextUserDetail();
        log.warn("userDetail========== {}",JSON.toJSONString(userDetail));
        String pt = getPtDay();
        assistantManagerPromotionSummaryQuery.setPt(pt);
        return onlinePromotionService.nextLevelDetailList(assistantManagerPromotionSummaryQuery);
    }
}
