package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
public class DailyRetrospectiveBasicDTO extends AssistantSalerBasicDTO{
    /** 当日标准保费 */
    @ApiModelProperty(value = "当日标准保费",notes = "")
    private Double cdAssessConvertInsuranceAmt ;
    @ApiModelProperty(value = "当周标准保费",notes = "")
    private Double swAssessConvertInsuranceAmt ;
    /** 当月信贷放款金额 */
    @ApiModelProperty(value = "当月信贷放款金额",notes = "")
    private Double smOfflineLoanAmt ;
    /** 上月信贷放款金额 */
    @ApiModelProperty(value = "上月信贷放款金额",notes = "")
    private Double lmOfflineLoanAmt ;
    /** 当月信贷相关规模保费 */
    @ApiModelProperty(value = "当月信贷相关规模保费",notes = "")
    private Double smLoanInsuranceAmt ;
    /** 上月信贷相关规模保费 */
    @ApiModelProperty(value = "上月信贷相关规模保费",notes = "")
    private Double lmLoanInsuranceAmt ;
    /** 当日信贷放款金额 */
    @ApiModelProperty(value = "当日信贷放款金额",notes = "")
    private Double cdOfflineLoanAmt ;
    /** 当日信贷相关规模保费 */
    @ApiModelProperty(value = "当日信贷相关规模保费",notes = "")
    private Double cdLoanInsuranceAmt ;
    /** 当日信贷相关标准保费 */
    @ApiModelProperty(value = "当日信贷相关标准保费",notes = "")
    private Double cdLoanAssessConvertInsuranceAmt ;
    /** 当月信贷相关标准保费 */
    @ApiModelProperty(value = "当月信贷相关标准保费",notes = "")
    private Double smLoanAssessConvertInsuranceAmt ;
    /** 上月信贷相关标准保费 */
    @ApiModelProperty(value = "上月信贷相关标准保费",notes = "")
    private Double lmLoanAssessConvertInsuranceAmt ;
    /** 当日非贷标准保费 */
    @ApiModelProperty(value = "当日非贷标准保费",notes = "")
    private Double cdUnloanAssessConvertInsuranceAmt ;
    /** 当月非贷标准保费 */
    @ApiModelProperty(value = "当月非贷标准保费",notes = "")
    private Double smUnloanAssessConvertInsuranceAmt ;
    /** 上月非贷标准保费 */
    @ApiModelProperty(value = "上月非贷标准保费",notes = "")
    private Double lmUnloanAssessConvertInsuranceAmt ;

    /** 信贷相关标准保费月环比 */
    @ApiModelProperty(value = "信贷相关标准保费月环比",notes = "")
    private Double loanAssessConvertInsuranceAmtMom;
    //yd_loan_assess_convert_insurance_amt
    @ApiModelProperty(value = "昨日信贷相关标准保费",notes = "")
    private Double ydLoanAssessConvertInsuranceAmt ;
    //loan_assess_convert_insurance_amt_dod
    @ApiModelProperty(value = "信贷相关标准保费日对比",notes = "")
    private Double loanAssessConvertInsuranceAmtDod ;
    //unloan_assess_convert_insurance_amt_mom
    @ApiModelProperty(value = "非贷相关标准保费月环比",notes = "")
    private Double unloanAssessConvertInsuranceAmtMom ;
    //yd_unloan_assess_convert_insurance_amt
    @ApiModelProperty(value = "昨日非贷相关标准保费",notes = "")
    private Double ydUnloanAssessConvertInsuranceAmt ;
    //unloan_assess_convert_insurance_amt_dod
    @ApiModelProperty(value = "非贷相关标准保费日对比",notes = "")
    private Double unloanAssessConvertInsuranceAmtDod ;
    //cd_offline_loan_insurance_rate
    @ApiModelProperty(value = "当日信贷相关保费占比",notes = "")
    private Double cdOfflineLoanInsuranceRate ;

    /** 待办跟进相关指标 */
    /** 当日断保跟进数量 */
    @ApiModelProperty(value = "当日断保跟进数量",notes = "")
    private Integer cdInterruptionTodoFollowCnt ;
    /** 当日续保跟进数量 */
    @ApiModelProperty(value = "当日续保跟进数量",notes = "")
    private Integer cdTodoShortFollowCnt ;
    /** 当日续期跟进数量 */
    @ApiModelProperty(value = "当日续期跟进数量",notes = "")
    private Integer cdTodoLongFollowCnt ;
    /** 当日A类跟进数量 */
    @ApiModelProperty(value = "当日A类跟进数量",notes = "")
    private Integer cdLoanFirstFollowCnt ;
    /** 当日断保激活数 */
    @ApiModelProperty(value = "当日断保激活数",notes = "")
    private Integer cdInterruptionTdoFollowCust ;
    /** 当日续保激活数 */
    @ApiModelProperty(value = "当日续保激活数",notes = "")
    private Integer cdTdoShortFollowPolicy ;
    /** 当日续期激活数 */
    @ApiModelProperty(value = "当日续期激活数",notes = "")
    private Integer cdTdoLongFollowPolicy ;
    /** 当日A类激活数 */
    @ApiModelProperty(value = "当日A类激活数",notes = "")
    private Integer cdTdoLoanFirstFollowPolicy ;
    /** 当日断保待办激活保费 */
    @ApiModelProperty(value = "当日断保待办激活保费",notes = "")
    private Double cdInterruptionTodoConversionAmt ;
    /** 当日续保待办激活保费 */
    @ApiModelProperty(value = "当日续保待办激活保费",notes = "")
    private Double cdRenewShortTodoConversionAmt ;
    /** 当日续期激活保费 */
    @ApiModelProperty(value = "当日续期激活保费",notes = "")
    private Double cdRenewLongTodoConversionAmt ;
    /** 当日A类激活保费 */
    @ApiModelProperty(value = "当日A类激活保费",notes = "")
    private Double cdLoanFirstTodoConversionAmt ;
    /** 当日断保跟进数量 */
    @ApiModelProperty(value = "月度断保跟进数量",notes = "")
    private Integer smInterruptionTodoFollowCnt ;
    /** 当日续保跟进数量 */
    @ApiModelProperty(value = "月度续保跟进数量",notes = "")
    private Integer smTdoShortFollowCnt ;
    /** 当日续期跟进数量 */
    @ApiModelProperty(value = "月度续期跟进数量",notes = "")
    private Integer smTdoLongFollowCnt ;
    /** 当日A类跟进数量 */
    @ApiModelProperty(value = "月度A类跟进数量",notes = "")
    private Integer smLoanFirstFollowCnt ;
    /** 截止当前剩余断保待办任务数（未完成的） */
    @ApiModelProperty(value = "截止当前剩余断保待办任务数（未完成的）",notes = "")
    private Integer interruptionTodoCnt ;
    /** 截止当前剩余续保待办任务数（未完成的） */
    @ApiModelProperty(value = "截止当前剩余续保待办任务数（未完成的）",notes = "")
    private Integer renewShortTodoCnt ;
    /** 截止当前剩余续期待办任务数（未完成的） */
    @ApiModelProperty(value = "截止当前剩余续期待办任务数（未完成的）",notes = "")
    private Integer renewLongTodoCnt ;
    /** 截止当前剩余A类待办任务数（未完成的） */
    @ApiModelProperty(value = "截止当前剩余A类待办任务数（未完成的）",notes = "")
    private Integer loanFirstTodoCnt ;
    /** 月度断保待办数量 */
    @ApiModelProperty(value = "月度断保待办数量",notes = "")
    private Integer smInterruptionTodoCnt ;
    /** 月度续保代办数量 */
    @ApiModelProperty(value = "月度续保待办数量",notes = "")
    private Integer smRenewShortTodoCnt ;
    /** 月度续期待办数量 */
    @ApiModelProperty(value = "月度续期待办数量",notes = "")
    private Integer smRenewLongTodoCnt ;
    /** 月度A类代办数量 */
    @ApiModelProperty(value = "月度A类待办数量",notes = "")
    private Integer smLoanFirstTodoCnt ;
    /** 月度断保待办跟进率 */
    @ApiModelProperty(value = "月度断保待办跟进率",notes = "")
    private Double smInterruptionTodoFollowRate ;
    /** 月度续保待办跟进率 */
    @ApiModelProperty(value = "月度续保待办跟进率",notes = "")
    private Double smRenewShortTodoFollowRate ;
    /** 月度续期待办跟进率 */
    @ApiModelProperty(value = "月度续期待办跟进率",notes = "")
    private Double smRenewLongTodoFollowRate ;
    /** 月度A类待办跟进率 */
    @ApiModelProperty(value = "月度A类待办跟进率",notes = "")
    private Double smLoanFirstTodoFollowRate ;
    /** 月度断保激活数量 */
    @ApiModelProperty(value = "月度断保激活数量",notes = "")
    private Integer smInterruptionTdoFollowCust ;
    /** 月度续保激活数量 */
    @ApiModelProperty(value = "月度续保激活数量",notes = "")
    private Integer smTdoShortFollowPolicy ;
    /** 月度续期激活数量 */
    @ApiModelProperty(value = "月度续期激活数量",notes = "")
    private Integer smTdoLongFollowPolicy ;
    /** 月度A类激活数量 */
    @ApiModelProperty(value = "月度A类激活数量",notes = "")
    private Integer smTdoLoanFirstFollowPolicy ;
    /** 月度断保待办转化率 */
    @ApiModelProperty(value = "月度断保待办转化率",notes = "")
    private Double smInterruptionTodoConversionRate ;
    /** 月度续保待办转化率 */
    @ApiModelProperty(value = "月度续保待办转化率",notes = "")
    private Double smRenewShortTodoConversionRate ;
    /** 月度续期待办转化率 */
    @ApiModelProperty(value = "月度续期待办转化率",notes = "")
    private Double smRenewLongTodoConversionRate ;
    /** 月度A类待办转化率 */
    @ApiModelProperty(value = "月度A类待办转化率",notes = "")
    private Double smLoanFirstTodoConversionRate ;
    /** 月度断保待办转化保费 */
    @ApiModelProperty(value = "月度断保待办激活保费",notes = "")
    private Double smInterruptionTodoConversionAmt ;
    /** 月度续保待办转化保费 */
    @ApiModelProperty(value = "月度续保待办激活保费",notes = "")
    private Double smRenewShortTodoConversionAmt ;
    /** 月度续期激活保费 */
    @ApiModelProperty(value = "月度续期激活保费",notes = "")
    private Double smRenewLongTodoConversionAmt ;
    /** 月度A类激活保费 */
    @ApiModelProperty(value = "月度A类激活保费",notes = "")
    private Double smLoanFirstTodoConversionAmt ;

    //生成上面所有属性的get方法，当为空是返回0，返回值是需要格式化为4位小数
    public Double getLoanAssessConvertInsuranceAmtMom() {
        if (loanAssessConvertInsuranceAmtMom == null) {
            return null;
        }
        return BigDecimal.valueOf(loanAssessConvertInsuranceAmtMom).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getLoanAssessConvertInsuranceAmtDod() {
        if (loanAssessConvertInsuranceAmtDod == null) {
            return null;
        }
        return BigDecimal.valueOf(loanAssessConvertInsuranceAmtDod).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getUnloanAssessConvertInsuranceAmtMom() {
        if (unloanAssessConvertInsuranceAmtMom == null) {
            return null;
        }
        return BigDecimal.valueOf(unloanAssessConvertInsuranceAmtMom).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getUnloanAssessConvertInsuranceAmtDod() {
        if (unloanAssessConvertInsuranceAmtDod == null) {
            return null;
        }
        return BigDecimal.valueOf(unloanAssessConvertInsuranceAmtDod).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getCdOfflineLoanInsuranceRate() {
        if (cdOfflineLoanInsuranceRate == null) {
            return null;
        }
        return BigDecimal.valueOf(cdOfflineLoanInsuranceRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getCdAssessConvertInsuranceAmt() {
        if (cdAssessConvertInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(cdAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getSmOfflineLoanAmt() {
        if (smOfflineLoanAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(smOfflineLoanAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getLmOfflineLoanAmt() {
        if (lmOfflineLoanAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(lmOfflineLoanAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getCdLoanInsuranceAmt() {
        if (cdLoanInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(cdLoanInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getSmLoanInsuranceAmt() {
        if (smLoanInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(smLoanInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getLmLoanInsuranceAmt() {
        if (lmLoanInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(lmLoanInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getCdLoanAssessConvertInsuranceAmt() {
        if (cdLoanAssessConvertInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(cdLoanAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getSmLoanAssessConvertInsuranceAmt() {
        if (smLoanAssessConvertInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(smLoanAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getLmLoanAssessConvertInsuranceAmt() {
        if (lmLoanAssessConvertInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(lmLoanAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getSmUnloanAssessConvertInsuranceAmt() {
        if (smUnloanAssessConvertInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(smUnloanAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }


    public Double getLmUnloanAssessConvertInsuranceAmt() {
        if (lmUnloanAssessConvertInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(lmUnloanAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getYdUnloanAssessConvertInsuranceAmt() {
        if (ydUnloanAssessConvertInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(ydUnloanAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getCdOfflineLoanAmt() {
        if (cdOfflineLoanAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(cdOfflineLoanAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    //cdUnloanAssessConvertInsuranceAmt
    public Double getCdUnloanAssessConvertInsuranceAmt() {
        if (cdUnloanAssessConvertInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(cdUnloanAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    public Double getYdLoanAssessConvertInsuranceAmt() {
        if (ydLoanAssessConvertInsuranceAmt == null) {
            return null;
        }
        return BigDecimal.valueOf(ydLoanAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

}
