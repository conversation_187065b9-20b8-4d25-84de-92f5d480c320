package com.cfpamf.ms.insur.report.pojo.vo.personnel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 非信贷客户统计信息
 *
 * <AUTHOR>
 * @date 2021/3/30 11:18
 */
@Data
public class NoLoanerStatisticsVo {
    /**
     * 非信贷客户当月保费
     */
    @ApiModelProperty(value = "非信贷客户当月保费")
    private BigDecimal monthPremium;

    /**
     * 非信贷客户当年保费
     */
    @ApiModelProperty(value = "非信贷客户当年保费")
    private BigDecimal yearPremium;

    /**
     * 非信贷客户当月保费占比
     */
    @ApiModelProperty(value = "非信贷客户当月保费占比")
    private BigDecimal monthPremiumProportion;

    /**
     * 非信贷客户当年保费占比
     */
    @ApiModelProperty(value = "非信贷客户当年保费占比")
    private BigDecimal yearPremiumProportion;

    /**
     * 当月非信贷相关客户占比
     */
    @ApiModelProperty(value = "当月非信贷相关客户占比")
    private BigDecimal currentMonthProportion;
}
