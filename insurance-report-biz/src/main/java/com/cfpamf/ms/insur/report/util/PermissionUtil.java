package com.cfpamf.ms.insur.report.util;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.enums.EnumBmsRole;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.feign.resp.vo.ModuleVO;
import com.cfpamf.ms.insur.report.feign.resp.vo.OrganizationBaseVO;
import com.cfpamf.ms.insur.report.feign.resp.vo.UserDetailVO;
import com.cfpamf.ms.insur.report.pojo.query.DataPermissionQuery;
import com.cfpamf.ms.insur.report.service.BmsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 权限工具类
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
public class PermissionUtil {

    public static final String ROLE_TYPE_BRANCHBU="branchBu";
    public static final String ROLE_TYPE_REGION="region";

    @Autowired
    private BmsService bmsService;

    /**
     * 数据权限query加入区域分支过滤
     *
     * @param query
     */
    public void buildDataPermissionPageQuery(DataPermissionQuery query) {
        UserDetailVO contextUser = ThreadUserUtil.userDetailTL.get();

        //log.info("getContextUserDetail={}", contextUser);



        if (contextUser.getRoleList() == null || contextUser.getRoleList().isEmpty()) {
            throw new MSBizNormalException(ExcptEnum.POST_ROLE_ERROR);
        }
        List<UserDetailVO.UserRoleVO> insRoleList = contextUser.getRoleList()
                .stream().filter(r -> r.getRoleName() != null && r.getRoleName().startsWith("保险"))
                .collect(Collectors.toList());
        if (insRoleList.isEmpty()) {
            throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
        }
        contextUser.setRoleList(insRoleList);
        //构建权限
        buildDataPermission(query,contextUser);

    }

    public void buildDataPermission(DataPermissionQuery query,UserDetailVO contextUser){
        //总部
        if (contextUser.getIsHeadOrg() && hasAuth(contextUser,getAllDataRole())) {

        //区域
        } else if (hasAuth(contextUser,getRegionDataRole())) {
            setQueryOrgInfo(query,contextUser,ROLE_TYPE_REGION);
        //片区
        } else if (hasAuth(contextUser,getZoneDataRole())) {
            //片区  R0024 保险|片区负责人
            query.setZoneName(contextUser.getOrgName());
            query.setZoneCode(contextUser.getOrgCode());
            List<String> orgCodeList = bmsService.listBranchCodesByHrParentId(contextUser.getHrOrgId());
            if(!CollectionUtils.isEmpty(orgCodeList)){
                query.setOrgCodeList(orgCodeList);
            }
        //分支
        } else if (hasAuth(contextUser,getBranchDataRole())) {
            // 当前角色是分支负责人、分支督导、分支内务 增加分支过滤条件
            query.setOrgName(contextUser.getOrgName());
            query.setOrgCode(contextUser.getOrgCode());
        //客户经理
        } else if(hasAuth(contextUser,getCustomerManagerDataRole())){
            query.setIsCustomerManager("1");
            query.setUserId(StringUtils.isEmpty(contextUser.getJianzhiJobNumber())?contextUser.getJobNumber():contextUser.getJianzhiJobNumber());
        }else{
            // 无角色报错
            throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
        }
    }

    private boolean hasAuth(UserDetailVO contextUser,String[] roleArr){
        return contextUser.getRoleList().stream().anyMatch(role -> Arrays.asList(roleArr).contains(role.getRoleCode()) && Objects.equals(role.getOrgId(),contextUser.getOrgId()));
    }


    /**
     * 后续改成从数据库查询
     */
    private String[] getAllDataRole(){
        return new String[]{
                EnumBmsRole.ADMIN.getCode(),EnumBmsRole.BIZ_MANAGER.getCode(),
                EnumBmsRole.BIZ_SUPPORT.getCode(), EnumBmsRole.CLAIM_CLERK.getCode(),
                EnumBmsRole.RISK_BIZ.getCode(), EnumBmsRole.OPER_BIZ.getCode(),
                EnumBmsRole.DATA_BIZ.getCode(), EnumBmsRole.BIZ_CHECK.getCode(),
                EnumBmsRole.FINANCE_BIZ.getCode(), EnumBmsRole.SUB_ADMIN.getCode(),
                EnumBmsRole.AUDIT_BIZ.getCode(), EnumBmsRole.YONG_TONG_MANAGER.getCode(),
                EnumBmsRole.YONG_TONG_BIZ.getCode(), EnumBmsRole.AUDIT_BIZ_2.getCode(),
                EnumBmsRole.AUDIT_BIZ_3.getCode(),EnumBmsRole.PRODUCT_MANAGER.getCode(),
                EnumBmsRole.PERFORMANCE_MANAGER.getCode(),EnumBmsRole.FINANCE_SETTLEMENT.getCode(),
                EnumBmsRole.DATA_CENTER.getCode()
        };
    }

    /**
     * 区域数据角色
     * @return
     */
    private String[] getRegionDataRole(){
        return new String[]{
                EnumBmsRole.REGION_MANAGER.getCode(),EnumBmsRole.REGION_SUPERVISE.getCode(),
                EnumBmsRole.INSUR_REGION_SUPER.getCode()
        };
    }

    /**
     * 片区数据角色
     * @return
     */
    private String[] getZoneDataRole(){
        return new String[]{
                EnumBmsRole.ZONE_MANAGER.getCode()
        };
    }
    /**
     * 分支数据角色
     *
     * @return
     */
    private String[] getBranchDataRole(){
        return new String[]{
                EnumBmsRole.BRANCH_MANAGER.getCode(),EnumBmsRole.BRANCH_SUPERVISE.getCode(),
                EnumBmsRole.INSURANCE_ASSISTANT.getCode(),
                EnumBmsRole.BRANCH_BIZ.getCode(),EnumBmsRole.ORG_ADMIN.getCode(),
                EnumBmsRole.PCO.getCode()
        };
    }

    public String[] getCustomerManagerDataRole(){
        return new String[]{
                EnumBmsRole.CUSTOMER_MANAGER.getCode()
        };
    }

    public String[] getInsBizDataRole(){
        return new String[]{
                EnumBmsRole.ORG_ADMIN.getCode()
        };
    }






    /**
     * 判断登录用户权限对客户敏感数据进行脱敏
     *
     * @param datas
     * @param <T>
     * @return
     */
    public <T> List<T> maskCustomerSensitiveFields(List<T> datas, String permissionCode) {
        List<ModuleVO> modules = ThreadUserUtil.userModulesTL.get();
        if (modules.stream().noneMatch(m -> Objects.equals(m.getModuleCode(), permissionCode))) {
            DataMaskUtil.maskList(datas);
        }
        return datas;
    }

    /**
     * 判断登录用户权限对客户敏感数据进行脱敏
     *
     * @param datas
     * @param <T>
     * @return
     */
    public <T> T maskCustomerSensitiveFields(T datas, String permissionCode) {
        List<ModuleVO> modules = ThreadUserUtil.userModulesTL.get();
        if (modules.stream().noneMatch(m -> Objects.equals(m.getModuleCode(), permissionCode))) {
            DataMaskUtil.maskObject(datas);
        }
        return datas;
    }

    public boolean inRoles(UserDetailVO detailVO, String... roles) {
        if (ArrayUtils.isEmpty(roles)) {
            return true;
        }
        List<UserDetailVO.UserRoleVO> roleList = detailVO.getRoleList();
        for (String role : roles) {
            if (roleList.stream().anyMatch(roleModel -> Objects.equals(roleModel.getRoleCode(), role))) {
                return true;
            }
        }
        return false;

    }

    public boolean inRoles(String... roles) {
        try {
            return inRoles(ThreadUserUtil.userDetailTL.get(), roles);
        } finally {
            ThreadUserUtil.userDetailTL.remove();
        }
    }

   /* *//**
     * 新的权限验证
     * 更加用户的当前所属机构id或者选择框的机构id来进行
     *//*
    public void buildNewDataPermissionPageQuery(String authorization,NewDataPermissionPageQuery query){
        UserDetailVO contextUser = ThreadUserUtil.userDetailTL.get();
        Integer orgId = query.getOrgId();
        if(orgId == null){
            orgId = contextUser.getOrgId();

        }
        String orgType = getOrgType(authorization,orgId);

    }*/

    private String getOrgType(String authorization,Integer orgId){
        /*Result<BmsOrgLevelEnum> result = bmsOrganizationFacade.getOrgLevelByOrgId(authorization,orgId);
        if (!result.isSuccess()) {
            throw new BizException(result.getErrorCode(), ERROR_PREFIX + result.getErrorMsg());
        }
        BmsOrgLevelEnum orgLevelEnum = result.getData();
        return orgLevelEnum.toString();*/
        return  null;
    }

    /**
     * 如果是总部机构树下的事业部、区域就需要重新设置
     * @param query
     * @param contextUser
     * @param type
     */
    public void setQueryOrgInfo(DataPermissionQuery query, UserDetailVO contextUser, String type){
        String userOrgPath = contextUser.getHrOrgTreePath();
        if(isHeadOrgTree(userOrgPath)){//如果是总部区域管理部的，则获取其所管护区域编码
            if(Objects.equals(type,ROLE_TYPE_REGION) && StringUtils.isNotBlank(contextUser.getAreaOrgCode())){
                query.setRegionCode(contextUser.getAreaOrgCode());
            }
        }else{
            if(Objects.equals(type,ROLE_TYPE_REGION)){
                query.setRegionCode(contextUser.getOrgCode());
            }
        }
    }




    /**
     * 是否总部机构树
     * @return
     */
    public boolean isHeadOrgTree(String orgPath){
        return orgPath.startsWith(BaseConstants.BMS_ROOT_HEAD_HR_ORG_ID_PATH);
    }
    /**
     * 根据orgCode获取当前机构信息,如果当前机构是区域办公室/总部事业部则返回对应的分支机构树上的区域信息/分支事业部，其他不变
     * @param orgCode
     * @return
     */
   /* public BmsOrgDTO getBranchOrgByOrgCode(String orgCode){
        if(orgCode==null){
            return null;
        }
        if(branchOrgMap == null){
            branchOrgMap = new HashMap<>();
        }
        BmsOrgDTO dto = branchOrgMap.get(orgCode);
        if(dto == null){
            OrganizationBaseVO organizationBaseVO = bmsService.getBizOrg(orgCode);
            if(organizationBaseVO!=null){
                dto = new BmsOrgDTO();
                BeanUtils.copyProperties(organizationBaseVO,dto);
                branchOrgMap.put(orgCode,dto);
            }
        }
        return dto;
    }*/
}
