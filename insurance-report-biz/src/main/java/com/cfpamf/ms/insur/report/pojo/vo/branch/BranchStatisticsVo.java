package com.cfpamf.ms.insur.report.pojo.vo.branch;

import com.cfpamf.ms.insur.report.pojo.vo.excel.BranchStatisticsExcelVo;
import com.cfpamf.ms.insur.report.pojo.vo.excel.ExcelVoConvert;
import com.cfpamf.ms.insur.report.pojo.vo.personnel.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

/**
 * 分支明细统计
 *
 * <AUTHOR>
 * @date 2021/3/30 17:36
 */
@Data
public class BranchStatisticsVo implements ExcelVoConvert {

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    private String region;

    /**
     * 机构/组织
     */
    @ApiModelProperty(value = "机构/组织")
    private String organizationName;

    /**
     * 员工人数统计
     */
    @ApiModelProperty(value = "员工人数")
    private StaffCountVo staffCountVo;

    /**
     * 单量统计
     */
    @ApiModelProperty(value = "单量统计")
    private OrderQuantityStatisticsVo orderQuantityStatisticsVo;

    /**
     * 保费统计
     */
    @ApiModelProperty(value = "保费统计")
    private PremiumStatisticsVo premiumStatisticsVo;

    /**
     * 保费目标
     */
    @ApiModelProperty(value = "保费目标")
    private PremiumTargetVo premiumTargetVo = new PremiumTargetVo();

    /**
     * 保费目标完成率
     */
    @ApiModelProperty(value = "保费目标完成率")
    private CompletionRateVo completionRateVo = new CompletionRateVo();

    /**
     * 月度人均保费统计
     */
    @ApiModelProperty(value = "月度人均保费")
    private PerCapitaPremiumVo perCapitaPremiumVo = new PerCapitaPremiumVo();

    /**
     * 非信贷客户统计
     */
    @ApiModelProperty(value = "非信贷客户")
    private NoLoanerStatisticsVo noLoanerStatisticsVo = new NoLoanerStatisticsVo();

    /**
     * 续保率统计
     */
    @ApiModelProperty(value = "续保率")
    private RenewInsuranceRateVo renewInsuranceRateVo = new RenewInsuranceRateVo();

    /**
     * 客户转化率
     */
    @ApiModelProperty(value = "客户转化率")
    private CustomerConversionRateVo customerConversionRateVo = new CustomerConversionRateVo();

    @Override
    public Object convert() {
        BranchStatisticsExcelVo branchStatisticsExcelVo = new BranchStatisticsExcelVo();
        //设置基础信息
        BeanUtils.copyProperties(this, branchStatisticsExcelVo);
        //设置单量统计信息
        OrderQuantityStatisticsVo orderQuantityStatisticsVo = getOrderQuantityStatisticsVo();
        if (Objects.nonNull(orderQuantityStatisticsVo)) {
            branchStatisticsExcelVo.setDayOrderQuantity(orderQuantityStatisticsVo.getDay());
            branchStatisticsExcelVo.setMonthOrderQuantity(orderQuantityStatisticsVo.getMonth());
            branchStatisticsExcelVo.setYearOrderQuantity(orderQuantityStatisticsVo.getYear());
        }
        //设置在职人数信息
        StaffCountVo staffCountVo = getStaffCountVo();
        if (Objects.nonNull(staffCountVo)) {
            branchStatisticsExcelVo.setMonthJobStaffCount(staffCountVo.getMonthJobStaffCount());
            branchStatisticsExcelVo.setOrganizationStaffCount(staffCountVo.getOrganizationStaffCount());
        }
        //设置保费信息
        PremiumStatisticsVo premiumStatisticsVo = getPremiumStatisticsVo();
        if (Objects.nonNull(premiumStatisticsVo)) {
            branchStatisticsExcelVo.setDayPremium(premiumStatisticsVo.getDay());
            branchStatisticsExcelVo.setMonthPremium(premiumStatisticsVo.getMonth());
            branchStatisticsExcelVo.setYearPremium(premiumStatisticsVo.getYear());
        }
        //设置非信贷客户信息
        NoLoanerStatisticsVo noLoanerStatisticsVo = getNoLoanerStatisticsVo();
        if (Objects.nonNull(noLoanerStatisticsVo)) {
            branchStatisticsExcelVo.setMonthPremiumNoLoaner(noLoanerStatisticsVo.getMonthPremium());
            branchStatisticsExcelVo.setYearPremiumNoLoaner(noLoanerStatisticsVo.getYearPremium());
            branchStatisticsExcelVo.setMonthPremiumProportionNoLoaner(noLoanerStatisticsVo.getMonthPremiumProportion());
            branchStatisticsExcelVo.setYearPremiumProportionNoLoaner(noLoanerStatisticsVo.getYearPremiumProportion());
            branchStatisticsExcelVo.setCurrentMonthProportionNoLoaner(noLoanerStatisticsVo.getCurrentMonthProportion());
        }
        //设置转化率信息
        CustomerConversionRateVo customerConversionRateVo = getCustomerConversionRateVo();
        if (Objects.nonNull(customerConversionRateVo)) {
            branchStatisticsExcelVo.setLoanerConversionRate(customerConversionRateVo.getLoaner());
            branchStatisticsExcelVo.setLoanerRelevantConversionRate(customerConversionRateVo.getLoanerRelevant());
        }
        //设置续保率信息
        RenewInsuranceRateVo renewInsuranceRateVo = getRenewInsuranceRateVo();
        if (Objects.nonNull(renewInsuranceRateVo)) {
            branchStatisticsExcelVo.setYearRenewInsuranceRate(renewInsuranceRateVo.getYear());
            branchStatisticsExcelVo.setMonthRenewInsuranceRate(renewInsuranceRateVo.getMonth());
        }
        //设置保费目标
        PremiumTargetVo premiumTargetVo = getPremiumTargetVo();
        if (Objects.nonNull(premiumTargetVo)) {
            branchStatisticsExcelVo.setCurrentMonthPremiumTarget(premiumTargetVo.getCurrentMonth());
            branchStatisticsExcelVo.setCurrentYearPremiumTarget(premiumTargetVo.getCurrentYear());
            branchStatisticsExcelVo.setPresentCurrentMonthPremiumTarget(premiumTargetVo.getPresentCurrentMonth());
        }
        //设置目标完成率
        CompletionRateVo completionRateVo = getCompletionRateVo();
        if (Objects.nonNull(completionRateVo)) {
            branchStatisticsExcelVo.setCurrentMonthCompletionRate(completionRateVo.getCurrentMonth());
            branchStatisticsExcelVo.setCurrentYearCompletionRate(completionRateVo.getCurrentYear());
            branchStatisticsExcelVo.setPresentCurrentMonthCompletionRate(completionRateVo.getPresentCurrentMonth());
        }
        //设置人均保费
        PerCapitaPremiumVo perCapitaPremiumVo = getPerCapitaPremiumVo();
        if (Objects.nonNull(perCapitaPremiumVo)) {
            branchStatisticsExcelVo.setCurrentMonthJobStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentMonthJobStaff());
            branchStatisticsExcelVo.setCurrentYearJobStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentYearJobStaff());
            branchStatisticsExcelVo.setCurrentMonthOrganizationStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentMonthOrganizationStaff());
            branchStatisticsExcelVo.setCurrentYearOrganizationStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentYearOrganizationStaff());
        }
        return branchStatisticsExcelVo;
    }
}
