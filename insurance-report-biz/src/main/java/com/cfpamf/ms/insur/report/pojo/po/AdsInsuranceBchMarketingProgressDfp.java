package com.cfpamf.ms.insur.report.pojo.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 保险助手分支集市;
 * <AUTHOR> 王浩
 * @date : 2024-2-28
 */
@ApiModel(value = "保险助手分支集市")
@Data
public class AdsInsuranceBchMarketingProgressDfp {

    @ApiModelProperty("分支名称")
    private String bchName;

    @ApiModelProperty("分支编码")
    private String bchCode;

    @ApiModelProperty("片区名称")
    private String districtName;

    @ApiModelProperty("片区编码")
    private String districtCode;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("当月标准保费")
    private Double smAssessConvertInsuranceAmt;

    @ApiModelProperty("当年标准保费")
    private Double syAssessConvertInsuranceAmt;

    @ApiModelProperty("当月标准保费目标")
    private Double smAssessConvertInsuranceAmtTarget;

    @ApiModelProperty("当年标准保费目标")
    private Double syAssessConvertInsuranceAmtTarget;

    @ApiModelProperty("当月标准保费达成率")
    private Double smAssessConvertInsuranceAmtAchieveRate;

    @ApiModelProperty("当年标准保费达成率")
    private Double syAssessConvertInsuranceAmtAchieveRate;

    @ApiModelProperty("当月异业保费配比")
    private Double smOfflineLoanInsuranceRate;

    @ApiModelProperty("当年异业保费配比")
    private Double syOfflineLoanInsuranceRate;

    @ApiModelProperty("当月异业保费配比目标")
    private Double smOfflineLoanInsuranceRateTarget;

    @ApiModelProperty("当年异业保费配比目标")
    private Double syOfflineLoanInsuranceRateTarget;

    @ApiModelProperty("当月留存率")
    private Double smInsuranceRetentionRate;

    @ApiModelProperty("当年留存率")
    private Double syInsuranceRetentionRate;

    @ApiModelProperty("当月留存率目标")
    private Double smInsuranceRetentionRateTarget;

    @ApiModelProperty("当年留存率目标")
    private Double syInsuranceRetentionRateTarget;

    @ApiModelProperty("当月信贷客户留存率")
    private Double smLoanInsuranceRetentionRate;

    @ApiModelProperty("当年信贷客户留存率")
    private Double syLoanInsuranceRetentionRate;

    @ApiModelProperty("当月非贷客户留存率")
    private Double smUnloanInsuranceRetentionRate;

    @ApiModelProperty("当年非贷客户留存率")
    private Double syUnloanInsuranceRetentionRate;

    @ApiModelProperty("当月信贷客户转化率")
    private Double smLoanCustTransRate;

    @ApiModelProperty("当年信贷客户转化率")
    private Double syLoanCustTransRate;

    @ApiModelProperty("上月标准保费")
    private Double lmAssessConvertInsuranceAmt;

    @ApiModelProperty("分区")
    private String pt;
}
