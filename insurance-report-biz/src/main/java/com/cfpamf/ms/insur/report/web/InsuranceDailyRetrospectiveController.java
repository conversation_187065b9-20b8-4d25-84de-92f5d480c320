package com.cfpamf.ms.insur.report.web;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.feign.resp.vo.UserVo;
import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveBasicDTO;
import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveBasicTrendDTO;
import com.cfpamf.ms.insur.report.pojo.dto.DailyRetrospectiveFinanceStatisticsDTO;
import com.cfpamf.ms.insur.report.pojo.query.InsuranceDailyRetrospectiveQuery;
import com.cfpamf.ms.insur.report.pojo.vo.EmployeeVo;
import com.cfpamf.ms.insur.report.service.retrospective.InsuranceDailyRetrospective;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("retrospective")
@Api(tags = "日复盘接口")
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class InsuranceDailyRetrospectiveController {
    @Autowired
    InsuranceDailyRetrospective insuranceDailyRetrospective;


    @ApiOperation(value = "获取员工信贷转化指标")
    @PostMapping(value = "customer/convert/metrics")
    public List<DailyRetrospectiveBasicDTO> getLoanCustomerConvertMetrics(@RequestBody InsuranceDailyRetrospectiveQuery query) {
        checkParams(query);
        List<EmployeeVo> empList = getEmpCodes(query);
        String pt = LocalDate.parse(query.getDate(), BaseConstants.FMT_YYYY_MM_DD).format(BaseConstants.FMT_YYYYMMDD);
        return insuranceDailyRetrospective.getLoanCustomerConvertMetrics(empList, query.getBchCode(), pt);
    }



    @ApiOperation(value = "获取员工信贷，非贷指标")
    @PostMapping(value ="customer/loanAndUnloan/metrics")
    public List<DailyRetrospectiveBasicDTO> getLoanAndUnloanMetrics(@RequestBody InsuranceDailyRetrospectiveQuery query) {
        checkParams(query);
        List<EmployeeVo> empCodes = getEmpCodes(query);
        String pt = LocalDate.parse(query.getDate(), BaseConstants.FMT_YYYY_MM_DD).format(BaseConstants.FMT_YYYYMMDD);
        return insuranceDailyRetrospective.getLoanAndUnloanMetrics(empCodes, query.getBchCode(), pt);
    }

    @ApiOperation(value = "获取员工留存指标" )
    @PostMapping(value ="customer/retention/metrics")
    public List<DailyRetrospectiveBasicDTO> getCustomerRetentionMetrics(@RequestBody InsuranceDailyRetrospectiveQuery query) {
        checkParams(query);
        List<EmployeeVo> emp = getEmpCodes(query);
        String pt = LocalDate.parse(query.getDate(), BaseConstants.FMT_YYYY_MM_DD).minusDays(1).format(BaseConstants.FMT_YYYYMMDD);
        return insuranceDailyRetrospective.getCustomerRetentionMetrics(emp, query.getBchCode(), pt);
    }
    @ApiOperation(value = "获取员工跟进指标")
    @PostMapping(value ="customer/follow/metrics")
    public List<DailyRetrospectiveBasicDTO> getCustomerFollowMetrics(@RequestBody InsuranceDailyRetrospectiveQuery query) {
        checkParams(query);
        List<EmployeeVo> empCodes = getEmpCodes(query);
        String pt = LocalDate.parse(query.getDate(), BaseConstants.FMT_YYYY_MM_DD).format(BaseConstants.FMT_YYYYMMDD);
        if (LocalDate.now().isEqual(LocalDate.parse(query.getDate(), BaseConstants.FMT_YYYY_MM_DD))) {
            pt = LocalDate.parse(query.getDate(), BaseConstants.FMT_YYYY_MM_DD).minusDays(1).format(BaseConstants.FMT_YYYYMMDD);
        }
        return insuranceDailyRetrospective.getCustomerFollowMetrics(empCodes, query.getBchCode(), pt,query.getDate());
    }

    @ApiOperation(value = "获取员工业绩指标")
    @PostMapping(value ="customer/convert/insurance/metrics")
    public List<DailyRetrospectiveBasicDTO> getCustomerConvertInsuranceMetrics(@RequestBody InsuranceDailyRetrospectiveQuery query) {
        checkParams(query);
        List<EmployeeVo> empCodes = getEmpCodes(query);
        String pt = "";
        //判断日期参数是否为当天，是则pt取前一天，否则pt取参数日期当天
        if (LocalDate.now().isEqual(LocalDate.parse(query.getDate(), BaseConstants.FMT_YYYY_MM_DD))) {
            pt = LocalDate.parse(query.getDate(), BaseConstants.FMT_YYYY_MM_DD).minusDays(1).format(BaseConstants.FMT_YYYYMMDD);
        } else {
            pt = LocalDate.parse(query.getDate(), BaseConstants.FMT_YYYY_MM_DD).format(BaseConstants.FMT_YYYYMMDD);
        }
        String monday = LocalDate.parse(pt, BaseConstants.FMT_YYYYMMDD).with(DayOfWeek.MONDAY).format(BaseConstants.FMT_YYYY_MM_DD);
        return insuranceDailyRetrospective.getCustomerConvertInsuranceMetrics(empCodes, query.getBchCode(), pt,query.getDate(),monday);
    }

    @ApiOperation(value = "查询员工保费配比日趋势" )
    @PostMapping(value ="trend/insuranceRate")
    public List<DailyRetrospectiveBasicTrendDTO> getTrendInsuranceRate(@RequestBody InsuranceDailyRetrospectiveQuery query) {
        if(CollectionUtils.isEmpty(query.getEmpCodes())){
            throw new MSBizNormalException(ExcptEnum.DAILY_RETROSPECTIVE_EMP_LIST_ERROR);
        }
        return insuranceDailyRetrospective.getTrendInsuranceRate(query.getEmpCodes().get(0), query.getBchCode(), query.getDate());
    }

    @ApiOperation(value = "获取员工下级列表" )
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "supervisorCode", value = "员工工号", required = true),
            @ApiImplicitParam(name = "bchCode", value = "分支编码", required = false)})
    @GetMapping(value ="employee/list")
    public List<UserVo> getUserInfoBySupervisor(@RequestParam(value = "supervisorCode", required = true ) String supervisorCode, @RequestParam(value = "bchCode", required = false )String bchCode) {
        return insuranceDailyRetrospective.getUserInfoBySupervisor(supervisorCode, bchCode);
    }

    private void checkParams(InsuranceDailyRetrospectiveQuery query){
        if (CollectionUtils.isEmpty(query.getEmpCodes())&& StringUtils.isEmpty(query.getSupervisorCode())){
            throw new MSBizNormalException(ExcptEnum.DAILY_RETROSPECTIVE_PARAM_ERROR);
        }
    }
    private List<EmployeeVo> getEmpCodes(InsuranceDailyRetrospectiveQuery query) {
        List<EmployeeVo> emps = new ArrayList<>();
        if (CollectionUtils.isEmpty(query.getEmpCodes())) {
            List<UserVo> userVos = insuranceDailyRetrospective.getUserInfoBySupervisor(query.getSupervisorCode(), query.getBchCode());
            if (CollectionUtils.isEmpty(userVos)){
                throw new MSBizNormalException(ExcptEnum.DAILY_RETROSPECTIVE_SUPERVISOR_ERROR);
            }
            emps.addAll(userVos.stream().filter(vo->"A".equals(vo.getUserSts())).map(
                    vo->{
                        EmployeeVo employeeVo = new EmployeeVo();
                        employeeVo.setEmpCode(vo.getUserCode());
                        employeeVo.setEmpName(vo.getUserName());
                        return employeeVo;
                    }
            ).collect(Collectors.toList()));
        }else{
            List<String> empCodes = query.getEmpCodes();
            List<String> empNames = query.getEmpNames();
            // 聚合empCodes，empNames 变成EmployeeVo 列表
            for (int i = 0; i < empCodes.size();i++){
                EmployeeVo employeeVo = new EmployeeVo();
                employeeVo.setEmpName(empNames.get(i));
                employeeVo.setEmpCode(empCodes.get(i));
                emps.add(employeeVo);
            }
        }
        return emps;
    }

    /**
     * 查询客户经理异业保费转化情况
     * @param query
     *
     * @return
     */
    @ApiOperation(value = "查询客户经理异业保费转化情况" )
    @PostMapping(value ="customer/convert/premium")
    public DailyRetrospectiveFinanceStatisticsDTO getCustomerConvertPremium(@RequestBody InsuranceDailyRetrospectiveQuery query) {
        if(CollectionUtils.isEmpty(query.getEmpCodes())){
            throw new MSBizNormalException(ExcptEnum.DAILY_RETROSPECTIVE_EMP_LIST_ERROR);
        }
        return insuranceDailyRetrospective.getCustomerConvertPremium(query.getEmpCodes().get(0), query.getBchCode(),query.getDate());
    }
}
