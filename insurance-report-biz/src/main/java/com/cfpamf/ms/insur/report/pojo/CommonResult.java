package com.cfpamf.ms.insur.report.pojo;

import com.cfpamf.common.ms.exception.MSException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Objects;

/**
 * rest api返回包装类
 *
 * <AUTHOR>
 */
@Data
public class CommonResult<T> {

    /**
     * 默认成功code
     */
    public static final String SUCCESS_CODE = "0";

    /**
     * 默认成功message
     */
    public static final String SUCCESS_MESSAGE = "success";

    /**
     * 默认失败code
     */
    public static final String FAIL_CODE = "999";

    /**
     * 默认失败message
     */
    public static final String FAIL_MESSAGE = "接口异常";

    /**
     * 授权失败code
     */
    public static final String AUTH_FAIL_CODE = "401";

    /**
     * 授权失败message
     */
    public static final String AUTH_FAIL_MESSAGE = "授权失败请登录重试";

    /**
     * code
     */
    private String code;

    /**
     * message
     */
    private String message;

    /**
     * 异常
     */
    @JsonIgnore
    private Throwable throwable;

    /**
     * data
     */
    private T data;

    /**
     * 默认成功CommonResult
     */
    public static <T> CommonResult<T> successResult(T v) {
        CommonResult<T> result = new CommonResult<>();
        result.setCode(SUCCESS_CODE);
        result.setMessage(SUCCESS_MESSAGE);
        result.setData(v);
        return result;
    }

    /**
     * 默认失败CommonResult
     */
    public static CommonResult failResult() {
        CommonResult result = new CommonResult<>();
        result.setCode(CommonResult.FAIL_CODE);
        result.setMessage(FAIL_MESSAGE);
        return result;
    }

    public static CommonResult failResult(MSException e) {
        CommonResult result = new CommonResult<>();
        result.setCode(e.getErrorCode());
        result.setMessage(e.getMessage());
        result.setThrowable(e);
        return result;
    }

    /**
     * 默认失败CommonResult
     */
    public static CommonResult failResult(Exception e) {
        CommonResult result = new CommonResult<>();
        result.setCode(CommonResult.FAIL_CODE);
        result.setMessage(FAIL_MESSAGE);
        result.setThrowable(e);
        return result;
    }

    /**
     * 授权失败CommonResult
     */
    public static CommonResult authFailResult() {
        CommonResult result = new CommonResult<>();
        result.setCode(CommonResult.AUTH_FAIL_CODE);
        result.setMessage(AUTH_FAIL_MESSAGE);
        return result;
    }

    /**
     * 调用是否成功
     *
     * @return
     */
    public boolean isSuccess() {
        return Objects.equals(code, SUCCESS_CODE);
    }

    /**
     * 异常堆栈字符串
     *
     * @return
     */
    public String getException() {
        if (throwable != null) {
            StringWriter stringWriter = new StringWriter();
            throwable.printStackTrace(new PrintWriter(stringWriter));
            return stringWriter.toString();
        }
        return null;
    }
}
