package com.cfpamf.ms.insur.report.pojo.dto;

import com.cfpamf.ms.insur.report.pojo.query.OrderByClause;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AssistantManagerPromotionRankDetail {


    public AssistantManagerPromotionRankDetail(Integer rank){
        this.rank = rank;
    }
    public AssistantManagerPromotionRankDetail(){

    }
    @ApiModelProperty(value = "层级名称", notes = "")
    String name;

    @ApiModelProperty(value = "当月分享数", notes = "")
    String smCnt;

    @ApiModelProperty(value = "当月人均分享数", notes = "")
    String smAvg;

    @ApiModelProperty(value = "排名", notes = "")
    Integer rank;
}
