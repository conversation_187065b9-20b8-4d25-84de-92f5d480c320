package com.cfpamf.ms.insur.report.feign;


import com.cfpamf.ms.insur.report.feign.req.GetEffectiveShareInfoQueryDTO;
import com.cfpamf.ms.insur.report.feign.resp.OmsBaseResponse;
import com.cfpamf.ms.insur.report.feign.resp.vo.MarketingForwardEffectiveInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "oms-service-base", url = "${oms.api.url}")
public interface OmsFacade {

    @ApiOperation("查询员工分享数量")
    @RequestMapping(value = "/publics/marketing/getEffectiveShareInfo", method = RequestMethod.POST)
    public OmsBaseResponse<List<MarketingForwardEffectiveInfo>> getEffectiveShareInfo(@RequestBody GetEffectiveShareInfoQueryDTO getEffectiveShareInfoQueryDTO);
}
