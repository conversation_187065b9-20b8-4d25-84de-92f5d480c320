package com.cfpamf.ms.insur.report.pojo.query;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.enums.EnumAssistantAreaDim;
import com.cfpamf.ms.insur.report.enums.EnumAssistantTimeDim;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/2/22 14:51
 */
@Data
@ApiModel("管理助手查询对象")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantAdminQuery extends PageQuery {
    static final long VALIDATION_BEFORE_DAY = 8L;

    @ApiModelProperty("相对分区")
    String pt;
    /**
     * 1-本月，2-本年
     */
    @NotNull(message = "时间维度不能为空")
    @ApiModelProperty("时间维度 MONTH-本月，YEAR-本年")
    EnumAssistantTimeDim timeDim;

    /**
     * 1-区域维度，2-片区维度 3-
     */
    @NotNull(message = "区域维度不能为空")
    @ApiModelProperty("区域维度 ALL 全国 AREA 区域 DISTRICT 片区 BRANCH 分支")
    EnumAssistantAreaDim areaDim;

    /**
     * 片区编码列表
     */
    @ApiModelProperty("片区编码列表 选择全部片区 就不传或者空数组 如果不是需要筛选片区就不要传")
    List<String> districtCodes;

    /**
     * 区域编码
     */
    @ApiModelProperty("区域编码 全部区域就不传")
    String areaCode;

    /**
     * 分支编码
     */
    @ApiModelProperty("分支编码 全部分支就不传")
    String bchCode;

    /**
     * 区域编码
     */
    @ApiModelProperty("账号授权区域编码")
    String areaCodeDataAuth;

    /**
     * 分支编码
     */
    @ApiModelProperty("账号授权分支编码")
    String bchCodeDataAuth;

    /**
     * 隐藏的区域名称
     */
    @ApiModelProperty(hidden = true)
    String areaName;

    /**
     * 隐藏的分支名称
     */
    @ApiModelProperty(hidden = true)
    String bchName;


    /**
     * 检查所有权限
     */
    public void checkAllAuth() {
        // 如果区域名称和业务渠道名称都不为空，则抛出异常
        if (!StringUtils.isAllBlank(areaName, bchName)) {
            throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
        }
    }

    /**
     * 检查区域权限
     */
    public void checkAreaAuth() {
        if (StringUtils.isNotBlank(bchName)) {
            throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
        }
    }


    /**
     * 获取当前日期前一天的日期字符串，格式为YYYYMMDD。如果已有pt变量且不为空，则直接返回该pt变量的值。
     *
     * @return 返回前一天日期的字符串表示，格式为YYYYMMDD。
     */
    public String getPt() {
        // 判断pt变量是否已存在且不为空，若满足条件则直接返回pt
        if (StringUtils.isNotBlank(pt)) {
            LocalDate parse = LocalDate.parse(pt, BaseConstants.FMT_YYYYMMDD);
            if (parse.isBefore(LocalDate.now().minusDays(VALIDATION_BEFORE_DAY))) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "pt不合法：" + pt);
            }
            return pt;
        }
        // 若pt为空，则获取当前日期前一天的日期，并以YYYYMMDD格式返回
        return LocalDate.now().minusDays(1L).format(BaseConstants.FMT_YYYYMMDD);
    }

    /**
     * 分支编码
     */
    @ApiModelProperty("分支编码 全部分支就不传")
    List<String> bchCodeList;
}

