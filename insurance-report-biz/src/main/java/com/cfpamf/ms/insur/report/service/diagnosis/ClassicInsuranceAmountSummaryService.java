package com.cfpamf.ms.insur.report.service.diagnosis;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.dao.safepg.*;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.enums.OrgLevelEnum;
import com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo;
import com.cfpamf.ms.insur.report.service.diagnosis.model.AbstractDiagnosisModel;
import com.cfpamf.ms.insur.report.service.diagnosis.model.ClassicInsuranceAmountSummaryModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 标准保费小结的诊断实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/02/27
 */
@Service
public class ClassicInsuranceAmountSummaryService extends AbstractBizHelperDiagnosisService {

    @Resource
    private AdsInsuranceCfpamfMarketingProgressDfpMapper cfpamfMapper;

    @Resource
    private AdsInsuranceAreaMarketingProgressDfpMapper areaMapper;

    @Resource
    private AdsInsuranceDistrictMarketingProgressDfpMapper districtMapper;

    @Resource
    private AdsInsuranceBchMarketingProgressDfpMapper branchMapper;

    @Resource
    private AdsInsuranceEmpMarketingProgressDfpMapper personMapper;

    /**
     * 获取受支持的诊断类型
     *
     * @return 诊断类型的枚举类
     */
    @Override
    public DiagnosisTypeEnum getSupportDiagnosisType() {
        return DiagnosisTypeEnum.CLASSIC_INSURANCE_AMOUNT_SUMMARY;
    }

    /**
     * 根据请求参数，创建诊断的数据模型
     *
     * @param request 请求参数
     * @return 诊断的数据模型
     */
    @Override
    protected AbstractDiagnosisModel createModel(DiagnosisRequest request) {
        ClassicInsuranceAmountSummaryModel model = new ClassicInsuranceAmountSummaryModel();
        OrgLevelEnum orgLevel = request.getOrgLevel();
        String pt = request.getPt();
        switch (orgLevel) {
            case COUNTRY: {
                Boolean assessConvertInsuranceAmtTag = cfpamfMapper.queryAssessConvertInsuranceAmtSummary(pt);
                if (assessConvertInsuranceAmtTag == null) {
                    return null;
                }
                List<DiagnosisRankInfoVo> rankInfoVos = areaMapper.queryAssessConvertInsuranceAmtSummaryRankInfo(pt, assessConvertInsuranceAmtTag);
                constructModel(rankInfoVos, model, orgLevel, assessConvertInsuranceAmtTag);
                break;
            }
            case AREA: {
                Boolean assessConvertInsuranceAmtTag = areaMapper.queryAssessConvertInsuranceAmtSummary(pt, request.getOrgCodes());
                if (assessConvertInsuranceAmtTag == null) {
                    return null;
                }
                List<DiagnosisRankInfoVo> rankInfoVos = null;
                if(request.isExistDistrict()){
                    //区域下有片区则获取片区数据
                    rankInfoVos = districtMapper.queryAssessConvertInsuranceAmtSummaryRankInfo(pt, assessConvertInsuranceAmtTag, request.getOrgCodes(), orgLevel.getLevel());
                }else{
                    //区域下无片区则获取分支数据
                    rankInfoVos = branchMapper.queryAssessConvertInsuranceAmtSummaryRankInfo(pt, assessConvertInsuranceAmtTag, request.getOrgCodes(), orgLevel.getLevel(),request.getBlackBchList());
                }
                //设置区域下是否有片区,后续freemaker模板中逻辑判断用
                model.setExistDistrict(request.isExistDistrict());
                constructModel(rankInfoVos, model, orgLevel, assessConvertInsuranceAmtTag);
                break;
            }
            case DISTRICT: {
                Boolean assessConvertInsuranceAmtTag = districtMapper.queryAssessConvertInsuranceAmtSummary(pt, request.getOrgCodes());
                if (assessConvertInsuranceAmtTag == null) {
                    return null;
                }
                List<DiagnosisRankInfoVo> rankInfoVos = branchMapper.queryAssessConvertInsuranceAmtSummaryRankInfo(pt, assessConvertInsuranceAmtTag, request.getOrgCodes(), orgLevel.getLevel(),request.getBlackBchList());
                constructModel(rankInfoVos, model, orgLevel, assessConvertInsuranceAmtTag);
                break;
            }
            case BRANCH: {
                Boolean assessConvertInsuranceAmtTag = branchMapper.queryAssessConvertInsuranceAmtSummary(pt, request.getOrgCodes());
                if (assessConvertInsuranceAmtTag == null) {
                    return null;
                }
                List<DiagnosisRankInfoVo> rankInfoVos = personMapper.queryAssessConvertInsuranceAmtSummaryRankInfo(pt, assessConvertInsuranceAmtTag, request.getOrgCodes());
                constructModel(rankInfoVos, model, orgLevel, assessConvertInsuranceAmtTag);
                break;
            }
            default:
                break;
        }
        return model;
    }

    /**
     * 构建业绩是否达成的数据模型
     *
     * @param rankInfoVos                  排行信息
     * @param model                        模型对象
     * @param orgLevel                     组织机构级别
     * @param assessConvertInsuranceAmtTag 业绩是否达成
     */
    private static void constructModel(List<DiagnosisRankInfoVo> rankInfoVos, ClassicInsuranceAmountSummaryModel model, OrgLevelEnum orgLevel, boolean assessConvertInsuranceAmtTag) {
        List<String> classicInsuranceAmountMonthRankNames = new ArrayList<>(3);
        List<String> classicInsuranceAmountYearRankNames = new ArrayList<>(3);
        if (CollectionUtils.isNotEmpty(rankInfoVos)) {
            for (DiagnosisRankInfoVo rankInfoVo : rankInfoVos) {
                if (StringUtils.isNotBlank(rankInfoVo.getSmAssessConvertInsuranceAmtAchieveRateRankName())) {
                    classicInsuranceAmountMonthRankNames.add(rankInfoVo.getSmAssessConvertInsuranceAmtAchieveRateRankName());
                }
                if (StringUtils.isNotBlank(rankInfoVo.getSyAssessConvertInsuranceAmtAchieveRateRankName())) {
                    classicInsuranceAmountYearRankNames.add(rankInfoVo.getSyAssessConvertInsuranceAmtAchieveRateRankName());
                }
            }
        }
        ClassicInsuranceAmountSummaryModel.Summary summary = new ClassicInsuranceAmountSummaryModel.Summary();
        model.setSummary(summary);
        summary.setType(orgLevel.getLevel());
        summary.setFinished(assessConvertInsuranceAmtTag);
        summary.setClassicInsuranceAmountMonthRankNames(classicInsuranceAmountMonthRankNames);
        summary.setClassicInsuranceAmountYearRankNames(classicInsuranceAmountYearRankNames);
    }

    /**
     * 获取诊断模板文件的地址
     *
     * @return 模板文件的地址
     */
    @Override
    protected String getTemplateFilePath() {
        return "classic_insurance_amount_summary.ftl";
    }
}
