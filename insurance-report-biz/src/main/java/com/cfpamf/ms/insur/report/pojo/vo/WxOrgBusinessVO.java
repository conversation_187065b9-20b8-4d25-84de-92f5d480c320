package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WxOrgBusinessVO extends WxBuisnessDetailsVO implements Serializable {

    @ApiModelProperty("分支机构")
    private String orgName;
    @ApiModelProperty("达成率")
    private BigDecimal achievementRate;

    @ApiModelProperty("续保率排名")
    private Integer renewalRateRank;

    @ApiModelProperty("非信贷及相关客户占比")
    private BigDecimal unloanValidInsuredPer;

    @ApiModelProperty("入围排名")
    private String cadidationRank;

    @ApiModelProperty("人均保费")
    private BigDecimal avgInsuranceAmtMonthly;

    @ApiModelProperty("编制人均保费")
    private BigDecimal planEmpAverageAmt;


    @ApiModelProperty("累计月度人均保费")
    private BigDecimal acmAvgInsuranceAmtMonthly;
}
