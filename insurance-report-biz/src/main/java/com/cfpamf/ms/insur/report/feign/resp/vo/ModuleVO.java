package com.cfpamf.ms.insur.report.feign.resp.vo;



import com.cfpamf.ms.insur.report.feign.enums.BmsModuleTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 权限VO
 */
@Data
@ApiModel
public class ModuleVO implements Serializable {

    private static final long serialVersionUID = 1571396380118318547L;

    private Integer moduleId;

    /**
     * systemId
     */
    @ApiModelProperty(value = "systemId", required = true)
    private Integer systemId;

    /**
     * 系统名称
     */
    @ApiModelProperty(value = "系统名称", required = true)
    private String systemShortName;

    /**
     * 类型(1.菜单权限 2.按钮权限)
     */
    @ApiModelProperty(value = "类型(1.菜单权限 2.按钮权限)", required = true)
    private Integer moduleType = BmsModuleTypeEnum.Menu.getValue();

    /**
     * 权限编码
     */
    @ApiModelProperty(value = "权限编码", required = true)
    private String moduleCode;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称", required = true)
    private String moduleName;

    /**
     * 模块图标
     */
    @ApiModelProperty(value = "模块图标", required = true)
    private String moduleIcon;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID", required = true)
    private Integer parentId;

    /**
     * 父名称
     */
    @ApiModelProperty(value = "父名称", required = true)
    private String parentName;

    /**
     * 调用入口
     */
    @ApiModelProperty(value = "调用入口", required = true)
    private String route;

    /**
     * 层级树
     */
    @ApiModelProperty(value = "层级树", required = true)
    private String idPath;

    /**
     * 功能描述
     */
    @ApiModelProperty(value = "功能描述", required = true)
    private String moduleDesc;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", required = true)
    private Integer orderNo;

    /**
     * 状态（0.停用 1.启用）
     */
    @ApiModelProperty(value = "状态（0.停用 1.启用）", required = true)
    private Integer moduleStatus;

    @ApiModelProperty(value = "app模块名称")
    private String appModuleName;

    @ApiModelProperty(value = "app模块图标")
    private String appModuleIcon;

    @ApiModelProperty(value = "app模块路由")
    private String appRoute;

    @ApiModelProperty(value = "app模块状态（0.停用 1.启用）")
    private Integer appModuleStatus;

    /**
     * 对应的接口
     */
    @ApiModelProperty(value = "对应的接口")
    private String api;
}
