package com.cfpamf.ms.insur.report.pojo.vo.assistant;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;


/**
 * 保险助手 区域维度
 *
 *
 * <AUTHOR>
 * @since 2024/2/21 14:11
 */
@Data
@ApiModel("保险助手-区域维度-都是下划线名")
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AssistantBchVO extends AssistantBasicVO {

    @ApiModelProperty("区域名字")
    String areaName;

    @ApiModelProperty("区域编码")
    String areaCode;

    String bchName;

    String bchCode;

    @ApiModelProperty("片区名字")
    String districtName;

    @ApiModelProperty("片区编码")
    String districtCode;
}
