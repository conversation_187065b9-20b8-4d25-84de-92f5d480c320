package com.cfpamf.ms.insur.report.util;


import com.cfpamf.ms.insur.report.feign.resp.vo.ModuleVO;
import com.cfpamf.ms.insur.report.feign.resp.vo.UserDetailVO;
import com.cfpamf.ms.insur.report.service.BmsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 当前用户ThreadLocal
 **/
public class  ThreadUserUtil {

    /**
     * 当前登录用户权限
     */
    public final static ThreadLocal<List<ModuleVO>> userModulesTL = new ThreadLocal<List<ModuleVO>>() {
        @Override
        protected synchronized List<ModuleVO> initialValue() {
           return SpringFactoryUtil.getBean(BmsService.class).getContextUserPermissions();
            //return null;
        }
    };

    /**
     * 当前等路用户信息
     */
    public final static ThreadLocal<UserDetailVO> userDetailTL = new ThreadLocal<UserDetailVO>() {
        @Override
        protected synchronized UserDetailVO initialValue() {
            UserDetailVO contextUserDetail = SpringFactoryUtil.getBean(BmsService.class).getContextUserDetail();

            if (contextUserDetail != null) {
                //过滤非保险的角色
                List<UserDetailVO.UserRoleVO> roleList = contextUserDetail.getRoleList();
                if (!CollectionUtils.isEmpty(roleList)) {
                    contextUserDetail.setRoleList(roleList.stream().filter(role -> StringUtils.startsWith(role.getRoleName(), "保险")).collect(Collectors.toList()));
                }
            }
            return contextUserDetail;
        }
    };
}
