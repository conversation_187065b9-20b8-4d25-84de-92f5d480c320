package com.cfpamf.ms.insur.report.service;

import com.cfpamf.ms.insur.report.dao.safes.OrgPicExtraMapper;
import com.cfpamf.ms.insur.report.pojo.vo.OrgPicExtraVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserService {

    @Autowired
    private OrgPicExtraMapper picMapper;
    /**
     * 通过工号查询机构创新业务对接人
     *
     * @param userId
     * @return
     */
    public OrgPicExtraVO getOrgPicExtraByUserIdAndOrg(String userId, String orgCode) {
        return picMapper.getOrgPicExtraByUserIdAndOrg(userId, orgCode);
    }
}
