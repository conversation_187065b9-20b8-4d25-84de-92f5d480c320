package com.cfpamf.ms.insur.report.feign.resp.vo;

import lombok.Data;

@Data
public class UserVo {
    private String acctId; // 员工虚拟账户id

    private String birthday; // 员工生日

    private String branchCode; // 员工所属分支机构

    private String branchName; // 员工所属分支机构名称

    private String custId; // 员工客户id

    private String deviceId; // 设备编号

    private String headQuarters; // 是否总部员工 Y-是 N-否

    private String jobCde; // 职务编号

    private String jobName; // 职务名称

    private String joinDate; // 员工入职日期

    private LawsuitAgentVo lawsuitAgentVo; // 法律代理人信息

    private String orgCode; // 实际的部门编号

    private String userCode; // 员工工号

    private UserConfigVo userConfigVo; // 用户配置信息

    private String userIdNo; // 员工身份证号

    private String userName; // 员工姓名

    private String userRetTel; // 员工回访电话

    private String userSts; // 状态 A-可用 I或其它-为不可用

    private String userTel; // 员工手机
}
