package com.cfpamf.ms.insur.report.aspect;


import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.annotation.AdminAutoAuthQuery;
import com.cfpamf.ms.insur.report.enums.EnumBmsRole;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.feign.resp.vo.UserDetailVO;
import com.cfpamf.ms.insur.report.pojo.query.DataPermissionQuery;
import com.cfpamf.ms.insur.report.pojo.vo.OrgPicExtraVO;
import com.cfpamf.ms.insur.report.service.OrgService;
import com.cfpamf.ms.insur.report.service.UserService;
import com.cfpamf.ms.insur.report.util.PermissionUtil;
import com.cfpamf.ms.insur.report.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.report.util.ThreadUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 微信数据权限查询基类构建[这里只做了一件事 就是封装基本的查询条件]
 * Create By zhengjing on 2020/2/17 15:04
 */
@Aspect
@Component
@Slf4j
public class AdminDataAuthQueryAspect {


    @Autowired
    private PermissionUtil permissionUtil;
    @Autowired
    private UserService userService;
    @Autowired
    private OrgService orgService;



    @Pointcut("@annotation(com.cfpamf.ms.insur.report.annotation.AdminAutoAuthQuery)")
    public void query() {
        throw new UnsupportedOperationException();
    }

    @Around("query()")
    public Object around(final ProceedingJoinPoint joinPoint) throws Throwable {

        UserDetailVO userDetailVO = ThreadUserUtil.userDetailTL.get();
        try {
            init(joinPoint, userDetailVO);
            return joinPoint.proceed();
        } finally {
            ThreadUserUtil.userDetailTL.remove();
        }

    }

    private void init(final ProceedingJoinPoint joinPoint, UserDetailVO userDetailVO) {
        Object[] args = joinPoint.getArgs();

        Signature signature = joinPoint.getSignature();
        if (signature instanceof MethodSignature) {
            Method method = ((MethodSignature) signature).getMethod();
            AdminAutoAuthQuery annotation = method.getAnnotation(AdminAutoAuthQuery.class);
            if (annotation == null) {
                return;
            }
            //List<UserDetailVO.UserRoleVO> roleList = userDetailVO.getRoleList();
            List<UserDetailVO.UserRoleVO> roleList = userDetailVO.getRoleList()
                    .stream().filter(r -> r.getRoleName() != null && r.getRoleName().startsWith("保险"))
                    .collect(Collectors.toList());
            if (roleList.isEmpty()) {
                throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
            }
            userDetailVO.setRoleList(roleList);
            //如果当前角色拥有所有权限直接return
            if (annotation.allRole().length > 0) {

                List<String> list = Stream.of(annotation.allRole()).map(EnumBmsRole::getCode).collect(Collectors.toList());
                if (roleList.stream().anyMatch(role -> list.contains(role.getRoleCode()))) {
                    return;
                }
            }

            //机构管理员
            if (annotation.orgAdminArea()) {
                OrgPicExtraVO picExtraVO =
                        userService.getOrgPicExtraByUserIdAndOrg(userDetailVO.getJobNumber(),
                                userDetailVO.getOrgCode());

                for (Object arg : args) {
                    if (arg instanceof DataPermissionQuery) {
                        DataPermissionQuery tmp = (DataPermissionQuery) arg;
                        if (picExtraVO != null) {
                            //tmp.setRegionName(picExtraVO.getRegionName());
                            //tmp.setOrgName(picExtraVO.getOrgName());
                            tmp.setOrgCode(picExtraVO.getOrgCode());
                        }//设置是否创新业务对接人
                        tmp.setPicRole(picExtraVO != null);
                    }
                }
                if (picExtraVO != null) {
                    return;
                }
            }

            buildDataPermission(args,userDetailVO);

            Class<? extends SelfAuthAbstractHandler>[] classes = annotation.selfAuth();
            if(classes!=null && classes.length>=1){
                for(int i=0;i<classes.length;i++) {
                    SpringFactoryUtil.getBean(classes[i]).execute(args);
                }
            }
        }

    }

    private void buildDataPermission(Object[] args, UserDetailVO contextUser){
        for (Object arg : args) {
            if (arg instanceof DataPermissionQuery) {
                DataPermissionQuery query = (DataPermissionQuery) arg;
                permissionUtil.buildDataPermission(query,contextUser);
            }
        }
    }
}
