package com.cfpamf.ms.insur.report.dao.safepg;

import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerPromotionSummaryDTO;
import com.cfpamf.ms.insur.report.pojo.query.AssistantManagerPromotionQuery;
import com.cfpamf.ms.insur.report.pojo.query.AssistantSalerPromotionQuery;
import com.cfpamf.ms.insur.report.pojo.query.ManagerRankPromotionQuery;
import com.cfpamf.ms.insur.report.pojo.query.SalerRankPromotionQuery;
import com.cfpamf.ms.insur.report.pojo.vo.promotion.AssistantManagerPromotionRankVO;
import com.cfpamf.ms.insur.report.pojo.vo.promotion.AssistantSalerPromotionRankVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AdsInsuranceEmpOnlinePromotionDfpMapper {

    AssistantSalerPromotionSummaryDTO empSummary(String pt, String empId);

    List<AssistantSalerPromotionSummaryDTO> empRankList(SalerRankPromotionQuery salerRankPromotionQuery);

    Integer empRankCount(AssistantSalerPromotionQuery assistantSalerPromotionSummaryQuery);

    List<AssistantSalerPromotionSummaryDTO> areaRankList(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);

    List<AssistantSalerPromotionSummaryDTO> districtRankList(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);

    List<AssistantSalerPromotionSummaryDTO> bchRankList(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);

    Integer areaRankCount(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);

    Integer districtCount(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);

    Integer bchRankCount(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);

    List<AssistantSalerPromotionSummaryDTO> empNewRankList(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);

    Integer empNewRankCount(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);

    List<AssistantSalerPromotionSummaryDTO> bchRankListForLevel(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery);

    Integer mineEmpRankCount(SalerRankPromotionQuery salerRankPromotionQuery);

    AssistantSalerPromotionSummaryDTO manageNationSummary(String pt);

    AssistantSalerPromotionSummaryDTO manageAreaSummary(String pt, String areaCode);

    AssistantSalerPromotionSummaryDTO manageDistrictSummary(String pt, String areaCode, List<String> districtList);

    AssistantSalerPromotionSummaryDTO manageBchSummary(String pt, String areaCode, List<String> districtList, String bchCode);

    Integer mineManageAreaRankCount(ManagerRankPromotionQuery managerRankPromotionQuery);

    Integer mineManageBckRankCount(ManagerRankPromotionQuery managerRankPromotionQuery);

    AssistantSalerPromotionSummaryDTO empTopOne(String pt, String dataType);

    List<AssistantSalerPromotionSummaryDTO> empSummaryList(String pt, List<String> empIdList, String bchCode);
}
