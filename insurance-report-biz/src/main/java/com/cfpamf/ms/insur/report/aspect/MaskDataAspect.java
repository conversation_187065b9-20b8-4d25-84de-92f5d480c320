package com.cfpamf.ms.insur.report.aspect;

import com.cfpamf.ms.insur.report.util.DataMaskUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Objects;

/**
 * 微信数据权限查询基类构建[这里只做了一件事 就是封装基本的查询条件]
 * Create By zhengjing on 2020/2/17 15:04
 */
@Aspect
@Component
@Slf4j
public class MaskDataAspect {

    @Pointcut("@annotation(com.cfpamf.ms.insur.report.annotation.MaskMethod)")
    public void query() {
        throw new UnsupportedOperationException();
    }

    @AfterReturning(value = "query()", returning = "result")
    public void around(JoinPoint point, Object result) throws Throwable {
        if (Objects.nonNull(result)) {
            if (result instanceof Collection) {
                DataMaskUtil.maskCollection((Collection) result);
            } else if (result instanceof PageInfo) {
                DataMaskUtil.maskCollection(((PageInfo) result).getList());
            } else {
                DataMaskUtil.maskObject(result);
            }
        }
    }
}
