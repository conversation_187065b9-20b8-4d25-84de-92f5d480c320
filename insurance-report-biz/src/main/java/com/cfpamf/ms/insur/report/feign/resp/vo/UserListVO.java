package com.cfpamf.ms.insur.report.feign.resp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 账号信息数据对象
 */
@Data
public class UserListVO {

    @ApiModelProperty("用户Id")
    private Integer userId;

    @ApiModelProperty("员工Id")
    private Integer employeeId;

    @ApiModelProperty("账号/手机号")
    private String userAccount;

    @ApiModelProperty("密码等级（极弱：0；弱：1；中等：2；强：3）")
    private Integer passwordLevel;

    @ApiModelProperty(value = "帐号状态（1-正常，2-未启用，3-锁定，4-注销）")
    private Integer userStatus;

    @ApiModelProperty("进入系统时间")
    private Date entrySystemDate;

    @ApiModelProperty("用户使用手机APP登录的设备Id")
    private String deviceId;

    @ApiModelProperty("用户描述")
    private String description;

    @ApiModelProperty("用户信息最后修改时间")
    private Date updateTime;

    @ApiModelProperty("姓名")
    private String employeeName;

    @ApiModelProperty("邮箱地址")
    private String userEmail;

    @ApiModelProperty("用户性别")
    private int userSex;

    @ApiModelProperty("入职时间")
    private Date entryDate;

    @ApiModelProperty("批次号（员工信息同步批次号）")
    private String batchNo;

    @ApiModelProperty("办公电话")
    private String officeTel;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("出生日期")
    private Date birthday;

    @ApiModelProperty(value = "雇佣类型(0:正式员工、1:外部员工、2:实习生)")
    private Integer employeeType;

    @ApiModelProperty(value = "人员状态（1.待入职 2.试用 3.正式 4.调出 5.待调入 6.退休 8.离职 12.非正式）")
    private Integer employeeStatus;

    @ApiModelProperty(value = "北森用户Id")
    private Integer hrUserId;

    @ApiModelProperty("工号")
    private String jobNumber;

    @ApiModelProperty("兼职工号")
    private String jianzhiJobNumber;

    @ApiModelProperty(value = "HR任职记录code,任职唯一识别编码")
    private String jobCode;

    @ApiModelProperty(value = "任职类型（0:正职、1:兼职）")
    private Integer serviceType;

    @ApiModelProperty(value = "行政上级/直线经理Id")
    private String userAdminId;

    @ApiModelProperty(value = "行政上级/直线经理姓名")
    private String userAdminName;

    @ApiModelProperty(value = "业务上级/虚线经理Id")
    private String userMasterId;

    @ApiModelProperty(value = "业务上级/虚线经理姓名")
    private String userMasterName;

    @ApiModelProperty("所属岗位Id")
    private Integer postId;

    @ApiModelProperty("所属岗位(职务)")
    private String postName;

    @ApiModelProperty("北森岗位Id")
    private Integer hrPostId;

    @ApiModelProperty("机构Id")
    private Integer orgId;

    @ApiModelProperty("机构编码")
    private String orgCode;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("北森机构Id")
    private Integer hrOrgId;

    @ApiModelProperty("树结构路径")
    private String hrOrgTreePath;

    @ApiModelProperty("所属机构编号--区域办公室属性")
    private String areaOrgCode;

    @ApiModelProperty("灰度用户")
    private Integer gray;
}
