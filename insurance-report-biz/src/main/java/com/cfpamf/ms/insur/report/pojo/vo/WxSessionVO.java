package com.cfpamf.ms.insur.report.pojo.vo;



import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 微信session信息
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxSessionVO extends WxUserVO implements Serializable {
    private static final long serialVersionUID = -373871580643467655L;

    /**
     * 系统（区分公众号）
     */
    @ApiModelProperty(value = "系统（区分公众号）")
    private String system;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    /**
     * 登录token
     */
    @ApiModelProperty(value = "登录token")
    private String authorization;

    /**
     * bms token
     */
    @ApiModelProperty(value = "bms授权码")
    private String bmsToken;

    /**
     * 是否显示推广费
     */
    @ApiModelProperty(value = "是否显示推广费")
    private Boolean showCmsRatio;

    /**
     * 是否显示中户保险入口
     */
    @ApiModelProperty(value = "是否显示中户保险入口")
    private Boolean showCicEntry;

    /**
     * 流程审批
     */
    @ApiModelProperty(value = "流程审批")
    private Boolean showProcessApproval;

    /**
     * 代理人类别
     */
    @ApiModelProperty(value = "代理人类别")
    private Integer agentType;

    /**
     * 微信绑定所有用户
     */
    @ApiModelProperty(value = "微信绑定所有用户")
    private List<WxUserVO> wxUsers;

    /**
     * bms systemId
     */
    @ApiModelProperty(value = "bms systemId")
    private Integer bmsSystemId;

    /**
     * bms roleCode
     */
    @ApiModelProperty(value = "bms roleCode")
    private String bmsRoleCode;

    /**
     * 判断是否员工
     *
     * @return
     */
    @JsonIgnore
    public boolean isBindEmployee() {
        return Objects.equals(getUserType(), BaseConstants.USER_TYPE_EMPLOYEE);
    }

    /**
     * 判断是否代理人
     *
     * @return
     */
    @JsonIgnore
    public boolean isBindAgent() {
        return Objects.equals(getUserType(), BaseConstants.USER_TYPE_AGENT);
    }

    /**
     * 判断是否普通用户
     *
     * @return
     */
    @JsonIgnore
    public boolean isBindWeixin() {
        return Objects.equals(getUserType(), BaseConstants.USER_TYPE_WEIXIN);
    }

    /**
     * 获取session 用户唯一编号
     * 如果是公司内部员工 返回员工号
     * 如果是代理人 返回代理人编号
     * 如果是微信未绑定用户 返回wxOpenid
     *
     * @return
     */
    public String getRoleUniqueId() {
        String roleUniqueId;
        if (isBindEmployee()) {
            roleUniqueId = getUserId();
        } else if (isBindAgent()) {
            roleUniqueId = String.valueOf(getAgentId());
        } else {
            roleUniqueId = getWxOpenId();
        }
        return roleUniqueId;
    }

    /**
     * 如果是员工 返回userId ，
     * 否则返回openId
     *
     * @return
     */
    @JsonIgnore
    public String getContextUser() {
        if (isBindEmployee()) {
            return getUserId();
        } else if (isBindAgent()) {
            return getAgentId().toString();
        }
        return getWxOpenId();
    }
}
