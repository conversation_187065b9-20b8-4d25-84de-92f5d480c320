package com.cfpamf.ms.insur.report.service.promotion.impl;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.constant.OnlinePromotionConstants;
import com.cfpamf.ms.insur.report.dao.safepg.*;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.enums.OnlinePromotionLevelTypeEnum;
import com.cfpamf.ms.insur.report.feign.req.CustVisitCountQueryInput;
import com.cfpamf.ms.insur.report.feign.req.CustVisitListQueryInput;
import com.cfpamf.ms.insur.report.feign.req.GetEffectiveShareInfoQueryDTO;
import com.cfpamf.ms.insur.report.feign.resp.vo.CustVisitCountQueryOutput;
import com.cfpamf.ms.insur.report.feign.resp.vo.MarketingForwardEffectiveInfo;
import com.cfpamf.ms.insur.report.pojo.dto.*;
import com.cfpamf.ms.insur.report.pojo.query.*;
import com.cfpamf.ms.insur.report.pojo.vo.promotion.*;
import com.cfpamf.ms.insur.report.service.OmsBaseService;
import com.cfpamf.ms.insur.report.service.WhaleOrderService;
import com.cfpamf.ms.insur.report.service.promotion.OnlinePromotionService;
import com.cfpamf.ms.insur.report.util.DateUtils;
import com.cfpamf.ms.insur.report.util.NumberUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OnlinePromotionServiceImpl implements OnlinePromotionService {

    @Autowired
    private AdsInsuranceEmpOnlinePromotionDfpMapper empOnlinePromotionDfpMapper;

    private AdsInsuranceAreaOnlinePromotionDfpMapper areaOnlinePromotionDfpMapper;

    private AdsInsuranceBchOnlinePromotionDfpMapper bchOnlinePromotionDfpMapper;

    private AdsInsuranceDistrictOnlinePromotionDfpMapper districtOnlinePromotionDfpMapper;

    @Autowired
    private AssistantAdminMapper assistantAdminMapper;

    @Autowired
    private WhaleOrderService whaleOrderService;

    @Autowired
    private OmsBaseService omsBaseService;


    @Override
    public AssistantSalerPromotionSummaryVO homeEmpSummary(String pt, String epmId) {
        AssistantSalerPromotionSummaryDTO salerPromotionSummaryDTO = empOnlinePromotionDfpMapper.empSummary(pt,epmId);
        AssistantSalerPromotionSummaryVO vo = new AssistantSalerPromotionSummaryVO();
        if(salerPromotionSummaryDTO == null){
            return buildDefaultAssistantSalerPromotionSummaryVO(vo);
        }

        //获取当日分享/访问指标
        List<String> empIdList = Lists.newArrayList(salerPromotionSummaryDTO.getEmpId());
        Map<String,CustVisitCountQueryOutput> custVisitCountQueryOutputMap = getCurrentVisitCountMap(empIdList);
        Map<String, MarketingForwardEffectiveInfo> shareCountMap = getCurrentShareCountMap(empIdList);

        Integer sdShareCnt = getCurrenDayEffectiveShareCount(shareCountMap,salerPromotionSummaryDTO.getEmpId());
        Integer sdVisitCnt = getCurrenDayEffectiveVisitCount(custVisitCountQueryOutputMap, salerPromotionSummaryDTO.getEmpId());
        vo.setSmShareCnt(NumberUtils.wanTransfer(salerPromotionSummaryDTO.getSmShareCnt()+sdShareCnt));
        vo.setSmVisitCnt(NumberUtils.wanTransfer(salerPromotionSummaryDTO.getSmVisitCnt()+sdVisitCnt));
        vo.setSdShareCnt(NumberUtils.wanTransfer(sdShareCnt));
        vo.setSdVisitCnt(NumberUtils.wanTransfer(sdVisitCnt));
        return vo;
    }

    @Override
    public AssistantSalerPromotionSummaryVO indexEmpSummary(String pt, String epmId) {
        //统计当前员工的分享和访客数
        AssistantSalerPromotionSummaryDTO salerPromotionSummaryDTO = empOnlinePromotionDfpMapper.empSummary(pt,epmId);
        //统计月分享和访客TopOne姓名
        AssistantSalerPromotionSummaryVO vo = new AssistantSalerPromotionSummaryVO();
        //统计未跟进访客数
        vo.setUnfollowCnt(NumberUtils.wanTransfer(queryCustNonVisitCount(epmId)));
        AssistantSalerPromotionSummaryDTO topVisitOne = empOnlinePromotionDfpMapper.empTopOne(pt, OnlinePromotionConstants.DATA_TYPE_VISIT);
        vo.setMonthVisitTopEmpName(topVisitOne.getEmpName());
        AssistantSalerPromotionSummaryDTO topShareOne = empOnlinePromotionDfpMapper.empTopOne(pt, OnlinePromotionConstants.DATA_TYPE_SHARE);
        vo.setMonthShareTopName(topShareOne.getEmpName());
        if(salerPromotionSummaryDTO == null){
            return buildDefaultAssistantSalerPromotionSummaryVO(vo);
        }

        List<String> empIdList = Lists.newArrayList(salerPromotionSummaryDTO.getEmpId());
        Map<String,CustVisitCountQueryOutput> custVisitCountQueryOutputMap = getCurrentVisitCountMap(empIdList);
        Map<String, MarketingForwardEffectiveInfo> shareCountMap = getCurrentShareCountMap(empIdList);

        Integer sdShareCnt = getCurrenDayEffectiveShareCount(shareCountMap,salerPromotionSummaryDTO.getEmpId());
        Integer sdVisitCnt = getCurrenDayEffectiveVisitCount(custVisitCountQueryOutputMap, salerPromotionSummaryDTO.getEmpId());
        vo.setSmShareCnt(NumberUtils.wanTransfer(salerPromotionSummaryDTO.getSmShareCnt() + sdShareCnt));
        vo.setSmVisitCnt(NumberUtils.wanTransfer(salerPromotionSummaryDTO.getSmVisitCnt() + sdVisitCnt));
        vo.setSdShareCnt(NumberUtils.wanTransfer(sdShareCnt));
        vo.setSdVisitCnt(NumberUtils.wanTransfer(sdVisitCnt));
        return vo;
    }

    @Override
    public AssistantSalerPromotionRankVO empRankList(AssistantSalerPromotionQuery assistantSalerPromotionSummaryQuery) {
        log.info("empRankList 当前版本时间 assistantSalerPromotionSummaryQuery= {}",JSON.toJSONString(assistantSalerPromotionSummaryQuery));
        String pt = assistantSalerPromotionSummaryQuery.getPt();
        String empId = assistantSalerPromotionSummaryQuery.getEmpId();
        String lastMonthPt = assistantSalerPromotionSummaryQuery.getLastMonthPt();
        log.info("empRankList assistantSalerPromotionSummaryQuery= {}",JSON.toJSONString(assistantSalerPromotionSummaryQuery));
        AssistantSalerPromotionSummaryDTO salerPromotionSummaryDTO = empOnlinePromotionDfpMapper.empSummary(pt,empId);
        AssistantSalerPromotionSummaryDTO lastMonthSalerPromotionSummaryDTO = empOnlinePromotionDfpMapper.empSummary(lastMonthPt,empId);
        if(salerPromotionSummaryDTO == null || lastMonthSalerPromotionSummaryDTO == null){
            log.warn("该员工无报表数据 req= {}", JSON.toJSON(assistantSalerPromotionSummaryQuery));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "数据缺失");
        }
        AssistantSalerPromotionRankVO assistantSalerPromotionRankVO = new AssistantSalerPromotionRankVO();
        assistantSalerPromotionRankVO.setDataType(assistantSalerPromotionSummaryQuery.getDataType());
        assistantSalerPromotionRankVO.setLevelType(assistantSalerPromotionSummaryQuery.getRankType());
        assistantSalerPromotionRankVO.setMineAvatarUrl(salerPromotionSummaryDTO.getEmpWxImgUrl());
        List<AssistantSalerPromotionRankInfo> assistantSalerPromotionRankInfoList = Lists.newArrayList();
        assistantSalerPromotionRankVO.setAssistantSalerPromotionRankInfoList(assistantSalerPromotionRankInfoList);
        AssistantSalerPromotionRankInfo rankInfo = null;
        SalerRankPromotionQuery salerRankPromotionQuery = new SalerRankPromotionQuery();
        BeanUtils.copyProperties(assistantSalerPromotionSummaryQuery,salerRankPromotionQuery);
        salerRankPromotionQuery.setAreaCode(salerPromotionSummaryDTO.getAreaCode());
        List<AssistantSalerPromotionSummaryDTO> assistantSalerPromotionSummaryDTOList = empOnlinePromotionDfpMapper.empRankList(salerRankPromotionQuery);
        List<String> currentMonthEmpList = Lists.newArrayList();
        for(AssistantSalerPromotionSummaryDTO dto : assistantSalerPromotionSummaryDTOList) {
            currentMonthEmpList.add(dto.getEmpId());
            if (Objects.equals(OnlinePromotionLevelTypeEnum.NATION.getLevelType(), assistantSalerPromotionSummaryQuery.getRankType())) {
                if ("share".equals(assistantSalerPromotionSummaryQuery.getDataType())) {
                    assistantSalerPromotionRankVO.setMineSmRank(String.valueOf(salerPromotionSummaryDTO.getSmEffectiveShareCntRank()));
                    rankInfo = buildAssistantSalerPromotionRankInfo(dto.getSmShareCnt(),dto.getEmpId(),dto.getEmpName(),dto.getEmpWxImgUrl(),dto.getSmEffectiveShareCntRank());
                    assistantSalerPromotionRankVO = rankHandle(assistantSalerPromotionRankVO, salerPromotionSummaryDTO.getSmEffectiveShareCntRank(), lastMonthSalerPromotionSummaryDTO.getSmEffectiveShareCntRank());
                } else if ("visit".equals(assistantSalerPromotionSummaryQuery.getDataType())) {
                    assistantSalerPromotionRankVO.setMineSmRank(String.valueOf(salerPromotionSummaryDTO.getSmEffectiveVisitUserCntRank()));
                    rankInfo = buildAssistantSalerPromotionRankInfo(dto.getSmVisitCnt(),dto.getEmpId(),dto.getEmpName(),dto.getEmpWxImgUrl(),dto.getSmEffectiveVisitUserCntRank());
                    assistantSalerPromotionRankVO = rankHandle(assistantSalerPromotionRankVO, salerPromotionSummaryDTO.getSmEffectiveVisitUserCntRank(), lastMonthSalerPromotionSummaryDTO.getSmEffectiveVisitUserCntRank());
                }
            } else if (Objects.equals(OnlinePromotionLevelTypeEnum.AREA.getLevelType(), assistantSalerPromotionSummaryQuery.getRankType())) {
                if ("share".equals(assistantSalerPromotionSummaryQuery.getDataType())) {
                    assistantSalerPromotionRankVO.setMineSmRank(String.valueOf(salerPromotionSummaryDTO.getSmEffectiveShareCntAreaRank()));
                    rankInfo = buildAssistantSalerPromotionRankInfo(dto.getSmShareCnt(),dto.getEmpId(),dto.getEmpName(),dto.getEmpWxImgUrl(),dto.getSmEffectiveShareCntAreaRank());
                    assistantSalerPromotionRankVO = rankHandle(assistantSalerPromotionRankVO, salerPromotionSummaryDTO.getSmEffectiveShareCntAreaRank(), lastMonthSalerPromotionSummaryDTO.getSmEffectiveShareCntAreaRank());
                } else if ("visit".equals(assistantSalerPromotionSummaryQuery.getDataType())) {
                    assistantSalerPromotionRankVO.setMineSmRank(String.valueOf(salerPromotionSummaryDTO.getSmEffectiveVisitUserCntAreaRank()));
                    rankInfo = buildAssistantSalerPromotionRankInfo(dto.getSmVisitCnt(),dto.getEmpId(),dto.getEmpName(),dto.getEmpWxImgUrl(),dto.getSmEffectiveVisitUserCntAreaRank());
                    assistantSalerPromotionRankVO = rankHandle(assistantSalerPromotionRankVO, salerPromotionSummaryDTO.getSmEffectiveVisitUserCntAreaRank(), lastMonthSalerPromotionSummaryDTO.getSmEffectiveVisitUserCntAreaRank());
                }
            }
            assistantSalerPromotionRankInfoList.add(rankInfo);
        }
        //计算当前月排名相较上月排名上升,下降,不变情况
        return comparativeQuarterlyRankHandle(assistantSalerPromotionRankVO,assistantSalerPromotionSummaryQuery,currentMonthEmpList);
    }

    @Override
    public AssistantManagerPromotionSummaryVO managerSummary(String empId,AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery) {
        String pt = assistantManagerPromotionSummaryQuery.getPt();
        AssistantSalerPromotionSummaryDTO salerPromotionSummaryDTO = empOnlinePromotionDfpMapper.empSummary(pt,empId);
        return getAssistantManagerPromotionSummaryVOByDataType(assistantManagerPromotionSummaryQuery, salerPromotionSummaryDTO);
    }

    @Override
    public AssistantManagerPromotionRankVO managerRankList(String empId,AssistantManagerPromotionQuery assistantManagerPromotionQuery) {
        AssistantManagerPromotionRankDTO assistantSalerPromotionRankDTO = new AssistantManagerPromotionRankDTO();
        assistantSalerPromotionRankDTO.setDataType(assistantSalerPromotionRankDTO.getDataType());
        assistantSalerPromotionRankDTO.setLevelType(assistantSalerPromotionRankDTO.getLevelType());

        AssistantManagerPromotionRankVO rankVO = new AssistantManagerPromotionRankVO();
        Integer page = assistantManagerPromotionQuery.getPage();
        page--;
        assistantManagerPromotionQuery.setPage(page*assistantManagerPromotionQuery.getSize());
        List<AssistantManagerPromotionRankDetail> rankDetailList = Lists.newArrayList();
        rankVO.setRankDetailList(rankDetailList);
        List<AssistantSalerPromotionSummaryDTO> summaryDTOList = null;
        AssistantManagerPromotionRankDetail detail = null;

        ManagerRankPromotionQuery managerRankPromotionQuery = new ManagerRankPromotionQuery();
        BeanUtils.copyProperties(assistantManagerPromotionQuery,managerRankPromotionQuery);
        String dataType = assistantManagerPromotionQuery.getDataType();

        if(Objects.equals("nation",assistantManagerPromotionQuery.getLevelType())){
            assistantManagerPromotionQuery.setAreaCode(null);
            assistantManagerPromotionQuery.setPage(-1);
//            count = empOnlinePromotionDfpMapper.areaRankCount(assistantManagerPromotionQuery);
            summaryDTOList = empOnlinePromotionDfpMapper.areaRankList(assistantManagerPromotionQuery);
            for(AssistantSalerPromotionSummaryDTO dto : summaryDTOList){
                detail = new AssistantManagerPromotionRankDetail();
                detail.setName(dto.getName());
                detail.setSmCnt(getSmCnt(dataType,dto));
                detail.setSmAvg("share".equals(dataType)?dto.getSmShareCntAvg().toString():dto.getSmVisitCntAvg().toString());
                rankDetailList.add(detail);
            }
        }else if(Objects.equals("area",assistantManagerPromotionQuery.getLevelType())){
            assistantManagerPromotionQuery.setAreaCode(null);
            summaryDTOList = empOnlinePromotionDfpMapper.areaRankList(assistantManagerPromotionQuery);
            for(AssistantSalerPromotionSummaryDTO dto : summaryDTOList){
                detail = new AssistantManagerPromotionRankDetail();
                detail.setName(dto.getName());
                detail.setSmCnt(getSmCnt(dataType,dto));
                detail.setSmAvg("share".equals(dataType)?dto.getSmShareCntAvg().toString():dto.getSmVisitCntAvg().toString());
                rankDetailList.add(detail);
            }
        }else if(Objects.equals("district",assistantManagerPromotionQuery.getLevelType())){
            assistantManagerPromotionQuery.setAreaCode(null);
            summaryDTOList = empOnlinePromotionDfpMapper.areaRankList(assistantManagerPromotionQuery);
            for(AssistantSalerPromotionSummaryDTO dto : summaryDTOList){
                detail = new AssistantManagerPromotionRankDetail();
                detail.setName(dto.getName());
                detail.setSmCnt(getSmCnt(dataType,dto));
                detail.setSmAvg("share".equals(dataType)?dto.getSmShareCntAvg().toString():dto.getSmVisitCntAvg().toString());
                rankDetailList.add(detail);
            }
        }else if(Objects.equals(OnlinePromotionLevelTypeEnum.BCH.getLevelType(),assistantManagerPromotionQuery.getLevelType())){
            if("nation".equals(assistantManagerPromotionQuery.getRankType())){
                assistantManagerPromotionQuery.setAreaCode(null);
                assistantManagerPromotionQuery.setDistrictCodeList(null);
                assistantManagerPromotionQuery.setBchCode(null);
                summaryDTOList = empOnlinePromotionDfpMapper.bchRankList(assistantManagerPromotionQuery);
            }else if("area".equals(assistantManagerPromotionQuery.getRankType())){
                assistantManagerPromotionQuery.setDistrictCodeList(null);
                assistantManagerPromotionQuery.setBchCode(null);
                managerRankPromotionQuery.setAreaCode(assistantManagerPromotionQuery.getAreaCode());
                summaryDTOList = empOnlinePromotionDfpMapper.bchRankList(assistantManagerPromotionQuery);
            }
            for(AssistantSalerPromotionSummaryDTO dto : summaryDTOList){
                detail = new AssistantManagerPromotionRankDetail();
                detail.setName(dto.getName());
                detail.setSmCnt(getSmCnt(dataType,dto));
                detail.setSmAvg("share".equals(dataType)?dto.getSmShareCntAvg().toString():dto.getSmVisitCntAvg().toString());
                rankDetailList.add(detail);
            }
        }
        String mineRank = null;
        if(
                Objects.equals(OnlinePromotionLevelTypeEnum.AREA.getLevelType(),assistantManagerPromotionQuery.getLevelType())
                || Objects.equals(OnlinePromotionLevelTypeEnum.DISTRICT.getLevelType(),assistantManagerPromotionQuery.getLevelType())
        ){
            AssistantSalerPromotionSummaryDTO assistantManagerPromotionSummaryDTO = empOnlinePromotionDfpMapper.manageAreaSummary(assistantManagerPromotionQuery.getPt(),managerRankPromotionQuery.getAreaCode());
            if(Objects.isNull(assistantManagerPromotionSummaryDTO)){
                log.warn("分支数据缺失 req= {}", JSON.toJSON(assistantManagerPromotionSummaryDTO));
                mineRank = null;
                rankVO.setCurrentName(null);
            }else{
                rankVO.setCurrentName(assistantManagerPromotionSummaryDTO.getAreaName());
                managerRankPromotionQuery.setSmVisitCnt(assistantManagerPromotionSummaryDTO.getSmVisitCnt()+1);
                managerRankPromotionQuery.setSmShareCnt(assistantManagerPromotionSummaryDTO.getSmShareCnt()+1);
                if(!Objects.equals(OnlinePromotionLevelTypeEnum.NATION.getLevelType(),assistantManagerPromotionQuery.getLevelType())){
                    mineRank = mineManageAreaRankCount(managerRankPromotionQuery);
                }
            }
        }else if(Objects.equals(OnlinePromotionLevelTypeEnum.BCH.getLevelType(),assistantManagerPromotionQuery.getLevelType())){
            AssistantSalerPromotionSummaryDTO assistantManagerPromotionSummaryDTO = empOnlinePromotionDfpMapper.manageBchSummary(assistantManagerPromotionQuery.getPt(),managerRankPromotionQuery.getAreaCode(),managerRankPromotionQuery.getDistrictCodeList(),managerRankPromotionQuery.getBchCode());
            if(Objects.isNull(assistantManagerPromotionSummaryDTO)){
                log.warn("分支数据缺失 req= {}", JSON.toJSON(assistantManagerPromotionSummaryDTO));
                mineRank = null;
                rankVO.setCurrentName(null);
            }else{
                rankVO.setCurrentName(assistantManagerPromotionSummaryDTO.getBchName());
                managerRankPromotionQuery.setSmVisitCnt(assistantManagerPromotionSummaryDTO.getSmVisitCnt()+1);
                managerRankPromotionQuery.setSmShareCnt(assistantManagerPromotionSummaryDTO.getSmShareCnt()+1);
                mineRank = mineManageBckRankCount(managerRankPromotionQuery);
            }
        }
        rankVO.setCurrentRank(mineRank);
        //排名再处理
        rankVO = rankHandle(rankVO);
        return rankVO;
    }

    @Override
    public AssistantManagerPromotionLevelVO nextLevelDetailList(AssistantManagerPromotionQuery assistantManagerPromotionQuery) {
        AssistantManagerPromotionLevelVO levelVO = new AssistantManagerPromotionLevelVO();
        List<AssistantManagerPromotionLevelDetail> levelDetailList = Lists.newArrayList();
        levelVO.setLevelDetailList(levelDetailList);
        Integer total = null;
        Integer page = assistantManagerPromotionQuery.getPage();
        page--;
        assistantManagerPromotionQuery.setPage(page*assistantManagerPromotionQuery.getSize());
        List<AssistantSalerPromotionSummaryDTO> summaryDTOList = null;
        AssistantManagerPromotionLevelDetail levelDetail = null;
        Integer rank = 1;
        List<String> empIdList = null;
        if(Objects.equals("area",assistantManagerPromotionQuery.getLevelType())){
            total = empOnlinePromotionDfpMapper.bchRankCount(assistantManagerPromotionQuery);
            if(Objects.equals(0,total)){
                //查询结果集为0,返回默认数据
                return buildEmptyNextLevelDetailList(levelVO);
            }
            summaryDTOList = empOnlinePromotionDfpMapper.bchRankListForLevel(assistantManagerPromotionQuery);
            for(AssistantSalerPromotionSummaryDTO dto : summaryDTOList){
                levelDetail = new AssistantManagerPromotionLevelDetail(rank);
                levelDetail.setName(dto.getName());
                levelDetail.setSmVisitCnt(NumberUtils.wanTransfer(dto.getSmVisitCnt()));
                levelDetail.setSmShareCnt(NumberUtils.wanTransfer(dto.getSmShareCnt()));
                levelDetail.setCdShareCnt(NumberUtils.wanTransfer(dto.getCdShareCnt()));
                levelDetail.setCdVisitCnt(NumberUtils.wanTransfer(dto.getCdVisitCnt()));
                levelDetailList.add(levelDetail);
                rank++;
            }
        }else if(Objects.equals("district",assistantManagerPromotionQuery.getLevelType())){
            total = empOnlinePromotionDfpMapper.bchRankCount(assistantManagerPromotionQuery);
            if(Objects.equals(0,total)){
                //查询结果集为0,返回默认数据
                return buildEmptyNextLevelDetailList(levelVO);
            }
            summaryDTOList = empOnlinePromotionDfpMapper.bchRankListForLevel(assistantManagerPromotionQuery);
            for(AssistantSalerPromotionSummaryDTO dto : summaryDTOList){
                levelDetail = new AssistantManagerPromotionLevelDetail(rank);
                levelDetail.setName(dto.getName());
                levelDetail.setSmVisitCnt(NumberUtils.wanTransfer(dto.getSmVisitCnt()));
                levelDetail.setSmShareCnt(NumberUtils.wanTransfer(dto.getSmShareCnt()));
                levelDetail.setCdShareCnt(NumberUtils.wanTransfer(dto.getCdShareCnt()));
                levelDetail.setCdVisitCnt(NumberUtils.wanTransfer(dto.getCdVisitCnt()));
                levelDetailList.add(levelDetail);
                rank++;
            }
        }else if(Objects.equals("bch",assistantManagerPromotionQuery.getLevelType())){
            total = empOnlinePromotionDfpMapper.empNewRankCount(assistantManagerPromotionQuery);
            if(Objects.equals(0,total)){
                //查询结果集为0,返回默认数据
                return buildEmptyNextLevelDetailList(levelVO);
            }
            summaryDTOList = empOnlinePromotionDfpMapper.empNewRankList(assistantManagerPromotionQuery);
            empIdList = summaryDTOList.stream().map(AssistantSalerPromotionSummaryDTO::getEmpId).collect(Collectors.toList());
            Map<String,CustVisitCountQueryOutput> custVisitCountQueryOutputMap = getCurrentVisitCountMap(empIdList);
            Map<String, MarketingForwardEffectiveInfo> shareCountMap = getCurrentShareCountMap(empIdList);
            for(AssistantSalerPromotionSummaryDTO dto : summaryDTOList){
                levelDetail = new AssistantManagerPromotionLevelDetail(rank);
                levelDetail.setName(dto.getName());
                levelDetail.setSmVisitCnt(NumberUtils.wanTransfer(dto.getSmVisitCnt()));
                levelDetail.setSdVisitCnt(NumberUtils.wanTransfer(getCurrenDayEffectiveVisitCount(custVisitCountQueryOutputMap,dto.getEmpId())));
                levelDetail.setSmShareCnt(NumberUtils.wanTransfer(dto.getSmShareCnt()));
                levelDetail.setSdShareCnt(NumberUtils.wanTransfer(getCurrenDayEffectiveShareCount(shareCountMap,dto.getEmpId())));
                levelDetailList.add(levelDetail);
                rank++;
            }
        }
        levelVO.setTotal(total);
        return levelVO;
    }

    private AssistantSalerPromotionSummaryVO buildDefaultAssistantSalerPromotionSummaryVO(AssistantSalerPromotionSummaryVO vo){
        vo.setSmVisitCnt("0");
        vo.setSdVisitCnt("0");
        vo.setSmShareCnt("0");
        vo.setSdShareCnt("0");
        return vo;
    }

    private String salerMineRankHandle(AssistantSalerPromotionQuery assistantSalerPromotionSummaryQuery, AssistantSalerPromotionSummaryDTO salerPromotionSummaryDTO,SalerRankPromotionQuery salerRankPromotionQuery){
        Integer mineRank = empOnlinePromotionDfpMapper.mineEmpRankCount(salerRankPromotionQuery);
        if(Objects.isNull(mineRank) || Objects.equals(0,mineRank)){
            return "1";
        }
        return String.valueOf(mineRank+1);
    }

    private String getSmCnt(String dataType,AssistantSalerPromotionSummaryDTO dto){
        if("share".equals(dataType)){
            return NumberUtils.wanTransfer(dto.getSmShareCnt());
        }else if("visit".equals(dataType)){
            return NumberUtils.wanTransfer(dto.getSmVisitCnt());
        }else{
            log.warn("不支持的数据类型 dataType= {}", JSON.toJSON(dataType));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "不支持的数据类型");
        }
    }

    /**
     * 计算管理端全国/区域/片区/分支当月和当日的分享数和访客数
     * @param assistantManagerPromotionSummaryQuery
     * @param salerPromotionSummaryDTO
     * @return
     */
    private AssistantManagerPromotionSummaryVO getAssistantManagerPromotionSummaryVOByDataType(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery, AssistantSalerPromotionSummaryDTO salerPromotionSummaryDTO){
        AssistantSalerPromotionSummaryDTO assistantManagerPromotionSummaryDTO = null;
        AssistantManagerPromotionSummaryVO summaryVO = new AssistantManagerPromotionSummaryVO();
        if(Objects.equals(OnlinePromotionLevelTypeEnum.NATION.getLevelType(),assistantManagerPromotionSummaryQuery.getLevelType())){
            //计算全国T+1当月分享数和访客数
            assistantManagerPromotionSummaryDTO = empOnlinePromotionDfpMapper.manageNationSummary(assistantManagerPromotionSummaryQuery.getPt());
        }else if(Objects.equals(OnlinePromotionLevelTypeEnum.AREA.getLevelType(),assistantManagerPromotionSummaryQuery.getLevelType())){
            //计算区域T+1当月分享数和访客数
            assistantManagerPromotionSummaryDTO = empOnlinePromotionDfpMapper.manageAreaSummary(assistantManagerPromotionSummaryQuery.getPt(),assistantManagerPromotionSummaryQuery.getAreaCode());
        }else if(Objects.equals(OnlinePromotionLevelTypeEnum.DISTRICT.getLevelType(),assistantManagerPromotionSummaryQuery.getLevelType())){
            //计算片区T+1当月分享数和访客数
            assistantManagerPromotionSummaryDTO = empOnlinePromotionDfpMapper.manageDistrictSummary(assistantManagerPromotionSummaryQuery.getPt(),assistantManagerPromotionSummaryQuery.getAreaCode(),assistantManagerPromotionSummaryQuery.getDistrictCodeList());
        }else if(Objects.equals(OnlinePromotionLevelTypeEnum.BCH.getLevelType(),assistantManagerPromotionSummaryQuery.getLevelType())){
            //计算分支T+1当月分享数和访客数
            assistantManagerPromotionSummaryDTO = empOnlinePromotionDfpMapper.manageBchSummary(assistantManagerPromotionSummaryQuery.getPt(),assistantManagerPromotionSummaryQuery.getAreaCode(),assistantManagerPromotionSummaryQuery.getDistrictCodeList(),assistantManagerPromotionSummaryQuery.getBchCode());
            //获取当前分支所有员工并计算当日访客和分享数量
            appendCurrentDayData(assistantManagerPromotionSummaryQuery,summaryVO);
        }else{
            log.warn("不支持的数据类型 dataType= {}", JSON.toJSON(assistantManagerPromotionSummaryQuery.getDataType()));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "不支持的数据类型");
        }
        if(Objects.isNull(assistantManagerPromotionSummaryDTO)){
            return getDefaultAssistantManagerPromotionSummaryVO();
        }
        summaryVO.setSmVisitCnt(NumberUtils.wanTransfer(assistantManagerPromotionSummaryDTO.getSmVisitCnt()));
        summaryVO.setSmShareCnt(NumberUtils.wanTransfer(assistantManagerPromotionSummaryDTO.getSmShareCnt()));
        summaryVO.setCdShareCnt(NumberUtils.wanTransfer(assistantManagerPromotionSummaryDTO.getCdShareCnt()));
        summaryVO.setCdVisitCnt(NumberUtils.wanTransfer(assistantManagerPromotionSummaryDTO.getCdVisitCnt()));
        return summaryVO;
    }

    private String mineManageAreaRankCount(ManagerRankPromotionQuery managerRankPromotionQuery){
        Integer mineRank = empOnlinePromotionDfpMapper.mineManageAreaRankCount(managerRankPromotionQuery);
        if(Objects.isNull(mineRank) || Objects.equals(0,mineRank)){
            return "1";
        }
        return String.valueOf(mineRank+1);
    }

    private String mineManageBckRankCount(ManagerRankPromotionQuery managerRankPromotionQuery){
        Integer mineRank = empOnlinePromotionDfpMapper.mineManageBckRankCount(managerRankPromotionQuery);
        if(Objects.isNull(mineRank) || Objects.equals(0,mineRank)){
            return "1";
        }
        return String.valueOf(mineRank+1);
    }

    private AssistantManagerPromotionRankVO rankHandle(AssistantManagerPromotionRankVO vo){
        if(CollectionUtils.isEmpty(vo.getRankDetailList())){
            return vo;
        }
        List<AssistantManagerPromotionRankDetail> rankDetailList = vo.getRankDetailList();
        AssistantManagerPromotionRankDetail last = null;
        AssistantManagerPromotionRankDetail detail = null;
        for(int i = 0; i < rankDetailList.size(); i++){
            detail = rankDetailList.get(i);
            if(last != null){
                if(Objects.equals(last.getSmCnt(),detail.getSmCnt())){
                    detail.setRank(last.getRank());
                }else{
                    detail.setRank(i+1);
                }
            }else{
                detail.setRank(i+1);
            }
            last = detail;
        }
        return vo;
    }

    private AssistantSalerPromotionRankVO rankHandle(AssistantSalerPromotionRankVO vo){
        if(CollectionUtils.isEmpty(vo.getAssistantSalerPromotionRankInfoList())){
            return vo;
        }
        List<AssistantSalerPromotionRankInfo> rankDetailList = vo.getAssistantSalerPromotionRankInfoList();
        AssistantSalerPromotionRankInfo last = null;
        AssistantSalerPromotionRankInfo detail = null;
        for(int i = 0; i < rankDetailList.size(); i++){
            detail = rankDetailList.get(i);
            if(last != null){
                if(Objects.equals(last.getSmCnt(),detail.getSmCnt())){
                    detail.setRank(last.getRank());
                }else{
                    detail.setRank(i+1);
                }
            }else{
                detail.setRank(i+1);
            }
            last = detail;
        }
        return vo;
    }

    private AssistantManagerPromotionSummaryVO getDefaultAssistantManagerPromotionSummaryVO(){
        AssistantManagerPromotionSummaryVO vo = new AssistantManagerPromotionSummaryVO();
        vo.setSmShareCnt("0");
        vo.setSmVisitCnt("0");
        vo.setSdShareCnt("0");
        vo.setSdVisitCnt("0");
        vo.setSmTransformPolicyCnt("0");
        vo.setCdVisitCnt("0");
        vo.setCdShareCnt("0");
        return vo;
    }

    /**
     * 获取排名: 当前排名-上月排名
     * @param currentRank
     * @param lastRank
     * @return
     */
    private String getRank(Integer currentRank, Integer lastRank){
        return Math.abs(currentRank - lastRank)+"";
    }

    /**
     * 获取排名: 当前排名-上月排名
     * @param currentRank
     * @param lastRank
     * @return
     */
    private Integer getRankInteger(Integer currentRank, Integer lastRank){
        return Math.abs(currentRank - lastRank);
    }

    /**
     * 计算排名状态
     * @param currentRank
     * @param lastRank
     * @return
     */
    private Integer getRankStatus(Integer currentRank, Integer lastRank){
        if(currentRank-lastRank > 0){
            return OnlinePromotionConstants.RANK_STATUS_DOWN;
        }else if(currentRank-lastRank < 0){
            return OnlinePromotionConstants.RANK_STATUS_UP;
        }else{
            return OnlinePromotionConstants.RANK_STATUS_NO_UP_NO_DOWN;
        }
    }

    /**
     * 计算当前月排名相较上月排名上升,下降,不变情况
     * @param assistantSalerPromotionRankVO
     * @param assistantSalerPromotionSummaryQuery
     * @param currentMonthEmpList
     * @return
     */
    private AssistantSalerPromotionRankVO comparativeQuarterlyRankHandle(AssistantSalerPromotionRankVO assistantSalerPromotionRankVO,AssistantSalerPromotionQuery assistantSalerPromotionSummaryQuery,List<String> currentMonthEmpList){
        List<AssistantSalerPromotionRankInfo> assistantSalerPromotionRankInfoList = assistantSalerPromotionRankVO.getAssistantSalerPromotionRankInfoList();
        List<AssistantSalerPromotionSummaryDTO> assistantSalerPromotionSummaryDTOList = empOnlinePromotionDfpMapper.empSummaryList(assistantSalerPromotionSummaryQuery.getLastMonthPt(),currentMonthEmpList,null);
        log.info("assistantSalerPromotionSummaryDTOList= {}",JSON.toJSONString(assistantSalerPromotionSummaryDTOList));
        Map<String,AssistantSalerPromotionSummaryDTO> assistantSalerPromotionSummaryDTOMap = assistantSalerPromotionSummaryDTOList.stream().collect(Collectors.toMap(AssistantSalerPromotionSummaryDTO::getEmpId,Function.identity()));
        AssistantSalerPromotionSummaryDTO dto = null;
        Integer rankTmp = null;
        Integer lastRankTmp = null;
        for(AssistantSalerPromotionRankInfo info : assistantSalerPromotionRankInfoList){
            rankTmp = info.getRank();
            dto = assistantSalerPromotionSummaryDTOMap.get(info.getEmpId());
            if(Objects.isNull(dto)){
                //上月没有数据的员工,环比排名不动
                info.setRankStatus(OnlinePromotionConstants.RANK_STATUS_NO_UP_NO_DOWN);
                continue;
            }
            if(Objects.equals(OnlinePromotionLevelTypeEnum.NATION.getLevelType(),assistantSalerPromotionSummaryQuery.getRankType())){
                if("share".equals(assistantSalerPromotionSummaryQuery.getDataType())){
                    lastRankTmp = dto.getSmEffectiveShareCntRank();
                }else if("visit".equals(assistantSalerPromotionSummaryQuery.getDataType())){
                    lastRankTmp = dto.getSmEffectiveVisitUserCntRank();
                }
            }else if(Objects.equals(OnlinePromotionLevelTypeEnum.AREA.getLevelType(),assistantSalerPromotionSummaryQuery.getRankType())){
                if("share".equals(assistantSalerPromotionSummaryQuery.getDataType())){
                    lastRankTmp = dto.getSmEffectiveShareCntAreaRank();
                }else if("visit".equals(assistantSalerPromotionSummaryQuery.getDataType())){
                    lastRankTmp = dto.getSmEffectiveVisitUserCntAreaRank();
                }
            }
            info.setRankStatus(getRankStatus(rankTmp,lastRankTmp));
            info.setRankComparativeQuarterly(getRank(rankTmp,lastRankTmp));
        }
        return assistantSalerPromotionRankVO;
    }

    private AssistantSalerPromotionRankVO rankHandle(AssistantSalerPromotionRankVO assistantSalerPromotionRankVO, Integer currentRank, Integer lastRank){
        assistantSalerPromotionRankVO.setMineSmComparativeQuarterlyRank(getRank(currentRank,lastRank));
        assistantSalerPromotionRankVO.setRankStatus(getRankStatus(currentRank,lastRank));
        return assistantSalerPromotionRankVO;
    }

    AssistantSalerPromotionRankInfo buildAssistantSalerPromotionRankInfo(Integer smCnt, String empId, String empName, String avatarUrl, Integer rank){
        AssistantSalerPromotionRankInfo rankInfo = new AssistantSalerPromotionRankInfo();
        rankInfo.setSmCnt(NumberUtils.wanTransfer(smCnt));
        rankInfo.setEmpId(empId);
        rankInfo.setEmpName(empName);
        rankInfo.setAvatarUrl(avatarUrl);
        rankInfo.setRank(rank);
        return rankInfo;
    }

    private Map<String, CustVisitCountQueryOutput> getCurrentVisitCountMap(List<String> manageCodeList){
        if(CollectionUtils.isEmpty(manageCodeList)){
            return Maps.newHashMap();
        }
        CustVisitCountQueryInput customerGroupVisitGroupQueryInput = new CustVisitCountQueryInput();
        customerGroupVisitGroupQueryInput.setManageCodeList(manageCodeList);
        List<CustVisitCountQueryOutput> custVisitCountQueryOutputs = whaleOrderService.queryCurrentDayCustVisitInfoCount(customerGroupVisitGroupQueryInput);
        Map<String,CustVisitCountQueryOutput> custVisitCountQueryOutputMap = Maps.newHashMap();
        if(CollectionUtils.isEmpty(custVisitCountQueryOutputs)){
            return custVisitCountQueryOutputMap;
        }
        for(CustVisitCountQueryOutput output : custVisitCountQueryOutputs){
            custVisitCountQueryOutputMap.put(output.getManagerCode(),output);
        }
        return custVisitCountQueryOutputMap;
    }

    private Integer getCurrenDayEffectiveVisitCount(Map<String,CustVisitCountQueryOutput> custVisitCountQueryOutputMap, String manageCode){
        CustVisitCountQueryOutput output = custVisitCountQueryOutputMap.get(manageCode);
        if(Objects.isNull(output)){
            return 0;
        }
        return output.getEffectiveVisitCount();
    }

    private Map<String, MarketingForwardEffectiveInfo> getCurrentShareCountMap(List<String> manageCodeList){
        if(CollectionUtils.isEmpty(manageCodeList)){
            return Maps.newHashMap();
        }
        GetEffectiveShareInfoQueryDTO getEffectiveShareInfoQueryDTO = new GetEffectiveShareInfoQueryDTO();
        getEffectiveShareInfoQueryDTO.setMasterJobNumberList(manageCodeList);
        //保险域编码
        getEffectiveShareInfoQueryDTO.setBizType("4");
        getEffectiveShareInfoQueryDTO.setForwardTime(DateUtils.getCurrentMinDateTime());
        List<MarketingForwardEffectiveInfo> custVisitCountQueryOutputs = omsBaseService.getEffectiveShareInfo(getEffectiveShareInfoQueryDTO);
        Map<String,MarketingForwardEffectiveInfo> custVisitCountQueryOutputMap = Maps.newHashMap();
        if(CollectionUtils.isEmpty(custVisitCountQueryOutputs)){
            return custVisitCountQueryOutputMap;
        }
        for(MarketingForwardEffectiveInfo output : custVisitCountQueryOutputs){
            custVisitCountQueryOutputMap.put(output.getForwardJobNumber(),output);
        }
        return custVisitCountQueryOutputMap;
    }

    private Integer getCurrenDayEffectiveShareCount(Map<String,MarketingForwardEffectiveInfo> custVisitCountQueryOutputMap, String manageCode){
        MarketingForwardEffectiveInfo output = custVisitCountQueryOutputMap.get(manageCode);
        if(Objects.isNull(output)){
            return 0;
        }
        return output.getShareCount();
    }

    /**
     * 获取当前分支所有员工并计算当日访客和分享数量
     * @param assistantManagerPromotionSummaryQuery
     * @param summaryVO
     * @return
     */
    private AssistantManagerPromotionSummaryVO appendCurrentDayData(AssistantManagerPromotionQuery assistantManagerPromotionSummaryQuery, AssistantManagerPromotionSummaryVO summaryVO){
        List<AssistantSalerPromotionSummaryDTO> assistantSalerPromotionSummaryDTOList = empOnlinePromotionDfpMapper.empSummaryList(assistantManagerPromotionSummaryQuery.getPt(),null,assistantManagerPromotionSummaryQuery.getBchCode());
        List<String> empIdList = assistantSalerPromotionSummaryDTOList.stream().map(AssistantSalerPromotionSummaryDTO::getEmpId).collect(Collectors.toList());

        Map<String,CustVisitCountQueryOutput> custVisitCountQueryOutputMap = getCurrentVisitCountMap(empIdList);
        Map<String, MarketingForwardEffectiveInfo> shareCountMap = getCurrentShareCountMap(empIdList);
        Integer sumSdShareCnt = 0;
        Integer sumSdVisitCnt = 0;
        for(String empId : empIdList){
            sumSdShareCnt += getCurrenDayEffectiveShareCount(shareCountMap,empId);
            sumSdVisitCnt += getCurrenDayEffectiveVisitCount(custVisitCountQueryOutputMap,empId);
        }
        summaryVO.setSdShareCnt(NumberUtils.wanTransfer(sumSdShareCnt));
        summaryVO.setSdVisitCnt(NumberUtils.wanTransfer(sumSdVisitCnt));
        return summaryVO;
    }

    /**
     * 统计未跟进访客数
     * @param epmId
     * @return
     */
    private Integer queryCustNonVisitCount(String epmId){
        CustVisitListQueryInput custVisitListQueryInput = new CustVisitListQueryInput();
        custVisitListQueryInput.setManageCode(epmId);
        //跟进标识 0 未跟进, 1 已跟进
        custVisitListQueryInput.setFollowFlag(0);
        return whaleOrderService.queryCustVisitCount(custVisitListQueryInput);
    }

    /**
     * 查询结果集为0,返回默认数据
     * @param levelVO
     * @return
     */
    private AssistantManagerPromotionLevelVO buildEmptyNextLevelDetailList(AssistantManagerPromotionLevelVO levelVO){
        levelVO.setTotal(0);
        levelVO.setLevelDetailList(Lists.newArrayList());
        return levelVO;
    }
}
