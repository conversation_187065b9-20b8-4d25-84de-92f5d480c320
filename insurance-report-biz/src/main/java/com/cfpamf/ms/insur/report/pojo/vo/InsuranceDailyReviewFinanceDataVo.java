package com.cfpamf.ms.insur.report.pojo.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/18
 * 查询保险日复盘信贷数据response类
 */
@Data
public class InsuranceDailyReviewFinanceDataVo {

    @ApiModelProperty(value = "客户经理工号")
    private String loanManagerId;

    @ApiModelProperty(value = "业务类型，授信：CREDIT，放款：LOAN")
    private String businessType;

    @ApiModelProperty(value = "贷款产品类型")
    private String loanType;

    @ApiModelProperty(value = "申请单号")
    private String applSeq;

    @ApiModelProperty(value = "申请/放款金额")
    private String applAmount;

    @ApiModelProperty(value = "申请/放款时间")
    private String applTime;

    @ApiModelProperty(value = "借款人信息")
    private BorrowerInfoVo borrowerInfoVo;

    @ApiModelProperty(value = "共借人列表")
    private List<BorrowerInfoVo> coborrowerVoList;

    @ApiModelProperty(value = "担保人列表")
    private List<BorrowerInfoVo> bondsmanVoList;

}
