package com.cfpamf.ms.insur.report.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * excel工具类
 *
 * <AUTHOR>
 */
public class ExcelReadUtil {

    public static final String SUFFIX_2003=".xls";

    public static final String SUFFIX_2007=".xlsx";

    /**
     * 时间格式yyyy-MM-dd hh:mm:ss
     */
    private static final String JAVA_TIME_FORMAT_YYYY_MM_DD = "yyyy-MM-dd HH:mm:ss";

    /**
     * 时间格式HH:mm
     */
    private static final String JAVA_TIME_FORMAT_HHMM = "HH:mm";

    /**
     * 时间格式h:mm
     */
    private static final String EXCEL_TIME_FORMAT_HMM = "h:mm";

    /**
     * excel 时间code
     */
    private static final int EXCEL_TIME_FORMAT_CODE = 58;

    /**
     * excel字段格式
     */
    private static final String EXCEL_TIME_FORMAT_TYPE_GENERAL = "General";


    private ExcelReadUtil() {
    }

    /**
     * 上传文件解析出Excel
     *
     * @param file
     * @return
     */
    public static Workbook analysisWorkbookFromFile(MultipartFile file) {
        Workbook workbook = null;
        try (InputStream is = file.getInputStream()) {
            String fileName = file.getOriginalFilename();
            if (fileName.endsWith(SUFFIX_2003)) {
                workbook = new HSSFWorkbook(is);
            } else if (fileName.endsWith(SUFFIX_2007)) {
                workbook = new XSSFWorkbook(is);
            }
        } catch (Exception e) {
            //todo
            //throw new MSBizNormalException("","Excel文件格式解析失败", e);
        }
        return workbook;
    }

    /**
     * 读取单元格字符串值
     *
     * @param cell
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String getCellValue(Cell cell) {
        String cellValue = "";
        if (cell == null) {
            return cellValue;
        }
        switch (cell.getCellTypeEnum()) {
            case NUMERIC:
                cellValue = cell.getNumericCellValue() + "";
                break;
            case STRING:
                cellValue = String.valueOf(cell.getStringCellValue());
                break;
            case BOOLEAN:
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA:
                cellValue = String.valueOf(cell.getCellFormula());
                break;
            case BLANK:
                cellValue = "";
                break;
            case ERROR:
                cellValue = "非法字符";
                break;
            default:
                cellValue = "未知类型";
                break;
        }
        String feedLine = "\n";
        String wordNull = "NULL";
        cellValue = cellValue.replaceAll(feedLine, "")
                .replaceAll(wordNull, "")
                .trim();
        return StringUtils.isEmpty(cellValue) ? null : cellValue;
    }

    /**
     * 时间格式处理
     *
     * @return
     */
    public static String stringDateProcess(Cell cell) {
        String result;
        if (HSSFDateUtil.isCellDateFormatted(cell)) {
            SimpleDateFormat sdf;
            if (cell.getCellStyle().getDataFormat() == HSSFDataFormat.getBuiltinFormat(EXCEL_TIME_FORMAT_HMM)) {
                sdf = new SimpleDateFormat(JAVA_TIME_FORMAT_HHMM);
            } else {
                sdf = new SimpleDateFormat(JAVA_TIME_FORMAT_YYYY_MM_DD);
            }
            Date date = cell.getDateCellValue();
            result = sdf.format(date);
        } else if (cell.getCellStyle().getDataFormat() == EXCEL_TIME_FORMAT_CODE) {
            SimpleDateFormat sdf = new SimpleDateFormat(JAVA_TIME_FORMAT_YYYY_MM_DD);
            double value = cell.getNumericCellValue();
            Date date = org.apache.poi.ss.usermodel.DateUtil
                    .getJavaDate(value);
            result = sdf.format(date);
        } else {
            double value = cell.getNumericCellValue();
            CellStyle style = cell.getCellStyle();
            DecimalFormat format = new DecimalFormat();
            String temp = style.getDataFormatString();
            if (Objects.equals(temp, EXCEL_TIME_FORMAT_TYPE_GENERAL)) {
                format.applyPattern("#");
            }
            result = format.format(value);
        }
        return result;
    }
}
