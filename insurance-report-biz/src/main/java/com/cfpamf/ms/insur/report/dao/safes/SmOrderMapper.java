package com.cfpamf.ms.insur.report.dao.safes;

import com.cfpamf.ms.insur.report.dao.CommonMapper;
import com.cfpamf.ms.insur.report.pojo.dto.OrderCommissionDTO;
import com.cfpamf.ms.insur.report.pojo.po.SmOrder;
import com.cfpamf.ms.insur.report.pojo.query.LookBackQuery;
import com.cfpamf.ms.insur.report.pojo.vo.OrderDetailVO;
import com.cfpamf.ms.insur.report.pojo.vo.OrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Create By zhengjing on 2020/9/15 16:04
 */
@Mapper
public interface SmOrderMapper extends CommonMapper<SmOrder> {

    List<OrderVO> selectByLookBack(LookBackQuery backQuery);

    OrderDetailVO getDetail(@Param("orderNo") String orderNo);

    OrderCommissionDTO getTodayCommissionByTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
