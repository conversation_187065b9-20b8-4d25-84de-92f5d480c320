package com.cfpamf.ms.insur.report.pojo.vo.personnel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 个人信息
 *
 * <AUTHOR>
 * @date 2021/3/30 9:36
 */
@Data
public class PersonnelVo {
    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    private String region;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String jobNumber;

    /**
     * 机构/组织
     */
    @ApiModelProperty(value = "机构/组织")
    private String organizationName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 入职日期
     */
    @ApiModelProperty(value = "入职日期")
    private LocalDateTime entryDate;

    /**
     * 岗位
     */
    @ApiModelProperty(value = "岗位")
    private String jobName;
}
