package com.cfpamf.ms.insur.report.pojo.vo.branch;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 人均保费
 *
 * <AUTHOR>
 * @date 2021/3/30 18:16
 */
@Data
public class PerCapitaPremiumVo {
    /**
     * 当月在职员工月度人均保费
     */
    @ApiModelProperty(value = "当月在职员工人均保费")
    private BigDecimal currentMonthJobStaff;

    /**
     * 当年在职员工月度人均保费
     */
    @ApiModelProperty(value = "当年在职员工人均保费")
    private BigDecimal currentYearJobStaff;

    /**
     * 当月编制月度人均保费人数
     */
    @ApiModelProperty(value = "当月编制月度人均保费人数")
    private BigDecimal currentMonthOrganizationStaff;

    /**
     * 当年编制月度人均保费人数
     */
    @ApiModelProperty(value = "当年编制月度人均保费人数")
    private BigDecimal currentYearOrganizationStaff;
}
