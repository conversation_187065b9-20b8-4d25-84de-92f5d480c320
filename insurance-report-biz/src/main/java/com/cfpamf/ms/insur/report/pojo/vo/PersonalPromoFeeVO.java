package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PersonalPromoFeeVO extends PromoFeeVO implements Serializable {
    @ExportField(name = "员工姓名",order = 4)
    @ApiModelProperty("员工姓名")
    private String userName;

    @ExportField(name = "员工工号",order = 5)
    @ApiModelProperty("员工工号")
    private String userId;

    @ApiModelProperty("保单推广费")
    private BigDecimal commissionAmount;

    @ApiModelProperty("退保推广费")
    private BigDecimal surrenderCommissionAmount;

    @ExportField(name = "推广费",order = 10,type = "money")
    @ApiModelProperty("推广费")
    private BigDecimal promoFee;
}
