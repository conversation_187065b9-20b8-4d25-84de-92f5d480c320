package com.cfpamf.ms.insur.report.feign.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/4/26 14:16
 */
@Getter
@AllArgsConstructor
public enum EnumTodoBizType {
    /**
     * target_id id_number
     * 断保
     * sort 99
     */
    INTERRUPTION
    /**
     * target_id policy_no
     * 续保-短险
     * sort 10~19
     */
    , RENEW_SHORT
    /**
     *续保-长险 target_id      {订单号}-{term}
     *  sort 10~19
     */
    , RENEW_LONG,
    /**
     * target_id  id_number
     * 信贷客户 20~50
     */
    LOAN;
}
