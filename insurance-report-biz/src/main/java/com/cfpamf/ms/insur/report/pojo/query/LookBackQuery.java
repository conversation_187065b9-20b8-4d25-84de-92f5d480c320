package com.cfpamf.ms.insur.report.pojo.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * Create By zhengjing on 2020/9/15 17:10
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LookBackQuery extends PageQuery {

    /**
     * 创建时间From
     */
    @DateTimeFormat
    @ApiModelProperty(value = "创建时间From 不传会查询上线时间之后的数据【不能超过上线时间】")
    LocalDate createDateStart;

    /**
     * 创建时间To
     */
    @DateTimeFormat
    @ApiModelProperty(value = "创建时间To 不传会查询昨天以前的数据 不能超过昨天")
    LocalDate createDateEnd;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    String policyNo;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    String orderNo;

    /**
     * 投保人
     */
    @ApiModelProperty(value = "投保人")
    private String applicantdName;

    /**
     * 被保人
     */
    @ApiModelProperty(value = "被保人")
    private String insuredName;
}
