package com.cfpamf.ms.insur.report.service.diagnosis.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 异业保费配比完成情况的小结数据模型类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/02/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InsuranceAmountRateSummaryModel extends AbstractDiagnosisModel {

    private Summary summary;

    @Data
    public static class Summary {
        /**
         * 组织类型，取值可以参见{@link com.cfpamf.ms.insur.report.enums.OrgLevelEnum}
         */
        private String type;
        /**
         * 目标是否完成
         */
        private boolean finished;
        /**
         * 当年异业保费配比
         */
        private String insuranceAmountYearRate;
        /**
         * 异业保费配比完成情况的TOP信息
         */
        private List<RankInfo> rankInfos;

        @Data
        public static class RankInfo {
            /**
             * 排名名称
             */
            private String name;
            /**
             * 当年异业保费配比
             */
            private String insuranceAmountYearRate;
        }
    }
}
