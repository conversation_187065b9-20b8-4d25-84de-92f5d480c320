package com.cfpamf.ms.insur.report.pojo.vo.promotion;

import com.cfpamf.ms.insur.report.pojo.dto.AssistantManagerPromotionRankDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AssistantManagerPromotionRankVO {

    @ApiModelProperty(value = "层级编码", notes = "")
    String currentName;

    @ApiModelProperty(value = "层级编码", notes = "")
    String currentRank;

    @ApiModelProperty(value = "当月分享排名", notes = "")
    List<AssistantManagerPromotionRankDetail> rankDetailList;

    /**
     * 数据类型 share 分享, visit
     */
    @ApiModelProperty(value = "数据类型 share 分享, visit")
    String dataType;

    @ApiModelProperty("nation 全国, area 区域, district 片区, bch 分支")
    String levelType;

    @ApiModelProperty("总数")
    Integer total;
}
