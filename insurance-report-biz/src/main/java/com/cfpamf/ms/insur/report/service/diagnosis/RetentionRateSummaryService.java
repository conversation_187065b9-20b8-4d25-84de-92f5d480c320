package com.cfpamf.ms.insur.report.service.diagnosis;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.dao.safepg.*;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.enums.OrgLevelEnum;
import com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo;
import com.cfpamf.ms.insur.report.service.diagnosis.model.AbstractDiagnosisModel;
import com.cfpamf.ms.insur.report.service.diagnosis.model.RetentionRateSummaryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 客户留存率小结的诊断实现服务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/02/27
 */
@Service
public class RetentionRateSummaryService extends AbstractBizHelperDiagnosisService {

    @Resource
    private AdsInsuranceCfpamfMarketingProgressDfpMapper cfpamfMapper;

    @Resource
    private AdsInsuranceAreaMarketingProgressDfpMapper areaMapper;

    @Resource
    private AdsInsuranceDistrictMarketingProgressDfpMapper districtMapper;

    @Resource
    private AdsInsuranceBchMarketingProgressDfpMapper branchMapper;

    @Resource
    private AdsInsuranceEmpMarketingProgressDfpMapper personMapper;

    /**
     * 获取受支持的诊断类型
     *
     * @return 诊断类型的枚举类
     */
    @Override
    public DiagnosisTypeEnum getSupportDiagnosisType() {
        return DiagnosisTypeEnum.RETENTION_RATE_SUMMARY;
    }

    /**
     * 根据请求参数，创建诊断的数据模型
     *
     * @param request 请求参数
     * @return 诊断的数据模型
     */
    @Override
    protected AbstractDiagnosisModel createModel(DiagnosisRequest request) {
        RetentionRateSummaryModel model = new RetentionRateSummaryModel();
        String pt = request.getPt();
        OrgLevelEnum orgLevel = request.getOrgLevel();
        switch (orgLevel) {
            case COUNTRY: {
                RetentionRateSummaryService.RetentionRateSummaryVo retentionRateSummaryVo = cfpamfMapper.queryRetentionRateSummary(pt);
                if (retentionRateSummaryVo == null) {
                    return null;
                }
                boolean retentionRateTag = Optional.ofNullable(retentionRateSummaryVo.getSyInsuranceRetentionRate()).orElse(0d) >= Optional.ofNullable(retentionRateSummaryVo.getSmInsuranceRetentionRateTarget()).orElse(0d);
                List<DiagnosisRankInfoVo> rankInfoVos = areaMapper.queryRetentionRateSummaryRankInfo(pt, retentionRateTag);
                constructModel(rankInfoVos, model, orgLevel, retentionRateTag, retentionRateSummaryVo);
                break;
            }
            case AREA: {
                RetentionRateSummaryService.RetentionRateSummaryVo retentionRateSummaryVo = areaMapper.queryRetentionRateSummary(pt, request.getOrgCodes());
                if (retentionRateSummaryVo == null) {
                    return null;
                }
                boolean retentionRateTag = Optional.ofNullable(retentionRateSummaryVo.getSyInsuranceRetentionRate()).orElse(0d) >= Optional.ofNullable(retentionRateSummaryVo.getSmInsuranceRetentionRateTarget()).orElse(0d);
                List<DiagnosisRankInfoVo> rankInfoVos = null;
                if(request.isExistDistrict()){
                    //区域下有片区则获取片区数据
                    rankInfoVos = districtMapper.queryRetentionRateSummaryRankInfo(pt, retentionRateTag, request.getOrgCodes(), orgLevel.getLevel());
                }else{
                    //区域下无片区则获取分支数据
                    rankInfoVos = branchMapper.queryRetentionRateSummaryRankInfo(pt, retentionRateTag, request.getOrgCodes(), orgLevel.getLevel(),request.getBlackBchList());
                }
                constructModel(rankInfoVos, model, orgLevel, retentionRateTag, retentionRateSummaryVo);
                break;
            }
            case DISTRICT: {
                RetentionRateSummaryService.RetentionRateSummaryVo retentionRateSummaryVo = districtMapper.queryRetentionRateSummary(pt, request.getOrgCodes());
                if (retentionRateSummaryVo == null) {
                    return null;
                }
                boolean retentionRateTag = Optional.ofNullable(retentionRateSummaryVo.getSyInsuranceRetentionRate()).orElse(0d) >= Optional.ofNullable(retentionRateSummaryVo.getSmInsuranceRetentionRateTarget()).orElse(0d);
                List<DiagnosisRankInfoVo> rankInfoVos = branchMapper.queryRetentionRateSummaryRankInfo(pt, retentionRateTag, request.getOrgCodes(), orgLevel.getLevel(),request.getBlackBchList());
                constructModel(rankInfoVos, model, orgLevel, retentionRateTag, retentionRateSummaryVo);
                break;
            }
            case BRANCH: {
                RetentionRateSummaryService.RetentionRateSummaryVo retentionRateSummaryVo = branchMapper.queryRetentionRateSummary(pt, request.getOrgCodes());
                if (retentionRateSummaryVo == null) {
                    return null;
                }
                boolean retentionRateTag = Optional.ofNullable(retentionRateSummaryVo.getSyInsuranceRetentionRate()).orElse(0d) >= Optional.ofNullable(retentionRateSummaryVo.getSmInsuranceRetentionRateTarget()).orElse(0d);
                List<DiagnosisRankInfoVo> rankInfoVos = personMapper.queryRetentionRateSummaryRankInfo(pt, retentionRateTag, request.getOrgCodes());
                constructModel(rankInfoVos, model, orgLevel, retentionRateTag, retentionRateSummaryVo);
                break;
            }
            default:
                break;
        }
        return model;
    }


    /**
     * 构造模型
     *
     * @param rankInfoVos            排名信息
     * @param model                  数据模型
     * @param orgLevel               组织级别
     * @param retentionRateTag       客户留存率是否达标
     * @param retentionRateSummaryVo 客户留存率数据
     */
    private static void constructModel(List<DiagnosisRankInfoVo> rankInfoVos, RetentionRateSummaryModel model, OrgLevelEnum orgLevel, boolean retentionRateTag, RetentionRateSummaryVo retentionRateSummaryVo) {
        List<RetentionRateSummaryModel.Summary.RankInfo> rankInfos = new ArrayList<>(3);
        if (CollectionUtils.isNotEmpty(rankInfoVos)) {
            for (DiagnosisRankInfoVo rankInfoVo : rankInfoVos) {
                if (StringUtils.isNotBlank(rankInfoVo.getSyInsuranceRetentionRateRankName())) {
                    RetentionRateSummaryModel.Summary.RankInfo rankInfo = new RetentionRateSummaryModel.Summary.RankInfo();
                    rankInfos.add(rankInfo);
                    rankInfo.setName(rankInfoVo.getSyInsuranceRetentionRateRankName());
                    rankInfo.setRetentionYearRate(DECIMAL_FORMAT.format(BigDecimal.valueOf(Optional.ofNullable(rankInfoVo.getSyInsuranceRetentionRate()).orElse(0d))
                            .multiply(BigDecimal.valueOf(100))));
                }
            }
        }
        RetentionRateSummaryModel.Summary summary = new RetentionRateSummaryModel.Summary();
        model.setSummary(summary);
        summary.setType(orgLevel.getLevel());
        summary.setFinished(retentionRateTag);
        summary.setRetentionYearRate(DECIMAL_FORMAT.format(BigDecimal.valueOf(Optional.ofNullable(retentionRateSummaryVo.getSyInsuranceRetentionRate()).orElse(0d))
                .multiply(BigDecimal.valueOf(100))));
        summary.setRankInfos(rankInfos);
    }

    /**
     * 获取诊断模板文件的地址
     *
     * @return 模板文件的地址
     */
    @Override
    protected String getTemplateFilePath() {
        return "retention_rate_summary.ftl";
    }

    /**
     * 留存率小结数据对象
     */
    @Data
    public static class RetentionRateSummaryVo {
        /**
         * 当年客户留存率
         */
        private Double syInsuranceRetentionRate;
        /**
         * 当月年累计客户留存率目标
         */
        private Double smInsuranceRetentionRateTarget;
    }
}
