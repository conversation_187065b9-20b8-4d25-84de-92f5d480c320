package com.cfpamf.ms.insur.report.dao.safepg;

import com.cfpamf.ms.insur.report.pojo.vo.activity.ActivityEmpVO;
import com.cfpamf.ms.insur.report.service.diagnosis.UnloanNcNormInsuranceAmtAssistantService;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> lww
 */
@Mapper
public interface TempSafesStatsNewYearMapper {

    /**
     * 获取分支活动数据
     * @param pt 分区
     * @param bchCodes 分支编码
     * @return 分支活动数据
     */
    UnloanNcNormInsuranceAmtAssistantService.UnloanNcNormInsuranceAmtAssistantVo queryByBchCode(String pt, List<String> bchCodes);

    /**
     * 获取上一名分支活动数据
     * @param pt 分区
     * @param rank 排名
     * @return 上一名分支活动数据
     */
    UnloanNcNormInsuranceAmtAssistantService.UnloanNcNormInsuranceAmtAssistantVo getBchPreDiff(String pt, Integer rank);

    /**
     * 获取分支员工活动数据列表
     * @param bchCode 分支编码
     * @return 员工活动数据列表
     */
    List<ActivityEmpVO> queryBchDetail(String pt, String bchCode);
}