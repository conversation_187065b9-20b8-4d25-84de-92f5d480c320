package com.cfpamf.ms.insur.report.pojo.vo.total;

import com.cfpamf.ms.insur.report.pojo.vo.branch.CompletionRateVo;
import com.cfpamf.ms.insur.report.pojo.vo.branch.PerCapitaPremiumVo;
import com.cfpamf.ms.insur.report.pojo.vo.branch.PremiumTargetVo;
import com.cfpamf.ms.insur.report.pojo.vo.branch.StaffCountVo;
import com.cfpamf.ms.insur.report.pojo.vo.excel.ExcelVoConvert;
import com.cfpamf.ms.insur.report.pojo.vo.excel.TotalStatisticsExcelVo;
import com.cfpamf.ms.insur.report.pojo.vo.personnel.*;
import com.cfpamf.ms.insur.report.pojo.vo.region.LoanAmountVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * 合计统计
 *
 * <AUTHOR>
 * @date 2021/3/31 15:40
 */
@Data
public class TotalStatisticsVo implements ExcelVoConvert {

    /**
     * 合计
     */
    @ApiModelProperty(value = "合计")
    private String Total = "合计";

    /**
     * 员工人数统计
     */
    @ApiModelProperty(value = "员工人数")
    private StaffCountVo staffCountVo = new StaffCountVo();

    /**
     * 单量统计
     */
    @ApiModelProperty(value = "单量统计")
    private OrderQuantityStatisticsVo orderQuantityStatisticsVo;

    /**
     * 保费统计
     */
    @ApiModelProperty(value = "保费统计")
    private PremiumStatisticsVo premiumStatisticsVo;
    /**
     * 保费目标
     */
    @ApiModelProperty(value = "保费目标")
    private PremiumTargetVo premiumTargetVo = new PremiumTargetVo();

    /**
     * 保费目标完成率
     */
    @ApiModelProperty(value = "保费目标完成率")
    private CompletionRateVo completionRateVo = new CompletionRateVo();

    /**
     * 月度人均保费统计
     */
    @ApiModelProperty(value = "月度人均保费")
    private PerCapitaPremiumVo perCapitaPremiumVo = new PerCapitaPremiumVo();

    /**
     * 非信贷客户统计
     */
    @ApiModelProperty(value = "非信贷客户")
    private NoLoanerStatisticsVo noLoanerStatisticsVo = new NoLoanerStatisticsVo();

    /**
     * 续保率统计
     */
    @ApiModelProperty(value = "续保率")
    private RenewInsuranceRateVo renewInsuranceRateVo = new RenewInsuranceRateVo();

    /**
     * 客户转化率
     */
    @ApiModelProperty(value = "客户转化率")
    private CustomerConversionRateVo customerConversionRateVo = new CustomerConversionRateVo();

    /**
     * 放款金额
     */
    @ApiModelProperty(value = "放款金额")
    private LoanAmountVo loanAmountVo = new LoanAmountVo();

    @Override
    public Object convert() {
        TotalStatisticsExcelVo totalStatisticsExcelVo = new TotalStatisticsExcelVo();
        //设置单量统计信息
        OrderQuantityStatisticsVo orderQuantityStatisticsVo = getOrderQuantityStatisticsVo();
        if (Objects.nonNull(orderQuantityStatisticsVo)) {
            totalStatisticsExcelVo.setDayOrderQuantity(orderQuantityStatisticsVo.getDay());
            totalStatisticsExcelVo.setMonthOrderQuantity(orderQuantityStatisticsVo.getMonth());
            totalStatisticsExcelVo.setYearOrderQuantity(orderQuantityStatisticsVo.getYear());
        }
        //设置在职人数信息
        StaffCountVo staffCountVo = getStaffCountVo();
        if (Objects.nonNull(staffCountVo)) {
            totalStatisticsExcelVo.setMonthJobStaffCount(staffCountVo.getMonthJobStaffCount());
            totalStatisticsExcelVo.setOrganizationStaffCount(staffCountVo.getOrganizationStaffCount());
        }
        //设置保费信息
        PremiumStatisticsVo premiumStatisticsVo = getPremiumStatisticsVo();
        if (Objects.nonNull(premiumStatisticsVo)) {
            totalStatisticsExcelVo.setDayPremium(premiumStatisticsVo.getDay());
            totalStatisticsExcelVo.setMonthPremium(premiumStatisticsVo.getMonth());
            totalStatisticsExcelVo.setYearPremium(premiumStatisticsVo.getYear());
        }
        //设置非信贷客户信息
        NoLoanerStatisticsVo noLoanerStatisticsVo = getNoLoanerStatisticsVo();
        if (Objects.nonNull(noLoanerStatisticsVo)) {
            totalStatisticsExcelVo.setMonthPremiumNoLoaner(noLoanerStatisticsVo.getMonthPremium());
            totalStatisticsExcelVo.setYearPremiumNoLoaner(noLoanerStatisticsVo.getYearPremium());
            totalStatisticsExcelVo.setMonthPremiumProportionNoLoaner(noLoanerStatisticsVo.getMonthPremiumProportion());
            totalStatisticsExcelVo.setYearPremiumProportionNoLoaner(noLoanerStatisticsVo.getYearPremiumProportion());
            totalStatisticsExcelVo.setCurrentMonthProportionNoLoaner(noLoanerStatisticsVo.getCurrentMonthProportion());
        }
        //设置转化率信息
        CustomerConversionRateVo customerConversionRateVo = getCustomerConversionRateVo();
        if (Objects.nonNull(customerConversionRateVo)) {
            totalStatisticsExcelVo.setLoanerConversionRate(customerConversionRateVo.getLoaner());
            totalStatisticsExcelVo.setLoanerRelevantConversionRate(customerConversionRateVo.getLoanerRelevant());
        }
        //设置续保率信息
        RenewInsuranceRateVo renewInsuranceRateVo = getRenewInsuranceRateVo();
        if (Objects.nonNull(renewInsuranceRateVo)) {
            totalStatisticsExcelVo.setYearRenewInsuranceRate(renewInsuranceRateVo.getYear());
            totalStatisticsExcelVo.setMonthRenewInsuranceRate(renewInsuranceRateVo.getMonth());
        }
        //设置保费目标
        PremiumTargetVo premiumTargetVo = getPremiumTargetVo();
        if (Objects.nonNull(premiumTargetVo)) {
            totalStatisticsExcelVo.setCurrentMonthPremiumTarget(premiumTargetVo.getCurrentMonth());
            totalStatisticsExcelVo.setCurrentYearPremiumTarget(premiumTargetVo.getCurrentYear());
            totalStatisticsExcelVo.setPresentCurrentMonthPremiumTarget(premiumTargetVo.getPresentCurrentMonth());
        }
        //设置目标完成率
        CompletionRateVo completionRateVo = getCompletionRateVo();
        if (Objects.nonNull(completionRateVo)) {
            totalStatisticsExcelVo.setCurrentMonthCompletionRate(completionRateVo.getCurrentMonth());
            totalStatisticsExcelVo.setCurrentYearCompletionRate(completionRateVo.getCurrentYear());
            totalStatisticsExcelVo.setPresentCurrentMonthCompletionRate(completionRateVo.getPresentCurrentMonth());
        }
        //设置人均保费
        PerCapitaPremiumVo perCapitaPremiumVo = getPerCapitaPremiumVo();
        if (Objects.nonNull(perCapitaPremiumVo)) {
            totalStatisticsExcelVo.setCurrentMonthJobStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentMonthJobStaff());
            totalStatisticsExcelVo.setCurrentYearJobStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentYearJobStaff());
            totalStatisticsExcelVo.setCurrentMonthOrganizationStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentMonthOrganizationStaff());
            totalStatisticsExcelVo.setCurrentYearOrganizationStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentYearOrganizationStaff());
        }
        return totalStatisticsExcelVo;
    }
}
