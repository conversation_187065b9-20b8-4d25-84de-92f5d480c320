package com.cfpamf.ms.insur.report.feign;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.report.feign.constants.BmsConstantCore;
import com.cfpamf.ms.insur.report.feign.resp.vo.UserVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "ms-bizconfig-service", url = "${ms-bizconfig-service.url}")
public interface BmsBizconfigFacade {

    @ApiOperation(value = "按督导员工编号查询员工信息-v2")
    @GetMapping("bizconfig/user/getUserInfoBySupervisor/v2")
    public Result<List<UserVo>> getUserInfoBySupervisor(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization,
                                                        @RequestParam String branchCode, @RequestParam(required = true) String supervisorCode);
}
