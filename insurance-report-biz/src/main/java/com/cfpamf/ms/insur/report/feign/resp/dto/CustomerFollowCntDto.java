package com.cfpamf.ms.insur.report.feign.resp.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 断保客户
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerFollowCntDto{

    @ApiModelProperty(name="员工工号")
    String empCode;

    @ApiModelProperty(name="员工姓名")
    String empName;

    @ApiModelProperty(name="分支编码")
    String bchCode;

    @ApiModelProperty(name="分支名称")
    String bchName;


    @ApiModelProperty(name="跟进数")
    Integer followCnt;

    @ApiModelProperty(name="类型")
    String bizType;

    @ApiModelProperty(name="激活保费")
    BigDecimal conversionAmt;

    @ApiModelProperty(name="激活数")
    Integer conversionCnt;
}
