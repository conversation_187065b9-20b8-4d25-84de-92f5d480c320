package com.cfpamf.ms.insur.report.util;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 基础加密组件
 *
 * <AUTHOR> @version 1.0
 * @since 1.0
 */
public abstract class Base64Util {

    protected Base64Util() {
    }

    /**
     * BASE64解密
     *
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] decryptBASE64(String key) {
        return Base64.getDecoder().decode(key);
    }

    /**
     * BASE64加密
     *
     * @param key
     * @return
     * @throws Exception
     */
    public static String encryptBASE64(byte[] key) {
        return Base64.getEncoder().encodeToString(key);
    }

    /**
     * 判断是否为BASE加密
     */
    public static boolean isEncrypt(String val) {
        try {
            byte[] key = decryptBASE64(val);
            String strs = new String(key, StandardCharsets.UTF_8.name());
            String result = encryptBASE64(strs.getBytes(StandardCharsets.UTF_8.name()));
            if (result.equals(val)) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }
}