package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 续保率
 */
@Data
public class RenewalRateVO implements Serializable {
    /**
     * 统计日期（始） 时间格式 yyyy-MM-dd
     */
    @ExportField(name = "统计日期（始）", order = 0)
    @ApiModelProperty("统计日期（始）*")
    private String startDate;

    @ExportField(name = "统计日期（止）", order = 1)
    @ApiModelProperty("统计日期（止）*")
    private String endDate;

    @ExportField(name = "到期客户数", order = 6,type = "integer")
    @ApiModelProperty("到期客户数")
    private Integer endCnt;




    @ExportField(name = "续保客户数", order = 7,type = "integer")
    @ApiModelProperty("续保客户数")
    private Integer renewalCnt;

    @ExportField(name = "客户续保率", order = 8,type = "money")
    @ApiModelProperty("客户续保率   续保客户数/到期客户数*100%")
    private BigDecimal renewalRate;





    @ApiModelProperty("当期总保费")
    private BigDecimal startAmount;

    @ExportField(name = "续保客户保费", order = 10,type = "money")
    @ApiModelProperty("续保客户保费")
    private BigDecimal renewalAmount;

    @ExportField(name = "续保客户保费占比", order = 11,type = "money")
    @ApiModelProperty("续保客户保费占比 续保客户保费/当期总保费*100%")
    private BigDecimal renewalAmountRate;

    @ApiModelProperty("到期信贷客户数")
    private Integer endLoanerCnt;

    @ApiModelProperty("续保信贷客户数")
    private Integer renewalLoanerCnt;

    @ExportField(name = "信贷客户续保率", order = 12,type = "money")
    @ApiModelProperty("信贷客户续保率 续保信贷客户数/保险到期信贷客户数*100%；")
    private BigDecimal loanerRenewalRate;

    @ApiModelProperty("到期信贷相关客户数")
    private Integer endLoancustCnt;

    @ApiModelProperty("续保信贷相关客户数")
    private Integer renewalLoancustCnt;

    @ExportField(name = "信贷相关客户续保率" , order = 13,type = "money")
    @ApiModelProperty("信贷相关客户续保率 续保信贷相关客户数/保险到期信贷相关客户数*100%；")
    private BigDecimal loancustRenewalRate;

    @ExportField(name = "非信贷客户续保率", order = 14,type = "money")
    @ApiModelProperty("非信贷客户续保率 续保非信贷客户数/保险到期非信贷客户数*100%；")
    private BigDecimal noLoanRenewalRate;

    @ExportField(name = "个意险到期客户数", order = 15,type = "integer")
    @ApiModelProperty("个意险到期客户数")
    private Integer personAccidentEnd;
    @ExportField(name = "个意险续保客户数", order = 16,type = "integer")
    @ApiModelProperty("个意险续保客户数")
    private Integer personAccidentRenewal;
    @ExportField(name = "个意险续保率", order = 17,type = "money")
    @ApiModelProperty("个意险续保率")
    private BigDecimal personRenewalRate;
    @ExportField(name = "医疗险到期客户数", order = 18,type = "integer")
    @ApiModelProperty("医疗险到期客户数")
    private Integer medicalEnd;
    @ExportField(name = "医疗险续保客户数", order = 19,type = "integer")
    @ApiModelProperty("医疗险续保客户数")
    private Integer medicalRenewal;
    @ExportField(name = "医疗险续保率", order = 20,type = "money")
    @ApiModelProperty("医疗险续保率")
    private BigDecimal medicalRenewalRate;
    @ExportField(name = "团意险到期客户数", order = 21,type = "integer")
    @ApiModelProperty("团意险到期客户数")
    private Integer groupAccidentEnd;
    @ExportField(name = "团意险续保客户数", order = 22,type = "integer")
    @ApiModelProperty("团意险续保客户数")
    private Integer groupAccidentRenewal;
    @ExportField(name = "团意险续保率", order = 23,type = "money")
    @ApiModelProperty("团意险续保率")
    private BigDecimal groupAccidentRenewalRate;
    @ExportField(name = "责任险到期客户数", order = 24,type = "integer")
    @ApiModelProperty("责任险到期客户数")
    private Integer liabilityEnd;
    @ExportField(name = "责任险续保客户数", order = 25,type = "integer")
    @ApiModelProperty("责任险续保客户数")
    private Integer liabilityRenewal;

    @ExportField(name = "责任险续保率" , order = 26,type = "money")
    @ApiModelProperty("责任险续保率")
    private BigDecimal liabilityRenewalRate;



}
