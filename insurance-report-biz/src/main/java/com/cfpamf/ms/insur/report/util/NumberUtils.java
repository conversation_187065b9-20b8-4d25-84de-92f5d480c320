package com.cfpamf.ms.insur.report.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public class NumberUtils {

    private NumberUtils(){

    }

    public static String wanTransfer(Integer cnt){
        if(Objects.isNull(cnt)){
            return "0";
        }
        BigDecimal val = new BigDecimal(cnt);
        BigDecimal ret = val.divide(BigDecimal.valueOf(10000),1, RoundingMode.DOWN);
        if(ret.compareTo(BigDecimal.ONE) > 0){
            return val.divide(BigDecimal.valueOf(10000),1, RoundingMode.DOWN).toPlainString()+"万";
        }else{
            return val.toPlainString();
        }
    }
}
