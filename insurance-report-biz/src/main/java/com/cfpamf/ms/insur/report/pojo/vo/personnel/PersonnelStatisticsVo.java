package com.cfpamf.ms.insur.report.pojo.vo.personnel;

import com.cfpamf.ms.insur.report.pojo.vo.excel.ExcelVoConvert;
import com.cfpamf.ms.insur.report.pojo.vo.excel.PersonnelStatisticsExcelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

/**
 * 个人明细统计
 *
 * <AUTHOR>
 * @date 2021/3/30 11:06
 */
@Data
public class PersonnelStatisticsVo implements ExcelVoConvert {

    /**
     * 个人信息
     */
    @ApiModelProperty(value = "个人信息")
    private PersonnelVo personnelVo;

    /**
     * 单量统计
     */
    @ApiModelProperty(value = "单量统计")
    private OrderQuantityStatisticsVo orderQuantityStatisticsVo;

    /**
     * 保费统计
     */
    @ApiModelProperty(value = "保费统计")
    private PremiumStatisticsVo premiumStatisticsVo;

    /**
     * 客户转化率
     */
    @ApiModelProperty(value = "客户转化率")
    private CustomerConversionRateVo customerConversionRateVo = new CustomerConversionRateVo();

    /**
     * 非信贷客户统计
     */
    @ApiModelProperty(value = "非信贷客户统计")
    private NoLoanerStatisticsVo noLoanerStatisticsVo = new NoLoanerStatisticsVo();

    /**
     * 续保率
     */
    @ApiModelProperty(value = "续保率")
    private RenewInsuranceRateVo renewInsuranceRateVo = new RenewInsuranceRateVo();

    @Override
    public Object convert() {
        PersonnelStatisticsExcelVo personnelStatisticsExcelVo = new PersonnelStatisticsExcelVo();
        //设置个人信息
        PersonnelVo personnelVo = getPersonnelVo();
        if (Objects.nonNull(personnelVo)) {
            BeanUtils.copyProperties(personnelVo, personnelStatisticsExcelVo);
        }
        //设置单量统计信息
        OrderQuantityStatisticsVo orderQuantityStatisticsVo = getOrderQuantityStatisticsVo();
        if (Objects.nonNull(orderQuantityStatisticsVo)) {
            personnelStatisticsExcelVo.setDayOrderQuantity(orderQuantityStatisticsVo.getDay());
            personnelStatisticsExcelVo.setMonthOrderQuantity(orderQuantityStatisticsVo.getMonth());
            personnelStatisticsExcelVo.setYearOrderQuantity(orderQuantityStatisticsVo.getYear());
        }
        //设置保费信息
        PremiumStatisticsVo premiumStatisticsVo = getPremiumStatisticsVo();
        if (Objects.nonNull(premiumStatisticsVo)) {
            personnelStatisticsExcelVo.setDayPremium(premiumStatisticsVo.getDay());
            personnelStatisticsExcelVo.setMonthPremium(premiumStatisticsVo.getMonth());
            personnelStatisticsExcelVo.setYearPremium(premiumStatisticsVo.getYear());
        }
        //设置非信贷客户信息
        NoLoanerStatisticsVo noLoanerStatisticsVo = getNoLoanerStatisticsVo();
        if (Objects.nonNull(noLoanerStatisticsVo)) {
            personnelStatisticsExcelVo.setMonthPremiumNoLoaner(noLoanerStatisticsVo.getMonthPremium());
            personnelStatisticsExcelVo.setYearPremiumNoLoaner(noLoanerStatisticsVo.getYearPremium());
            personnelStatisticsExcelVo.setMonthPremiumProportionNoLoaner(noLoanerStatisticsVo.getMonthPremiumProportion());
            personnelStatisticsExcelVo.setYearPremiumProportionNoLoaner(noLoanerStatisticsVo.getYearPremiumProportion());
            personnelStatisticsExcelVo.setCurrentMonthProportionNoLoaner(noLoanerStatisticsVo.getCurrentMonthProportion());
        }
        //设置转化率信息
        CustomerConversionRateVo customerConversionRateVo = getCustomerConversionRateVo();
        if (Objects.nonNull(customerConversionRateVo)) {
            personnelStatisticsExcelVo.setLoanerConversionRate(customerConversionRateVo.getLoaner());
            personnelStatisticsExcelVo.setLoanerRelevantConversionRate(customerConversionRateVo.getLoanerRelevant());
        }
        //设置续保率信息
        RenewInsuranceRateVo renewInsuranceRateVo = getRenewInsuranceRateVo();
        if (Objects.nonNull(renewInsuranceRateVo)) {
            personnelStatisticsExcelVo.setYearRenewInsuranceRate(renewInsuranceRateVo.getYear());
            personnelStatisticsExcelVo.setMonthRenewInsuranceRate(renewInsuranceRateVo.getMonth());
        }
        return personnelStatisticsExcelVo;
    }
}
