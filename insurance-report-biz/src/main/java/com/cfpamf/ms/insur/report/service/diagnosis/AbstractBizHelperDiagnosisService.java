package com.cfpamf.ms.insur.report.service.diagnosis;


import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.config.FreemarkerConfig;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.enums.OrgLevelEnum;
import com.cfpamf.ms.insur.report.feign.resp.vo.OrganizationBaseVO;
import com.cfpamf.ms.insur.report.feign.resp.vo.OrganizationParentVo;
import com.cfpamf.ms.insur.report.service.BmsService;
import com.cfpamf.ms.insur.report.service.diagnosis.model.AbstractDiagnosisModel;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.StringWriter;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 保险业务助手中诊断与结论的抽象类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024年03月03日
 */
public abstract class AbstractBizHelperDiagnosisService implements BizHelperDiagnosisService {

    protected static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("0.00");

    protected volatile Template template;

    @Resource
    protected BmsService bmsService;

    /**
     * 实现类的初始化实现
     *
     * @throws IOException
     */
    @PostConstruct
    public void init() throws IOException {
        createTemplate();
    }

    /**
     * 创建freemarker的Template实例的对象
     *
     * @throws IOException
     */
    protected void createTemplate() throws IOException {
        this.template = FreemarkerConfig.getConfiguration().getTemplate(getTemplateFilePath());
    }

    /**
     * 获取受支持的诊断类型
     *
     * @return 诊断类型的枚举类
     */
    public abstract DiagnosisTypeEnum getSupportDiagnosisType();

    /**
     * 根据请求参数，创建诊断的数据模型
     *
     * @param request 请求参数
     * @return 诊断的数据模型
     */
    protected abstract AbstractDiagnosisModel createModel(DiagnosisRequest request);

    /**
     * 获取诊断模板文件的地址
     *
     * @return 模板文件的地址
     */
    protected abstract String getTemplateFilePath();

    /**
     * 根据诊断的数据模型，渲染诊断数据
     *
     * @param model 数据模型
     * @return 渲染后的诊断结论
     */
    protected String renderData(AbstractDiagnosisModel model) {
        String result = null;
        try (StringWriter stringWriter = new StringWriter()) {
            this.template.process(model, stringWriter);
            result = stringWriter.toString();
        } catch (IOException | TemplateException e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    /**
     * 进行业务诊断
     *
     * @return 诊断的结果，返回的格式是前端的html格式数据。
     */
    @Override
    public String diagnosis(DiagnosisRequest request) {
        verifyParametersAndDataPermissions(request);
        AbstractDiagnosisModel model = createModel(request);
        if (model == null) {
            return "";
        }
        return renderData(model);
    }

    /**
     * 校验参数合法性与数据权限合法性
     *
     * @param request 请求参数
     * @throws MSBizNormalException 当参数校验未通过，或者数据权限校验未通过，都将抛出此异常
     */
    protected void verifyParametersAndDataPermissions(DiagnosisRequest request) throws MSBizNormalException {
        if (StringUtils.isBlank(request.getPt())) {
            request.setPt(BaseConstants.FMT_YYYYMMDD.format(LocalDate.now().minusDays(1)));
        } else {
            LocalDate ptLocalDate = null;
            try {
                ptLocalDate = LocalDate.parse(request.getPt(), BaseConstants.FMT_YYYYMMDD);
            } catch (DateTimeParseException e) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "传入的时间分区参数格式非法！", e);
            }
            LocalDate today = LocalDate.now();
            if (today.isBefore(ptLocalDate) || ptLocalDate.isBefore(today.minusDays(7))) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "传入的时间分区参数非法！");
            }
        }
        if (request.getDiagnosisType() != getSupportDiagnosisType()) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "传入的业务诊断类型和实现的类型不匹配！");
        }
        OrgLevelEnum orgLevel = request.getOrgLevel();
        if (orgLevel == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "传入的组织级别不能为空！");
        }
        List<String> orgCodes = request.getOrgCodes();
        switch (orgLevel) {
            case COUNTRY: {
                // 对于全国的数据权限校验，只要用户没有指定的区域或者分支即可查看，否则则不能查看。
                if (StringUtils.isNotBlank(request.getAreaName()) || StringUtils.isNotBlank(request.getBranchName())) {
                    throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(), "您没有查看全国数据的权限！");
                }
                break;
            }
            case AREA: {
                if (CollectionUtils.isEmpty(orgCodes) || orgCodes.size() != 1) {
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "需要查看数据的区域只支持单选！");
                }
                if (StringUtils.isBlank(request.getAreaName()) && StringUtils.isBlank(request.getBranchName())) {
                    return;
                }
                List<OrganizationParentVo> parentOrganizations = bmsService.getParentOrganizationsByOrgCodes(orgCodes);
                if (CollectionUtils.isEmpty(parentOrganizations)) {
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "当前组织不存在，请重新选择！");
                }
                for (OrganizationParentVo parentOrganization : parentOrganizations) {
                    if (!Objects.equals(parentOrganization.getOrgName(), request.getAreaName())) {
                        throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(), "您没有查看该区域数据的权限！");
                    }
                }
                break;
            }
            case DISTRICT: {
                if (CollectionUtils.isEmpty(orgCodes)) {
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "需要查看的片区信息不能为空！");
                }
                if (StringUtils.isBlank(request.getAreaName()) && StringUtils.isBlank(request.getBranchName())) {
                    return;
                }
                List<OrganizationParentVo> parentOrganizations = bmsService.getParentOrganizationsByOrgCodes(orgCodes);
                if (CollectionUtils.isEmpty(parentOrganizations)) {
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "当前组织不存在，请重新选择！");
                }
                for (String orgCode : orgCodes) {
                    boolean districtCheck = false;
                    boolean areaCheck = false;
                    for (OrganizationParentVo parentOrganization : parentOrganizations) {
                        if (!Objects.equals(orgCode, parentOrganization.getOrgCode())) {
                            continue;
                        }
                        districtCheck = true;
                        List<OrganizationParentVo> superOrganizations = parentOrganization.getSuperOrganizations();
                        if (CollectionUtils.isEmpty(superOrganizations)) {
                            throw new MSBizNormalException(ExcptEnum.DATA_ERROR_801304.getCode(), "数据异常，找不到片区对应的区域信息！");
                        }
                        for (OrganizationParentVo superOrganization : superOrganizations) {
                            if (superOrganization.getOrgCategory() == null) {
                                continue;
                            }
                            if (superOrganization.getOrgCategory() == 4) {
                                if (!Objects.equals(superOrganization.getOrgName(), request.getAreaName())) {
                                    throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(), "您没有查看该区域数据的权限！");
                                }
                                areaCheck = true;
                                break;
                            }
                        }
                    }
                    if (!districtCheck) {
                        throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(), "您没有查看该片区数据的权限！");
                    }
                    if (!areaCheck) {
                        throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(), "您没有查看该区域数据的权限！");
                    }
                }
                break;
            }
            case BRANCH: {
                if (CollectionUtils.isEmpty(orgCodes) || orgCodes.size() != 1) {
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "需要查看数据的分支只支持单选！");
                }
                if (StringUtils.isBlank(request.getAreaName()) && StringUtils.isBlank(request.getBranchName())) {
                    return;
                }
                List<OrganizationParentVo> parentOrganizations = bmsService.getParentOrganizationsByOrgCodes(orgCodes);
                if (CollectionUtils.isEmpty(parentOrganizations)) {
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "当前组织不存在，请重新选择！");
                }
                for (OrganizationParentVo parentOrganization : parentOrganizations) {
                    if (StringUtils.isNotBlank(request.getBranchName())) {
                        if (!Objects.equals(parentOrganization.getOrgName(), request.getBranchName()) &&
                                !Objects.equals(parentOrganization.getShortName(), request.getBranchName()) &&
                                !Objects.equals(parentOrganization.getOrgCode(), request.getOrgCodes().get(0))
                        ) {
                            throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(), "您没有查看该分支数据的权限！");
                        }
                    }
                    if (StringUtils.isNotBlank(request.getAreaName())) {
                        List<OrganizationParentVo> superOrganizations = parentOrganization.getSuperOrganizations();
                        if (CollectionUtils.isEmpty(superOrganizations)) {
                            throw new MSBizNormalException(ExcptEnum.DATA_ERROR_801304.getCode(), "数据异常，找不到分支对应的区域信息！");
                        }
                        boolean areaCheck = false;
                        for (OrganizationParentVo superOrganization : superOrganizations) {
                            if (superOrganization.getOrgCategory() == null) {
                                continue;
                            }
                            if (superOrganization.getOrgCategory() == 4) {
                                if (!Objects.equals(superOrganization.getOrgName(), request.getAreaName())) {
                                    throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(), "您没有查看该区域数据的权限！");
                                }
                                areaCheck = true;
                                break;
                            }
                        }
                        if (!areaCheck) {
                            throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(), "您没有查看该分支数据的权限！");
                        }
                    }
                }
                break;
            }
            default:
                break;
        }
    }
}
