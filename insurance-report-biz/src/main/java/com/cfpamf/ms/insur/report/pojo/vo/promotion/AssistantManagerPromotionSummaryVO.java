package com.cfpamf.ms.insur.report.pojo.vo.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AssistantManagerPromotionSummaryVO {
    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "当月分享数", notes = "")
    String smShareCnt;
    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "当月访客数", notes = "")
    String smVisitCnt;
    /**
     * 当月转化保单数
     */
    @ApiModelProperty(value = "当月转化保单数", notes = "")
    String smTransformPolicyCnt;
    /**
     * 当日分享数
     */
    @ApiModelProperty(value = "当日分享数", notes = "")
    String sdShareCnt;
    /**
     * 当日访客数
     */
    @ApiModelProperty(value = "当日访客数", notes = "")
    String sdVisitCnt;

    /**
     * 当日分享数
     */
    @ApiModelProperty(value = "昨日分享数", notes = "")
    String cdShareCnt;
    /**
     * 当日访客数
     */
    @ApiModelProperty(value = "昨日访客数", notes = "")
    String cdVisitCnt;
}
