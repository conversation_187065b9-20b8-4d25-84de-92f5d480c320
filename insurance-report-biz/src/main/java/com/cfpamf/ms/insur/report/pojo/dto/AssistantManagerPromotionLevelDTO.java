package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@ApiModel(value = "保险管理端推广分支/员工明细对象", description = "")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantManagerPromotionLevelDTO {

    @ApiModelProperty(value = "当月层级详细列表", notes = "")
    List<AssistantManagerPromotionLevelDetail> levelDetailList;

    @ApiModelProperty("nation 全国, area 区域, district 片区, bch 分支")
    String levelType;

    @ApiModelProperty("总数")
    Integer total;
}
