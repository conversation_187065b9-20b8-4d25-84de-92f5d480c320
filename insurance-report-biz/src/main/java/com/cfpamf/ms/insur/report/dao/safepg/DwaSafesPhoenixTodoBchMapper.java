package com.cfpamf.ms.insur.report.dao.safepg;

import com.cfpamf.ms.insur.report.service.diagnosis.RpNormInsuranceAmtAssistantService;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 分支待办指标;(dwa_safes_phoenix_todo_bch)表数据库访问层
 * <AUTHOR> lww
 */
@Mapper
public interface DwaSafesPhoenixTodoBchMapper {

    /**
     * 查询续保待办相关指标数据
     * @param pt 分区
     * @param orgCodes 机构编码
     * @return 机构续保待办相关指标数据
     */
    RpNormInsuranceAmtAssistantService.RenewPhoenixTodoSummaryVo queryBchRenewTodoSummary(String pt, List<String> orgCodes);
}