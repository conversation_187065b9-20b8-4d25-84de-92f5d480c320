package com.cfpamf.ms.insur.report.dao.safes;


import com.cfpamf.ms.insur.report.pojo.vo.OrgPicExtraVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * <p>
 * 机构对接人信息Mapper
 **/
@Mapper
public interface OrgPicExtraMapper {

    /**
     * 通过工号查询机构创新业务对接人
     *
     * @param userId
     * @return
     */
    OrgPicExtraVO getOrgPicExtraByUserId(@Param("userId") String userId);

    /**
     * 通过工号查询机构创新业务对接人
     *
     * @param userId
     * @return
     */
    OrgPicExtraVO getOrgPicExtraByUserIdAndOrg(@Param("userId") String userId,
                                               @Param("orgCode") String orgCode);
}
