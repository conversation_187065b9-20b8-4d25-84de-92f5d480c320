package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 机构负责人信息表
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
public class OrgPicExtraDTO {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 区域hrId
     */
    @ApiModelProperty(hidden = true)
    private String regionHrId;

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    private String regionName;

    /**
     * 分支hrId
     */
    @ApiModelProperty(hidden = true)
    private String orgHrId;

    /**
     * 分支名称
     */
    @ApiModelProperty("分支名称")
    private String orgName;

    /**
     * 员工工号
     */
    @ApiModelProperty("员工工号")
    private String userId;

    /**
     * 员工名称
     */
    @ApiModelProperty("员工名称")
    private String userName;

    /**
     * 职务
     */
    @ApiModelProperty("职务")
    private String userJobPost;

    /**
     * 岗位编码
     */
    @ApiModelProperty("岗位编码")
    private String jobCode;

    /**
     * 组织机构编码
     */
    @ApiModelProperty("组织机构编码")
    private String orgCode;

    /**
     * 主工号
     */
    @ApiModelProperty("主工号")
    private String mainJobNumber;
}
