package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OrgPromoFeeVO extends PromoFeeVO implements Serializable {
    @ExportField(name = "负责人姓名",order = 4)
    @ApiModelProperty("负责人姓名")
    private String userName;

    @ExportField(name = "负责人工号",order = 5)
    @ApiModelProperty("负责人工号")
    private String userId;

    @ApiModelProperty("保单绩效")
    private BigDecimal rewardAmount;

    @ApiModelProperty("退保绩效")
    private BigDecimal surrenderRewardAmount;

    @ExportField(name = "保险绩效奖励" ,order = 10,type = "money")
    @ApiModelProperty("保险绩效奖励")
    private BigDecimal promoFee;
}
