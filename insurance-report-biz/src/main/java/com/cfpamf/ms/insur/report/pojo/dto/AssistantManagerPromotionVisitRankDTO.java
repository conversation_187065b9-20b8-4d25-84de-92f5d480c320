package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@ApiModel(value = "保险管理端推广指标排名对象", description = "")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantManagerPromotionVisitRankDTO {
    public AssistantManagerPromotionVisitRankDTO(Integer rank){
        this.rank = rank;
    }

    @ApiModelProperty(value = "层级名称", notes = "")
    String name="名称";

    @ApiModelProperty(value = "当月访客数", notes = "")
    String smVisitCnt="2万";

    @ApiModelProperty(value = "当月人均访客数", notes = "")
    String smVisitAvg="100";

    /**
     * 当月分享数
     */
    @ApiModelProperty(value = "排名", notes = "")
    Integer rank;
}
