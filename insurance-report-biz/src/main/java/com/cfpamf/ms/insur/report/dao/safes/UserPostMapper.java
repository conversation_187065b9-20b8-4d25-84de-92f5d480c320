package com.cfpamf.ms.insur.report.dao.safes;


import com.cfpamf.ms.insur.report.pojo.dto.EmpStatus;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * <p>
 * 机构对接人信息Mapper
 **/
@Mapper
public interface UserPostMapper {

    /**
     * 通过工号 查询所有员工状态
     *
     * @return
     */
    @MapKey("mainJobNumber")
    Map<String,EmpStatus> listStatusByJobNumbers(@Param("jobNumbers")List<String> jobNumbers);
}
