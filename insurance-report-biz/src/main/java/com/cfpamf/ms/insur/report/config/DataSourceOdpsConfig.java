package com.cfpamf.ms.insur.report.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.MybatisProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 数仓odps项目数据连接
 * 同一个账号可以连接不同的项目，如digitaltech、opmgt、cdfinance都可以用同一个账号连接
 * 数据库配置任意一个即可，语句中需要写项目前缀，如 digitaltech.ods__cmis__applicationphoto
 */
@Configuration
@MapperScan(basePackages = "com.cfpamf.ms.insur.report.dao.odps", sqlSessionFactoryRef = "odpsSqlSessionFactory")
@EnableConfigurationProperties(MybatisProperties.class)
public class DataSourceOdpsConfig {
    static final String PACKAGE = "com.cfpamf.ms.insur.report.dao.odps";
    static final String MAPPER_LOCATION = "classpath:mapper/odps/lookback/*.xml";

    static final String PREFIX = "odps";

    final
    MybatisProperties properties;

    @Autowired(required = false)
    public DataSourceOdpsConfig(MybatisProperties properties) {
        this.properties = properties;
    }

    @Primary
    @ConfigurationProperties(prefix = "spring.datasource." + PREFIX)
    @Bean(name = PREFIX + "DataSource")
    public DataSource odpsDataSource() {
        return new DruidDataSource();
    }

    @Primary
    @Bean(name = PREFIX + "SqlSessionFactory")
    public SqlSessionFactory sqlSessionFactoryEngine(@Qualifier(PREFIX + "DataSource") DataSource dataSource) throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        applyConfiguration(sessionFactory);
        Resource[] resources = new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION);
        sessionFactory.setMapperLocations(resources);

        return sessionFactory.getObject();
    }

    @Primary
    @Bean(name = PREFIX + "DataSourceTransactionManager")
    public DataSourceTransactionManager dataSourceTransactionManagerEngine(@Qualifier(PREFIX + "DataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Primary
    @Bean(name = PREFIX + "SqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplateEngine(@Qualifier(PREFIX + "SqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    private void applyConfiguration(SqlSessionFactoryBean factory) {
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        factory.setConfiguration(configuration);
    }
}
