package com.cfpamf.ms.insur.report.pojo.vo.region;

import com.cfpamf.ms.insur.report.pojo.vo.branch.CompletionRateVo;
import com.cfpamf.ms.insur.report.pojo.vo.branch.PerCapitaPremiumVo;
import com.cfpamf.ms.insur.report.pojo.vo.branch.PremiumTargetVo;
import com.cfpamf.ms.insur.report.pojo.vo.branch.StaffCountVo;
import com.cfpamf.ms.insur.report.pojo.vo.excel.ExcelVoConvert;
import com.cfpamf.ms.insur.report.pojo.vo.excel.RegionStatisticsExcelVo;
import com.cfpamf.ms.insur.report.pojo.vo.personnel.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

/**
 * 区域业绩明细统计
 *
 * <AUTHOR>
 * @date 2021/3/31 14:48
 */
@Data
public class RegionStatisticsVo implements ExcelVoConvert {

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    private String region;

    /**
     * 员工人数统计
     */
    @ApiModelProperty(value = "员工人数")
    private StaffCountVo staffCountVo;

    /**
     * 单量统计
     */
    @ApiModelProperty(value = "单量统计")
    private OrderQuantityStatisticsVo orderQuantityStatisticsVo;

    /**
     * 保费统计
     */
    @ApiModelProperty(value = "保费统计")
    private PremiumStatisticsVo premiumStatisticsVo;
    /**
     * 保费目标
     */
    @ApiModelProperty(value = "保费目标")
    private PremiumTargetVo premiumTargetVo = new PremiumTargetVo();

    /**
     * 保费目标完成率
     */
    @ApiModelProperty(value = "保费目标完成率")
    private CompletionRateVo completionRateVo = new CompletionRateVo();

    /**
     * 月度人均保费统计
     */
    @ApiModelProperty(value = "月度人均保费")
    private PerCapitaPremiumVo perCapitaPremiumVo = new PerCapitaPremiumVo();

    /**
     * 非信贷客户统计
     */
    @ApiModelProperty(value = "非信贷客户")
    private NoLoanerStatisticsVo noLoanerStatisticsVo = new NoLoanerStatisticsVo();

    /**
     * 续保率统计
     */
    @ApiModelProperty(value = "续保率")
    private RenewInsuranceRateVo renewInsuranceRateVo = new RenewInsuranceRateVo();

    /**
     * 客户转化率
     */
    @ApiModelProperty(value = "客户转化率")
    private CustomerConversionRateVo customerConversionRateVo = new CustomerConversionRateVo();

    /**
     * 放款金额
     */
    @ApiModelProperty(value = "放款金额")
    private LoanAmountVo loanAmountVo = new LoanAmountVo();

    @Override
    public Object convert() {
        RegionStatisticsExcelVo regionStatisticsExcelVo = new RegionStatisticsExcelVo();
        //设置基础信息
        BeanUtils.copyProperties(this, regionStatisticsExcelVo);
        //设置单量统计信息
        OrderQuantityStatisticsVo orderQuantityStatisticsVo = getOrderQuantityStatisticsVo();
        if (Objects.nonNull(orderQuantityStatisticsVo)) {
            regionStatisticsExcelVo.setDayOrderQuantity(orderQuantityStatisticsVo.getDay());
            regionStatisticsExcelVo.setMonthOrderQuantity(orderQuantityStatisticsVo.getMonth());
            regionStatisticsExcelVo.setYearOrderQuantity(orderQuantityStatisticsVo.getYear());
        }

        //设置在职人数信息
        StaffCountVo staffCountVo = getStaffCountVo();
        if (Objects.nonNull(staffCountVo)) {
            regionStatisticsExcelVo.setMonthJobStaffCount(staffCountVo.getMonthJobStaffCount());
            regionStatisticsExcelVo.setOrganizationStaffCount(staffCountVo.getOrganizationStaffCount());
        }
        //设置保费信息
        PremiumStatisticsVo premiumStatisticsVo = getPremiumStatisticsVo();
        if (Objects.nonNull(premiumStatisticsVo)) {
            regionStatisticsExcelVo.setDayPremium(premiumStatisticsVo.getDay());
            regionStatisticsExcelVo.setMonthPremium(premiumStatisticsVo.getMonth());
            regionStatisticsExcelVo.setYearPremium(premiumStatisticsVo.getYear());
        }
        //设置非信贷客户信息
        NoLoanerStatisticsVo noLoanerStatisticsVo = getNoLoanerStatisticsVo();
        if (Objects.nonNull(noLoanerStatisticsVo)) {
            regionStatisticsExcelVo.setMonthPremiumNoLoaner(noLoanerStatisticsVo.getMonthPremium());
            regionStatisticsExcelVo.setYearPremiumNoLoaner(noLoanerStatisticsVo.getYearPremium());
            regionStatisticsExcelVo.setMonthPremiumProportionNoLoaner(noLoanerStatisticsVo.getMonthPremiumProportion());
            regionStatisticsExcelVo.setYearPremiumProportionNoLoaner(noLoanerStatisticsVo.getYearPremiumProportion());
            regionStatisticsExcelVo.setCurrentMonthProportionNoLoaner(noLoanerStatisticsVo.getCurrentMonthProportion());
        }
        //设置转化率信息
        CustomerConversionRateVo customerConversionRateVo = getCustomerConversionRateVo();
        if (Objects.nonNull(customerConversionRateVo)) {
            regionStatisticsExcelVo.setLoanerConversionRate(customerConversionRateVo.getLoaner());
            regionStatisticsExcelVo.setLoanerRelevantConversionRate(customerConversionRateVo.getLoanerRelevant());
        }
        //设置续保率信息
        RenewInsuranceRateVo renewInsuranceRateVo = getRenewInsuranceRateVo();
        if (Objects.nonNull(renewInsuranceRateVo)) {
            regionStatisticsExcelVo.setYearRenewInsuranceRate(renewInsuranceRateVo.getYear());
            regionStatisticsExcelVo.setMonthRenewInsuranceRate(renewInsuranceRateVo.getMonth());
        }
        //设置保费目标
        PremiumTargetVo premiumTargetVo = getPremiumTargetVo();
        if (Objects.nonNull(premiumTargetVo)) {
            regionStatisticsExcelVo.setCurrentMonthPremiumTarget(premiumTargetVo.getCurrentMonth());
            regionStatisticsExcelVo.setCurrentYearPremiumTarget(premiumTargetVo.getCurrentYear());
            regionStatisticsExcelVo.setPresentCurrentMonthPremiumTarget(premiumTargetVo.getPresentCurrentMonth());
        }
        //设置目标完成率
        CompletionRateVo completionRateVo = getCompletionRateVo();
        if (Objects.nonNull(completionRateVo)) {
            regionStatisticsExcelVo.setCurrentMonthCompletionRate(completionRateVo.getCurrentMonth());
            regionStatisticsExcelVo.setCurrentYearCompletionRate(completionRateVo.getCurrentYear());
            regionStatisticsExcelVo.setPresentCurrentMonthCompletionRate(completionRateVo.getPresentCurrentMonth());
        }
        //设置人均保费
        PerCapitaPremiumVo perCapitaPremiumVo = getPerCapitaPremiumVo();
        if (Objects.nonNull(perCapitaPremiumVo)) {
            regionStatisticsExcelVo.setCurrentMonthJobStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentMonthJobStaff());
            regionStatisticsExcelVo.setCurrentYearJobStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentYearJobStaff());
            regionStatisticsExcelVo.setCurrentMonthOrganizationStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentMonthOrganizationStaff());
            regionStatisticsExcelVo.setCurrentYearOrganizationStaffPerCapitaPremium(perCapitaPremiumVo.getCurrentYearOrganizationStaff());
        }
        //设置放款金额
        LoanAmountVo loanAmountVo = getLoanAmountVo();
        if (Objects.nonNull(loanAmountVo)) {
            regionStatisticsExcelVo.setCurrentMonthOffline(loanAmountVo.getCurrentMonthOffline());
            regionStatisticsExcelVo.setCurrentYearOffline(loanAmountVo.getCurrentYearOffline());
            regionStatisticsExcelVo.setCurrentMonthOfflinePremiumProportion(loanAmountVo.getCurrentMonthOfflinePremiumProportion());
            regionStatisticsExcelVo.setCurrentYearOfflinePremiumProportion(loanAmountVo.getCurrentYearOfflinePremiumProportion());

            regionStatisticsExcelVo.setCurrentMonth(loanAmountVo.getCurrentMonth());
            regionStatisticsExcelVo.setCurrentYear(loanAmountVo.getCurrentYear());
            regionStatisticsExcelVo.setCurrentMonthPremiumProportion(loanAmountVo.getCurrentMonthPremiumProportion());
            regionStatisticsExcelVo.setCurrentYearPremiumProportion(loanAmountVo.getCurrentYearPremiumProportion());
        }
        return regionStatisticsExcelVo;
    }
}
