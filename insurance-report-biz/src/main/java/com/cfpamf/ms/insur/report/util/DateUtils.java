package com.cfpamf.ms.insur.report.util;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

@Slf4j
public class DateUtils {

    public static final String YYYYMMDD = "yyyyMMdd";

    public static final String YYYYMM = "yyyyMM";

    public static final String STANDARD_DATE_TIME = "yyyy-MM-dd";

    private DateUtils(){}

    public static String getCurrentDay() {
        SimpleDateFormat sf = new SimpleDateFormat(YYYYMMDD);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR,-1);
        return sf.format(calendar.getTime());
    }

    public static String getLastDay() {
        SimpleDateFormat sf = new SimpleDateFormat(YYYYMMDD);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR,-1);
        return sf.format(calendar.getTime());
    }

    public static String getLastMonthDay() {
        YearMonth yearMonth = YearMonth.now(); // 获取当前年月
        yearMonth = yearMonth.plusMonths(-1);
        LocalDate lastDayOfNextMonth = yearMonth.atEndOfMonth();
        log.info("getLastMonthDay= {}",lastDayOfNextMonth);
        return lastDayOfNextMonth.toString().replaceAll("-*","");
    }

    public static String getCurrentMonthFirstDay() {
        SimpleDateFormat sf = new SimpleDateFormat(YYYYMM);
        return sf.format(new Date())+"01";
    }

    public static String getCurrentMinDateTime() {
        SimpleDateFormat sf = new SimpleDateFormat(STANDARD_DATE_TIME);
        return sf.format(new Date())+" 00:00:00";
    }

    /**
     * 根据当前pt获取上月最后一天
     * @param pt
     * @return
     */
    public static String getLastMonthDay(String pt) {
        Integer year = null;
        Integer month = null;
        try{
            year = Integer.valueOf(pt.substring(0,4));
            month = Integer.valueOf(pt.substring(4,6));
        }catch(Exception e){
            log.error("pt不合法：{}",pt,e);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "pt不合法：" + pt);
        }
        YearMonth yearMonth = YearMonth.of(year,month); // 获取当前年月
        yearMonth = yearMonth.plusMonths(-1);
        LocalDate lastDayOfNextMonth = yearMonth.atEndOfMonth();
        log.info("getLastMonthDay= {}",lastDayOfNextMonth);
        return lastDayOfNextMonth.toString().replaceAll("-*","");
    }
}
