package com.cfpamf.ms.insur.report.dao.dw;

import com.cfpamf.ms.insur.report.pojo.dto.InsuranceOrderAllStatDTO;
import com.cfpamf.ms.insur.report.pojo.query.*;
import com.cfpamf.ms.insur.report.pojo.vo.*;
import com.cfpamf.ms.insur.report.pojo.vo.branch.BranchStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.personnel.PersonnelStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.personnel.PersonnelTransferRateVo;
import com.cfpamf.ms.insur.report.pojo.vo.region.RegionStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.total.TotalStatisticsVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SmDcReportMapper {
    /**
     * 个人业绩报表
     *
     * @param reportQuery
     * @return
     */
    List<PersonalBusinessVO> listSmPersonalBusinessDetails(ReportQuery reportQuery);

    /**
     * 机构业绩报表
     *
     * @param reportQuery
     * @return
     */
    List<OrgBusinessVO> listSmOrgBusinessDetails(ReportQuery reportQuery);

    /**
     * 机构业绩报表
     *
     * @param reportQuery
     * @return
     */
    List<RegionBusinessVO> listSmRegionBusinessDetails(ReportQuery reportQuery);

    /**
     * 续保率汇总
     *
     * @param reportQuery
     * @return
     */
    List<RenewalRateVO> listRenewalRate(RenewalRateQuery reportQuery);

    /**
     * 个人续保率
     *
     * @param reportQuery
     * @return
     */
    List<PersonalRenewalRateVO> listPersonalRenewalRate(RenewalRateQuery reportQuery);

    /**
     * 机构续保率
     *
     * @param reportQuery
     * @return
     */
    List<OrgRenewalRateVO> listOrgRenewalRate(RenewalRateQuery reportQuery);

    /**
     * 区域续保率
     *
     * @param reportQuery
     * @return
     */
    List<RegionRenewalRateVO> listRegionRenewalRate(RenewalRateQuery reportQuery);

    /**
     * 个人推广费
     *
     * @param reportQuery
     * @return
     */
    List<PersonalPromoFeeVO> listPersonalPromoFee(PromoFeeQuery reportQuery);

    /**
     * 个人推广费2021
     *
     * @param reportQuery
     * @return
     */
    List<PersonalPromoFeeVO> searchPersonalPromoFee(PromoFeeQuery reportQuery);

    /**
     * 机构绩效
     *
     * @param reportQuery
     * @return
     */
    List<OrgPromoFeeVO> listOrgPromoFee(PromoFeeQuery reportQuery);

    /**
     * 业务排名
     *
     * @return
     */
    List<BusinessRankVO> listOrgBusinessRank(ReportQuery query);

    /**
     * 获取基础报表T-1日日数据、月数据、年数据
     *
     * @return
     */
    InsuranceOrderAllStatDTO getInsuranceOrderAllStat();

    /**
     * 搜索个人业绩明细
     *
     * @param performanceReportQuery
     * @return
     */
    List<PersonnelStatisticsVo> searchPersonalPerformance(PerformanceReportQuery performanceReportQuery);

    /**
     * 搜索机构业绩明细
     *
     * @param performanceReportQuery
     * @return
     */
    List<BranchStatisticsVo> searchBranchPerformance(PerformanceReportQuery performanceReportQuery);

    /**
     * 机构佣金报表
     *
     * @param query
     * @return
     */
    List<SmOrgCommissionReportVO> listOrgCommission(ReportQuery query);

    /**
     * 搜索区域业绩明细
     *
     * @param performanceReportQuery
     * @return
     */
    List<RegionStatisticsVo> searchRegionPerformance(PerformanceReportQuery performanceReportQuery);

    /**
     * 搜索汇总业绩明细
     *
     * @param performanceReportQuery
     * @return
     */
    List<TotalStatisticsVo> searchTotalPerformance(PerformanceReportQuery performanceReportQuery);

    /**
     * 个人转换率查询
     *
     * @param wxReportQuery
     * @return
     */
    List<PersonnelTransferRateVo> searchPersonnelTransferRate(WxReportQuery wxReportQuery);
}
