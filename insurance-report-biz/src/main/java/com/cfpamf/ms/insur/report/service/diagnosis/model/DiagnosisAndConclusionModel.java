package com.cfpamf.ms.insur.report.service.diagnosis.model;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 总的诊断与结论
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/02/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DiagnosisAndConclusionModel extends AbstractDiagnosisModel {

    /**
     * 结论
     */
    private Conclusion conclusion;
    /**
     * 待优化项，有可能为empty
     */
    private List<MajorItem> majorItems = new ArrayList<>(2);

    public void addMajorItem(MajorItem item) {
        majorItems.add(item);
    }

    /**
     * 结论类
     */
    @Data
    public static class Conclusion {
        /**
         * 组织类型，取值可以参见{@link com.cfpamf.ms.insur.report.enums.OrgLevelEnum}
         */
        private String type; // 区域类型：COUNTRY,AREA,DISTRICT,BRANCH
        /**
         * 标准保费当年目标达成率
         */
        private String classicInsuranceAmountYearRate;
        /**
         * 标准保费当年全国排名
         */
        private Integer classicInsuranceAmountYearCountryRank;
        /**
         * 标准保费当年区域排名
         */
        private Integer classicInsuranceAmountYearAreaRank;
        /**
         * 标准保费环比是否有值
         */
        private boolean classicInsuranceAmountMonthOnMonthHasValue = false;
        /**
         * 标准保费当月目标达成率
         */
        private String classicInsuranceAmountMonthRate;
        /**
         * 标准保费环比
         */
        private String classicInsuranceAmountMonthOnMonth;
        /**
         * 标准保费环比趋势，false为降，true为升
         */
        private boolean classicInsuranceAmountMonthOnMonthTrend = false;
        /**
         * 标准保费目标是否达成
         */
        private boolean finished;
        /**
         * 相关TOP3的组织/机构/个人名称
         */
        private List<String> rankNames;
    }

    @Data
    public static class MajorItem {
        /**
         * 优化项的类型：INSURANCE_AMOUNT_RATE,RETENTION_RATE
         */
        private String type;
        /**
         * 异业保费配比
         */
        private String insuranceAmountRate;
        /**
         * 异业保费配比全国标准水平
         */
        private String insuranceAmountRateTarget;
        /**
         * 客户留存率
         */
        private String retentionRate;
        /**
         * 客户留存率
         */
        private String retentionRateTarget;
        /**
         * 相关TOP3的组织/机构/个人名称
         */
        private List<String> rankNames;

    }


}
