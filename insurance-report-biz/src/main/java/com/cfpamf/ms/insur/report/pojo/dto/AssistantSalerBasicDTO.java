package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@ApiModel(value = "保险组手员工指标", description = "")
public class AssistantSalerBasicDTO {
    @ApiModelProperty(value = "员工姓名", notes = "")
    private String empName;
    /**
     * 工号
     */
    @ApiModelProperty(value = "工号", notes = "")
    private String empCode;
    /**
     * 分支
     */
    @ApiModelProperty(value = "分支", notes = "")
    private String bchName;
    /**
     * 分支编码
     */
    @ApiModelProperty(value = "分支编码", notes = "")
    private String bchCode;
    /**
     * 片区
     */
    @ApiModelProperty(value = "片区", notes = "")
    private String districtName;
    /**
     * 片区编码
     */
    @ApiModelProperty(value = "片区编码", notes = "")
    private String districtCode;
    /**
     * 区域
     */
    @ApiModelProperty(value = "区域", notes = "")
    private String areaName;
    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码", notes = "")
    private String areaCode;
    /**
     * 当月标准保费
     */
    @ApiModelProperty(value = "当月标准保费", notes = "")
    private Double smAssessConvertInsuranceAmt;
    /**
     * 当年标准保费
     */
    @ApiModelProperty(value = "当年标准保费", notes = "")
    private Double syAssessConvertInsuranceAmt;
    /**
     * 当月标准保费目标
     */
    @ApiModelProperty(value = "当月标准保费目标", notes = "")
    private Double smAssessConvertInsuranceAmtTarget;
    /**
     * 当年标准保费目标
     */
    @ApiModelProperty(value = "当年标准保费目标", notes = "")
    private Double syAssessConvertInsuranceAmtTarget;
    /**
     * 当月标准保费达成率
     */
    @ApiModelProperty(value = "当月标准保费达成率", notes = "")
    private Double smAssessConvertInsuranceAmtAchieveRate;
    /**
     * 当年标准保费达成率
     */
    @ApiModelProperty(value = "当年标准保费达成率", notes = "")
    private Double syAssessConvertInsuranceAmtAchieveRate;
    /**
     * 当月异业保费配比
     */
    @ApiModelProperty(value = "当月异业保费配比", notes = "")
    private Double smOfflineLoanInsuranceRate;
    /**
     * 当年异业保费配比
     */
    @ApiModelProperty(value = "当年异业保费配比", notes = "")
    private Double syOfflineLoanInsuranceRate;
    /**
     * 当月异业保费配比目标
     */
    @ApiModelProperty(value = "当月异业保费配比目标", notes = "")
    private Double smOfflineLoanInsuranceRateTarget;
    /**
     * 当年异业保费配比目标
     */
    @ApiModelProperty(value = "当年异业保费配比目标", notes = "")
    private Double syOfflineLoanInsuranceRateTarget;
    /**
     * 当月留存率
     */
    @ApiModelProperty(value = "当月留存率", notes = "")
    private Double smInsuranceRetentionRate;
    /**
     * 当月留存率目标值
     */
    @ApiModelProperty(value = "当月留存率", notes = "")
    private Double smInsuranceRetentionRateTarget;
    /**
     * 当年留存率
     */
    @ApiModelProperty(value = "当年留存率", notes = "")
    private Double syInsuranceRetentionRate;
    /**
     * 当年留存率目标值
     */
    @ApiModelProperty(value = "当年留存率", notes = "")
    private Double syInsuranceRetentionRateTarget;
    /**
     * 当月信贷客户留存率
     */
    @ApiModelProperty(value = "当月信贷客户留存率", notes = "")
    private Double smLoanInsuranceRetentionRate;
    /**
     * 当年信贷客户留存率
     */
    @ApiModelProperty(value = "当年信贷客户留存率", notes = "")
    private Double syLoanInsuranceRetentionRate;
    /**
     * 当月非信贷客户留存率
     */
    @ApiModelProperty(value = "当月非信贷客户留存率", notes = "")
    private Double smUnloanInsuranceRetentionRate;
    /**
     * 当年非信贷客户留存率
     */
    @ApiModelProperty(value = "当年非信贷客户留存率", notes = "")
    private Double syUnloanInsuranceRetentionRate;
    /**
     * 当月信贷客户转化率
     */
    @ApiModelProperty(value = "当月信贷客户转化率", notes = "")
    private Double smLoanCustTransRate;
    /**
     * 当年信贷客户转化率
     */
    @ApiModelProperty(value = "当年信贷客户转化率", notes = "")
    private Double syLoanCustTransRate;
    /**
     * 当月信贷客户转化率目标
     */
    @ApiModelProperty(value = "当月信贷客户转化率目标", notes = "")
    private Double smLoanCustTransRateTarget;
    /**
     * 当年信贷客户转化率目标
     */
    @ApiModelProperty(value = "当年信贷客户转化率目标", notes = "")
    private Double syLoanCustTransRateTarget;

    @ApiModelProperty(value = "个人标准保费在分支排行", notes = "")
    private Integer rankInBch;

    @ApiModelProperty(value = "个人标准保费在分支排行与前一名的差距", notes = "")
    private Double gapWithPreviousInBch;

    @ApiModelProperty(value = "个人标准保费在区域排行", notes = "")
    private Integer rankInArea;

    @ApiModelProperty(value = "个人标准保费在区域排行与前一名的差距", notes = "")
    private Double gapWithPreviousInArea;

    @ApiModelProperty(value = "个人标准保费在全国排行", notes = "")
    private Integer rankInCountry;

    @ApiModelProperty(value = "个人标准保费在全国排行与前一名的差距", notes = "")
    private Double gapWithPreviousInCountry;

    @ApiModelProperty(value = "年度断保代办数量", notes = "")
    private Integer syInterruptionTodoCnt;
    /**
     * 年度续保代办数量
     */
    @ApiModelProperty(value = "年度续保代办数量", notes = "")
    private Integer syRenewShortTodoCnt;
    /**
     * 年度A类代办数量
     */
    @ApiModelProperty(value = "年度A类代办数量", notes = "")
    private Integer syLoanFirstTodoCnt;
    /**
     * 年度代办数量
     */
    @ApiModelProperty(value = "年度代办数量", notes = "")
    private Integer syTodoCnt;
    /**
     * 年度断保待办跟进率
     */
    @ApiModelProperty(value = "年度断保待办跟进率", notes = "")
    private Double syInterruptionTodoFollowRate;
    /**
     * 年度续保待办跟进率
     */
    @ApiModelProperty(value = "年度续保待办跟进率", notes = "")
    private Double syRenewShortTodoFollowRate;
    /**
     * 年度断保待办转化率
     */
    @ApiModelProperty(value = "年度断保待办转化率", notes = "")
    private Double syInterruptionTodoConversionRate;
    /**
     * 年度续保待办转化率
     */
    @ApiModelProperty(value = "年度续保待办转化率", notes = "")
    private Double syRenewShortTodoConversionRate;
    /**
     * 年度断保待办转化保费
     */
    @ApiModelProperty(value = "年度断保待办转化保费", notes = "")
    private Double syInterruptionTodoConversionAmt;
    /**
     * 年度喜报待办转化保费
     */
    @ApiModelProperty(value = "年度续保待办转化保费", notes = "")
    private Double syRenewShortTodoConversionAmt;

    @ApiModelProperty(value ="当月断保待办跟进数")
    private Integer smInterruptionTodoFollowCnt;
    //sy_interruption_todo_follow_cnt int 当年断保待办跟进数
    @ApiModelProperty(value ="当年断保待办跟进数")
    private Integer syInterruptionTodoFollowCnt;
    //sy_interruption_tdo_follow_cust intn当年断保待办激活数
    @ApiModelProperty(value ="当年断保待办激活数")
    private Integer syInterruptionTdoFollowCust;
    //sm_interruption_tdo_follow_cust int 当月断保待办激活数
    @ApiModelProperty(value ="当月断保待办激活数")
    private Integer smInterruptionTdoFollowCust;
    //sy_tdo_short_follow_cnt int 当年续保待办跟进数
    @ApiModelProperty(value ="当年续保待办跟进数")
    private Integer syTdoShortFollowCnt;
    //sm_tdo_short_follow_cnt int 当月续保待办跟进数
    @ApiModelProperty(value ="当月续保待办跟进数")
    private Integer smTdoShortFollowCnt;
    //sm_tdo_short_follow_policy int 当月续保待办激活数
    @ApiModelProperty(value ="当月续保待办激活数")
    private Integer smTdoShortFollowPolicy;
    //sy_tdo_short_follow_policy int 当年续保待办激活数`
    @ApiModelProperty(value ="当年续保待办激活数")
    private Integer syTdoShortFollowPolicy;

    public Double getSmAssessConvertInsuranceAmt() {
        //判断是否为空，是则返回0.00
        if(smAssessConvertInsuranceAmt==null){
            return 0.00;
        }
        //格式化为两位小数
        smAssessConvertInsuranceAmt = BigDecimal.valueOf(smAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return smAssessConvertInsuranceAmt;
    }

    public Double getSyAssessConvertInsuranceAmt() {
        //判断是否为空，是则返回0.00
        if(syAssessConvertInsuranceAmt==null){
            return 0.00;
        }
        //格式化为两位小数
        syAssessConvertInsuranceAmt = BigDecimal.valueOf(syAssessConvertInsuranceAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syAssessConvertInsuranceAmt;
    }

    public Double getSmAssessConvertInsuranceAmtTarget() {
        //判断是否为空，是则返回0.00
        if(smAssessConvertInsuranceAmtTarget==null){
            return 0.00;
        }
        //格式化为两位小数
        smAssessConvertInsuranceAmtTarget = BigDecimal.valueOf(smAssessConvertInsuranceAmtTarget).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return smAssessConvertInsuranceAmtTarget;
    }

    public Double getSyAssessConvertInsuranceAmtTarget() {
        //判断是否为空，是则返回0.00
        if(syAssessConvertInsuranceAmtTarget==null){
            return 0.00;
        }
        //格式化为两位小数
        syAssessConvertInsuranceAmtTarget = BigDecimal.valueOf(syAssessConvertInsuranceAmtTarget).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syAssessConvertInsuranceAmtTarget;
    }

    public Double getSmAssessConvertInsuranceAmtAchieveRate() {
        //判断是否为空，是则返回0.00
        if(smAssessConvertInsuranceAmtAchieveRate==null){
            return 0.00;
        }
        //格式化为两位小数
        smAssessConvertInsuranceAmtAchieveRate = BigDecimal.valueOf(smAssessConvertInsuranceAmtAchieveRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return smAssessConvertInsuranceAmtAchieveRate;
    }

    public Double getSyAssessConvertInsuranceAmtAchieveRate() {
        //判断是否为空，是则返回0.00
        if(syAssessConvertInsuranceAmtAchieveRate==null){
            return 0.00;
        }
        //格式化为两位小数
        syAssessConvertInsuranceAmtAchieveRate = BigDecimal.valueOf(syAssessConvertInsuranceAmtAchieveRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syAssessConvertInsuranceAmtAchieveRate;
    }

    public Double getSmOfflineLoanInsuranceRate() {
        //判断是否为空，是则返回0.00
        if (smOfflineLoanInsuranceRate==null){
            return 0.00;
        }
        //格式化为两位小数
        smOfflineLoanInsuranceRate = BigDecimal.valueOf(smOfflineLoanInsuranceRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return smOfflineLoanInsuranceRate;
    }

    public Double getSyOfflineLoanInsuranceRate() {
        //判断是否为空，是则返回0.00
        if (syOfflineLoanInsuranceRate==null){
            return 0.00;
        }
        //格式化为两位小数
        syOfflineLoanInsuranceRate = BigDecimal.valueOf(syOfflineLoanInsuranceRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syOfflineLoanInsuranceRate;
    }

    public Double getSmOfflineLoanInsuranceRateTarget() {
        //判断是否为空，是则返回0.00
        if (smOfflineLoanInsuranceRateTarget==null){
            return 0.00;
        }
        //格式化为两位小数
        smOfflineLoanInsuranceRateTarget = BigDecimal.valueOf(smOfflineLoanInsuranceRateTarget).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return smOfflineLoanInsuranceRateTarget;
    }

    public Double getSyOfflineLoanInsuranceRateTarget() {
        //判断是否为空，是则返回0.00
        if (syOfflineLoanInsuranceRateTarget==null){
            return 0.00;
        }
        //格式化为两位小数
        syOfflineLoanInsuranceRateTarget = BigDecimal.valueOf(syOfflineLoanInsuranceRateTarget).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syOfflineLoanInsuranceRateTarget;
    }

    public Double getSmInsuranceRetentionRate() {
        //判断是否为空，是则返回0.00
        if (smInsuranceRetentionRate==null){
            return 0.00;
        }
        //格式化为两位小数
        smInsuranceRetentionRate = BigDecimal.valueOf(smInsuranceRetentionRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return smInsuranceRetentionRate;
    }

    public Double getSmInsuranceRetentionRateTarget() {
        //判断是否为空，是则返回0.00
        if (smInsuranceRetentionRateTarget==null){
            return 0.00;
        }
        //格式化为两位小数
        smInsuranceRetentionRateTarget = BigDecimal.valueOf(smInsuranceRetentionRateTarget).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return smInsuranceRetentionRateTarget;
    }

    public Double getSyInsuranceRetentionRate() {
        //判断是否为空，是则返回0.00
        if (syInsuranceRetentionRate==null){
            return 0.00;
        }
        //格式化为两位小数
        syInsuranceRetentionRate = BigDecimal.valueOf(syInsuranceRetentionRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syInsuranceRetentionRate;
    }

    public Double getSyInsuranceRetentionRateTarget() {
        //判断是否为空，是则返回0.00
        if (syInsuranceRetentionRateTarget==null){
            return 0.00;
        }
        //格式化为两位小数
        syInsuranceRetentionRateTarget = BigDecimal.valueOf(syInsuranceRetentionRateTarget).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syInsuranceRetentionRateTarget;
    }

    public Double getSmLoanInsuranceRetentionRate() {
        //判断是否为空，是则返回0.00
        if (smLoanInsuranceRetentionRate==null){
            return 0.00;
        }
        //格式化为两位小数
        smLoanInsuranceRetentionRate = BigDecimal.valueOf(smLoanInsuranceRetentionRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return smLoanInsuranceRetentionRate;
    }

    public Double getSyLoanInsuranceRetentionRate() {
        //判断是否为空，是则返回0.00
        if (syLoanInsuranceRetentionRate==null){
            return 0.00;
        }
        //格式化为两位小数
        syLoanInsuranceRetentionRate = BigDecimal.valueOf(syLoanInsuranceRetentionRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syLoanInsuranceRetentionRate;
    }

    public Double getSmUnloanInsuranceRetentionRate() {
        //判断是否为空，是则返回0.00
        if (smUnloanInsuranceRetentionRate==null){
            return 0.00;
        }
        //格式化为两位小数
        smUnloanInsuranceRetentionRate = BigDecimal.valueOf(smUnloanInsuranceRetentionRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return smUnloanInsuranceRetentionRate;
    }

    public Double getSyUnloanInsuranceRetentionRate() {
        //判断是否为空，是则返回0.00
        if (syUnloanInsuranceRetentionRate==null){
            return 0.00;
        }
        //格式化为两位小数
        syUnloanInsuranceRetentionRate = BigDecimal.valueOf(syUnloanInsuranceRetentionRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syUnloanInsuranceRetentionRate;
    }

    public Double getSmLoanCustTransRate() {
        //判断是否为空，是则返回0.00
        if (smLoanCustTransRate==null){
            return 0.00;
        }
        //格式化为两位小数
        smLoanCustTransRate = BigDecimal.valueOf(smLoanCustTransRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return smLoanCustTransRate;
    }

    public Double getSyLoanCustTransRate() {
        //判断是否为空，是则返回0.00
        if (syLoanCustTransRate==null){
            return 0.00;
        }
        //格式化为两位小数
        syLoanCustTransRate = BigDecimal.valueOf(syLoanCustTransRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syLoanCustTransRate;
    }

    public Double getSmLoanCustTransRateTarget() {
        //判断是否为空，是则返回0.00
        if (smLoanCustTransRateTarget==null){
            return 0.00;
        }
        //格式化为两位小数
        smLoanCustTransRateTarget = BigDecimal.valueOf(smLoanCustTransRateTarget).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return smLoanCustTransRateTarget;
    }

    public Double getSyLoanCustTransRateTarget() {
        //判断是否为空，是则返回0.00
        if (syLoanCustTransRateTarget==null){
            return 0.00;
        }
        //格式化为两位小数
        syLoanCustTransRateTarget = BigDecimal.valueOf(syLoanCustTransRateTarget).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syLoanCustTransRateTarget;
    }

    public Double getGapWithPreviousInBch() {
        //判断是否为空，是则返回0.00
        if (gapWithPreviousInBch==null){
            return 0.00;
        }
        //格式化为两位小数
        gapWithPreviousInBch = BigDecimal.valueOf(gapWithPreviousInBch).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return gapWithPreviousInBch;
    }

    public Double getGapWithPreviousInArea() {
        //判断是否为空，是则返回0.00
        if (gapWithPreviousInArea==null){
            return 0.00;
        }
        //格式化为两位小数
        gapWithPreviousInArea = BigDecimal.valueOf(gapWithPreviousInArea).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return gapWithPreviousInArea;
    }

    public Double getGapWithPreviousInCountry() {
        //判断是否为空，是则返回0.00
        if (gapWithPreviousInCountry==null){
            return 0.00;
        }
        //格式化为两位小数
        gapWithPreviousInCountry = BigDecimal.valueOf(gapWithPreviousInCountry).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return gapWithPreviousInCountry;
    }

    public Double getSyInterruptionTodoFollowRate() {
        //判断是否为空，是则返回0.00
        if (syInterruptionTodoFollowRate==null){
            return 0.00;
        }
        //格式化为两位小数
        syInterruptionTodoFollowRate = BigDecimal.valueOf(syInterruptionTodoFollowRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syInterruptionTodoFollowRate;
    }

    public Double getSyRenewShortTodoFollowRate() {
        //判断是否为空，是则返回0.00
        if (syRenewShortTodoFollowRate==null){
            return 0.00;
        }
        //格式化为两位小数
        syRenewShortTodoFollowRate = BigDecimal.valueOf(syRenewShortTodoFollowRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syRenewShortTodoFollowRate;
    }

    public Double getSyInterruptionTodoConversionRate() {
        //判断是否为空，是则返回0.00
        if (syInterruptionTodoConversionRate==null){
            return 0.00;
        }
        //格式化为两位小数
        syInterruptionTodoConversionRate = BigDecimal.valueOf(syInterruptionTodoConversionRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syInterruptionTodoConversionRate;
    }

    public Double getSyRenewShortTodoConversionRate() {
        //判断是否为空，是则返回0.00
        if (syRenewShortTodoConversionRate==null){
            return 0.00;
        }
        //格式化为两位小数
        syRenewShortTodoConversionRate = BigDecimal.valueOf(syRenewShortTodoConversionRate).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syRenewShortTodoConversionRate;
    }

    public Double getSyInterruptionTodoConversionAmt() {
        //判断是否为空，是则返回0.00
        if (syInterruptionTodoConversionAmt==null){
            return 0.00;
        }
        //格式化为两位小数
        syInterruptionTodoConversionAmt = BigDecimal.valueOf(syInterruptionTodoConversionAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syInterruptionTodoConversionAmt;
    }

    public Double getSyRenewShortTodoConversionAmt() {
        //判断是否为空，是则返回0.00
        if (syRenewShortTodoConversionAmt==null){
            return 0.00;
        }
        //格式化为两位小数
        syRenewShortTodoConversionAmt = BigDecimal.valueOf(syRenewShortTodoConversionAmt).setScale(4, RoundingMode.HALF_UP).doubleValue();
        return syRenewShortTodoConversionAmt;
    }
}
