package com.cfpamf.ms.insur.report.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.MybatisProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 数仓项目数据连接
 */
@Configuration
@MapperScan(basePackages = "com.cfpamf.ms.insur.report.dao.dw", sqlSessionFactoryRef = DataSourceDwConfig.PREFIX + "SqlSessionFactory")
@EnableConfigurationProperties(MybatisProperties.class)
public class DataSourceDwConfig {
    static final String PACKAGE = "com.cfpamf.ms.insur.report.dao.odps";
    static final String MAPPER_LOCATION = "classpath:mapper/dw/*.xml";

    static final String PREFIX = "dw";

    final
    MybatisProperties properties;

    @Autowired(required = false)
    public DataSourceDwConfig(MybatisProperties properties) {
        this.properties = properties;
    }

    @ConfigurationProperties(prefix = "spring.datasource." + PREFIX)
    @Bean(name = PREFIX + "DataSource")
    public DataSource dwDataSource() {
        return new DruidDataSource();
    }

    @Bean(name = PREFIX + "SqlSessionFactory")
    public SqlSessionFactory sqlSessionFactoryDw(@Qualifier(PREFIX + "DataSource") DataSource dataSource) throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        applyConfiguration(sessionFactory);
        Resource[] resources = new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION);
        sessionFactory.setMapperLocations(resources);

        return sessionFactory.getObject();
    }

    @Bean(name = PREFIX + "DataSourceTransactionManager")
    public DataSourceTransactionManager dataSourceTransactionManagerDw(@Qualifier(PREFIX + "DataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = PREFIX + "SqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplateDw(@Qualifier(PREFIX + "SqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    private void applyConfiguration(SqlSessionFactoryBean factory) {
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        factory.setConfiguration(configuration);
    }
}
