package com.cfpamf.ms.insur.report.service;

import com.cfpamf.ms.insur.report.annotation.MaskMethod;
import com.cfpamf.ms.insur.report.dao.odps.LookBackMapper;
import com.cfpamf.ms.insur.report.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.report.pojo.dto.LookBackInstantDTO;
import com.cfpamf.ms.insur.report.pojo.query.LookBackQuery;
import com.cfpamf.ms.insur.report.pojo.vo.LookBackEventVO;
import com.cfpamf.ms.insur.report.pojo.vo.OrderDetailVO;
import com.cfpamf.ms.insur.report.pojo.vo.OrderVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Create By zhengjing on 2020/9/15 15:16
 */
@Service
public class LookBackService {

    static final DateTimeFormatter FMT_PT = DateTimeFormatter.ofPattern("yyyyMMdd");
    final
    LookBackMapper lookBackMapper;

    final
    SmOrderMapper orderMapper;

    @Value("${online-date:2020-10-01}")
    private String onlineDateStr;

    private LocalDate onlineDate;

    public LookBackService(LookBackMapper lookBackMapper, SmOrderMapper orderMapper) {
        this.lookBackMapper = lookBackMapper;
        this.orderMapper = orderMapper;
    }

    @PostConstruct
    public void init() {
        onlineDate = LocalDate.parse(onlineDateStr);
    }

    /**
     * 修订日期:2021-09-30
     * 修订人：zenghuaguang
     * 兼容团险单-多个被保人；取第一个被保人
     */
    @MaskMethod
    public OrderDetailVO detail(String orderNo) {
        return  orderMapper.getDetail(orderNo);
    }

    public List<LookBackEventVO> events(String orderId, LocalDate subDate,
                                        LocalDate payDate) {

        String start = yesterdayPt(subDate);
        String end = tomorrowPt(payDate);
        LookBackInstantDTO instByOrderId = lookBackMapper.getInstByOrderId(orderId,
                start,
                end);
        if (Objects.isNull(instByOrderId)) {
            return Collections.emptyList();
        }
        List<LookBackEventVO> lists = lookBackMapper.events(start, end, instByOrderId.getEndTime(), instByOrderId.getUserId(),
                instByOrderId.getIsShare());
        int index = -1;
        for (int i = 0; i < lists.size(); i++) {
            LookBackEventVO lookBackEventVO = lists.get(i);
            if (Objects.nonNull(lookBackEventVO.getOrderid()) &&
                    !Objects.equals(lookBackEventVO.getOrderid(), orderId)) {
                index = i;
                break;
            }
        }
        if (index > 0) {
            lists = lists.subList(0, index);
        }
        int successIndex = -1;
        List<Integer> rmIndexs = new ArrayList<>();
        for (int i = 0; i < lists.size(); i++) {
            if (Objects.equals(lists.get(i).getTracecode(), "orderApplySuccess")) {
                if (successIndex == -1) {
                    successIndex = i;
                } else {
                    rmIndexs.add(i);
                }
            }
        }

        List<LookBackEventVO> res = Lists.newLinkedList();
        for (int i = lists.size() - 1; i >= 0; i--) {
            if (!rmIndexs.contains(i)) {
                res.add(lists.get(i));
            }
        }

        return res;
    }

    public static String yesterdayPt(LocalDate opDate) {
        return opDate.minusDays(1).format(FMT_PT);
    }

    public static String tomorrowPt(LocalDate opDate) {
        return opDate.plusDays(1).format(FMT_PT);
    }

    public PageInfo<OrderVO> pageQuery(LookBackQuery lookBackQuery) {

        //如果创建时间为空 或者是上线时间之前 那么直接开始时间设置成 onlineDate
        if (Objects.isNull(lookBackQuery.getCreateDateStart())
                || lookBackQuery.getCreateDateStart().isBefore(onlineDate)) {
            lookBackQuery.setCreateDateStart(onlineDate);
        }
        //结束时间往后面推一天（确保可以查询到当天数据）
        if (Objects.nonNull(lookBackQuery.getCreateDateEnd())) {
            lookBackQuery.setCreateDateEnd(lookBackQuery.getCreateDateEnd().plusDays(1));
        }
        if (Objects.isNull(lookBackQuery.getCreateDateEnd())
                || lookBackQuery.getCreateDateEnd().isAfter(LocalDate.now())) {
            lookBackQuery.setCreateDateEnd(LocalDate.now());
        }
        return PageHelper
                .startPage(lookBackQuery.getPage(),
                        lookBackQuery.getSize(),
                        lookBackQuery.isCount())
                .doSelectPageInfo(() -> orderMapper.selectByLookBack(lookBackQuery));
    }
}
