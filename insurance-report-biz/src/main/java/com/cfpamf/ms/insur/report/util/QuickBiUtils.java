package com.cfpamf.ms.insur.report.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSException;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;


/**
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class QuickBiUtils {

    //看板
    private static final String DASHBOARD_URI = "/token3rd/dashboard/view/pc.htm";

    //电子表格Url
    private static final String REPORT_URI = "/token3rd/report/view.htm";

    //自助取数Url
    private static final String AUTO_QUERY_URL = "/token3rd/offline/view/pc.htm";

    private static final String EMP_ID = "emp_id";
    private static final String ORG_NAME = "bch_name";
    private static final String REGION_NAME = "area_name";
    /**
     * 获取quickBi嵌入连接
     *
     * @param pageId
     * @param isDashboard
     * @param validityTime
     * @param ak
     * @param sk
     * @param host
     * @param userId
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String getReportUri(String pageId, Integer isDashboard, String validityTime, String ak,
                                      String sk, String host, String userId, JSONObject param) throws UnsupportedEncodingException {
        // 参与签名的请求参数
        Map<String, String> paramMap = new HashMap<>();
        if(isDashboard == 0){
            //仪表板
            paramMap.put("pageId", pageId);
        }
        else if(isDashboard == 10){
            //自助取数
            paramMap.put("pageId", pageId);
        }else {
            //电子表格
            paramMap.put("id", pageId);
        }
        paramMap.put("validityTime", validityTime);
        paramMap.put("userId", userId);
        JSONArray gloablParam = new JSONArray();
        if (null != param && !param.isEmpty()) {
            Set<String> set = param.keySet();
            set.forEach(key -> {
                JSONObject p = new JSONObject();
                p.put("paramKey", key);
                p.put("joinType", "and");
                JSONArray a = new JSONArray();
                JSONObject o = new JSONObject();
                o.put("operate", "in");
                if (EMP_ID.equals(key)) {
                    o.put("value", param.getString(key));
                } else if(ORG_NAME.equals(key)){
                    o.put("value", param.getString(key));
                } else if(REGION_NAME.equals(key)){
                    o.put("value", param.getString(key));
                }
                a.add(o);
                p.put("conditionList", a);
                gloablParam.add(p);
            });
            log.info("globalParam:{}", gloablParam.toJSONString());
            paramMap.put("param", gloablParam.toJSONString());
        }
        // 参与签名的请求头
        Map<String, String> headers = new HashMap<>();
        String timestamp = String.valueOf(System.currentTimeMillis());
        headers.put("X-La-timestamp", timestamp);
        headers.put("X-La-AccessId", ak);
        // 生成签名 & Base64 编码
        String signature = URLEncoder.encode(sign(sk, "GET", headers, paramMap), "UTF-8");
        // 加入签名到参数中 & 拼接生成 URL
        paramMap.put("signature", signature);
        paramMap.put("timestamp", timestamp);
        paramMap.put("accessId", ak);
        // 拼接 URL 的时候，全局参数必须 URL ENCODE
        if (CollectionUtils.isNotEmpty(gloablParam)) {
            paramMap.put("param", URLEncoder.encode(gloablParam.toJSONString(), "UTF-8"));
        }
        if(isDashboard == 0){
            //看板，仪表版
            return makeURL(host, DASHBOARD_URI, paramMap);
        }
        else if(isDashboard == 10){
            //自助取数
            return makeURL(host, AUTO_QUERY_URL, paramMap);
        }
        else {
            //电子表格
            return makeURL(host, REPORT_URI, paramMap);
        }
    }

    /**
     * 拼接请求地址
     *
     * @param host       请求的 HOST
     * @param uri        请求的 URI，根据电子表格、仪表板、自助取数而不同
     * @param parameters 拼接 URL 的请求参数
     * @return
     */
    private static String makeURL(String host, String uri, Map<String, String> parameters) {
        StringBuilder sb = new StringBuilder(host);
        sb.append(uri).append("?");
        for (String key : parameters.keySet()) {
            sb.append(key).append("=").append(parameters.get(key)).append("&");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    /**
     * 计算签名
     *
     * @param secret  APP 密钥即为 accessKey
     * @param method  HttpMethod 固定为 GET
     * @param headers 请求头
     * @param querys  上述请求参数
     * @return 签名后的字符串
     */
    private static String sign(String secret, String method, Map<String, String> headers, Map<String, String> querys) {
        try {
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            byte[] keyBytes =
                    secret.getBytes(StandardCharsets.UTF_8);
            hmacSha256.init(new SecretKeySpec(keyBytes, 0,
                    keyBytes.length, "HmacSHA256"));
            byte[] bytesToSign = buildStringToSign(method, headers,
                    querys).getBytes(StandardCharsets.UTF_8);
            byte[] signature = hmacSha256.doFinal(bytesToSign);
            return new String(Base64.encodeBase64(signature),
                    StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new MSException("", "");
        }
    }

    /**
     * 拼接待签名字符串
     */
    private static String buildStringToSign(String method,
                                            Map<String, String>
                                                    headers,
                                            Map<String, String> querys) {
        StringBuilder sb = new StringBuilder();
        sb.append(method.toUpperCase()).append("\n");
        sb.append("\n");
        sb.append(buildHeaders(headers));
        sb.append(buildResource(querys));
        return sb.toString();
    }

    private static String buildResource(Map<String, String> querys) {
        Map<String, String> sortMap = new TreeMap<>(querys);
        StringBuilder param = new StringBuilder();
        for (Map.Entry<String, String> item : sortMap.entrySet()) {
            if (param.length() > 0) {
                param.append("&");
            }
            param.append(item.getKey());
            param.append("=").append(item.getValue());
        }
        return param.toString();
    }

    private static String buildHeaders(Map<String, String> headers) {
        StringBuilder sb = new StringBuilder();
        if (null == headers || headers.size() < 1) {
            return "";
        }
        Map<String, String> sortMap = new TreeMap<>(headers);
        for (Map.Entry<String, String> header : sortMap.entrySet()) {
            sb.append(header.getKey());
            sb.append(":");
            sb.append(header.getValue());
            sb.append("\n");
        }
        return sb.toString();
    }
}
