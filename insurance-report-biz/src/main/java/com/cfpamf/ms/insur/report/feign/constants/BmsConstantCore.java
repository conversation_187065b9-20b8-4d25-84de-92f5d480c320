package com.cfpamf.ms.insur.report.feign.constants;

/**
 * @author:老K
 * @description:Facade的常量
 * @date:2018-09-21 16:51
 **/
public class BmsConstantCore {
    /**
     * 授权key名称
     */
    public static final String JWT_KEY_AUTH = "authorization";
    /**
     * 授权访问的IP地址
     */
    public static final String REMOTE_IP = "remote-ip";
    /**
     * 前端数据来源：1：web端；2：钉钉端；3：Wechat端；4：App端；100：目前未知的其他方式
     */
    public static final String DATA_ORIGIN = "data-origin";
    /**
     * 同一个请求跟踪号
     */
    public static final String TRACK_NUMBER = "track-number";
    /**
     * 授权钉钉key名称
     */
    public static final String JWT_KEY_AUTH_DingTalk = "authorization-dingtalk";
    /**
     * 授权token
     */
    public static final String JWT_KEY_TOKEN = "bms_figure_";
    /**
     * sso版token后缀
     */
    public static final String JWT_KEY_SSO_TOKEN_SUFFIX = "sso_";
    /**
     * token最长有效期，24小时。单位秒
     */
    public static final long TOKEN_MAX_EXPIRE = 24 * 3600;
    /**
     * refresh token web传参名
     */
    public static final String WEB_KEY_REFRESH_TOKEN = "cfpamfRt";
    /**
     * 授权refresh token
     */
    public static final String BMS_REDIS_KEY_REFRESH_TOKEN = "bms_refresh_token_";
    /**
     * refresh token 摘要
     */
    public static final String BMS_REDIS_KEY_REFRESH_TOKEN_DIGEST = "bms_refresh_token_digest_";
    /**
     * 根据UserId存的用户详情
     */
    public static final String BMS_USER_USERID = "bms_user_id_";
    /**
     * 根据HrUserId存的用户详情
     */
    public static final String BMS_USER_HRUSERID = "bms_user_hr_id_";
    /**
     * 根据Account存的用户详情
     */
    public static final String BMS_USER_ACCOUNT = "bms_user_account_";
    /**
     * 角色所属权限Key名称
     */
    public static final String ROLE_MODULE_BUTTON_KEY = "bms_module_role_";
    /**
     * 授权token有效性判断，token最小长度限制
     */
    public static final Integer JWT_KEY_TOKEN_MIN_LENGTH = 100;
    /**
     * 授权refresh token的最长有效期，单位秒，16小时
     */
    public static final Integer REDIS_REFRESH_TOKEN_MAX_EXPIRE = 3600 * 16;
    /**
     * 分页大小
     */
    public static final Integer WEB_PAGE_SIZE = 20;
    /**
     * 默认用户Id
     */
    public static final Integer BMS_DEFAULT_USER_ID = 1;
    /**
     * 默认真的数据值
     */
    public static final Integer BMS_DEFAULT_TRUE = 1;
    /**
     * 默认假的数据值
     */
    public static final Integer BMS_DEFAULT_FALSE = 0;
    /**
     * 默认用户账号
     */
    public static final String BMS_DEFAULT_USER_ACCOUNT = "admin";
    /**
     * 默认用户名
     */
    public static final String BMS_DEFAULT_USER_NAME = "系统后台管理员";
    /**
     * 授权值
     */
    public static final String JWT_SECRET_KEY = "1E02941E-9E8D-4CFB-9BEA-3AA5FCC5863B";
    /**
     * jwt属性 employeeId
     */
    public static final String JWT_KEY_EMPLOYEEID = "employeeId";
    /**
     * jwt属性 hrUserId
     */
    public static final String JWT_KEY_HRUSERID = "hrUserId";
    /**
     * jwt属性 account
     */
    public static final String JWT_KEY_ACCOUNT = "account";
    /**
     * jwt属性 userName
     */
    public static final String JWT_KEY_USERNAME = "userName";
    /**
     * jwt属性 jobNumber
     */
    public static final String JWT_KEY_JOBNUMBER = "jobNumber";
    /**
     * jwt属性 jobCode
     */
    public static final String JWT_KEY_JOBCODE = "jobCode";
    /**
     * jwt属性 masterJobNumber
     */
    public static final String JWT_KEY_MASTERJOBNUMBER = "masterJobNumber";
    /**
     * jwt属性 orgId
     */
    public static final String JWT_KEY_ORGID = "orgId";
    /**
     * jwt属性 hrOrgId
     */
    public static final String JWT_KEY_HRORGID = "hrOrgId";
    /**
     * jwt属性 hrOrgCode
     */
    public static final String JWT_KEY_HRORGCODE = "hrOrgCode";
    /**
     * jwt属性 hrOrgName
     */
    public static final String JWT_KEY_HRORGNAME = "hrOrgName";
    /**
     * jwt属性 hrOrgTreePath
     */
    public static final String JWT_KEY_HRORGTREEPATH = "hrOrgTreePath";
    /**
     * jwt属性 userType
     */
    public static final String JWT_KEY_USERTYPE = "userType";
    /**
     * jwt属性 randomCode
     */
    public static final String JWT_KEY_RANDOMCODE = "randomCode";
    /**
     * jwt属性 useSso
     */
    public static final String JWT_KEY_USE_SSO = "useSso";
    /**
     * jwt属性 灰度标识
     */
    public static final String JWT_KEY_GRAY = "gray";
    /**
     * jwt属性 systemId
     */
    public static final String JWT_KEY_SYSTEM_ID = "systemId";
    /**
     * jwt属性 createTime
     */
    public static final String JWT_KEY_CREATE_TIME = "createTime";
    /**
     * http的get方法,必须大写
     */
    public static final String HTTP_METHOD_GET = "GET";
    /**
     * http的post方法,必须大写
     */
    public static final String HTTP_METHOD_POST = "POST";
    /**
     * bms图片验证码key
     */
    public static final String BMS_REDIS_KEY_IMAGE_VERIFY = "bms_image_verify_answer_";
    /**
     * 图片验证码验证次数
     */
    public static final String BMS_REDIS_KEY_IMAGE_VERIFY_COUNT = "bms_image_verify_count_";
    /**
     * 图片验证码有效时间，单位秒
     */
    public static final Integer BMS_REDIS_IMAGE_VERIFY_EXPIRE = 300;

    /**
     * 登录密码加密公钥redis key
     */
    public static final String BMS_REDIS_KEY_LOGIN_PUB_KEY = "bms_login_pubkey_";
    /**
     * 登录密码加密公钥redis key有效时间
     */
    public static final Integer BMS_REDIS_LOGIN_PUBKEY_EXPIRE = 300;
    /**
     * 系统最大时间默认值
     */
    public static final String BMS_MAX_DATE = "9999-12-31";

    /**
     * 接口鉴权通过
     */
    public static final String API_ACCESS_PASS = "pass";
    /**
     * 接口鉴权失败但仅告警
     */
    public static final String API_ACCESS_FAIL_ALARM = "fail_alarm";
    /**
     * 接口鉴权失败且拒绝
     */
    public static final String API_ACCESS_FAIL_REJECT = "fail_reject";

    /**
     * redis key：开启了接口鉴权告警的系统id
     */
    public static final String REDIS_KEY_ENABLED_API_ACCESS_ALARM_SYSTEM_IDS = "enabled_api_access_alarm_system_ids";
    /**
     * redis key：开启了接口鉴权拒绝的系统id
     */
    public static final String REDIS_KEY_ENABLED_API_ACCESS_REJECT_SYSTEM_IDS = "enabled_api_access_reject_system_ids";
    /**
     * redis key：开启了接口鉴权严格模式的系统id
     */
    public static final String REDIS_KEY_STRICT_API_ACCESS_SYSTEM_IDS = "enabled_api_access_strict_system_ids";

}
