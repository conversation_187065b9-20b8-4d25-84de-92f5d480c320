package com.cfpamf.ms.insur.report.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.config.AssistantConfig;
import com.cfpamf.ms.insur.report.constant.AssistantConstants;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.dao.safepg.AssistantAdminMapper;
import com.cfpamf.ms.insur.report.dao.safes.UserPostMapper;
import com.cfpamf.ms.insur.report.enums.EnumAssistantAreaDim;
import com.cfpamf.ms.insur.report.enums.EnumAssistantTimeDim;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.enums.OrgLevelEnum;
import com.cfpamf.ms.insur.report.pojo.dto.DataAuthDTO;
import com.cfpamf.ms.insur.report.pojo.query.AssistantAdminQuery;
import com.cfpamf.ms.insur.report.pojo.query.AssistantAdminRankQuery;
import com.cfpamf.ms.insur.report.pojo.query.AssistantDiagnosisQuery;
import com.cfpamf.ms.insur.report.pojo.query.OrderByClause;
import com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantBchVO;
import com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantRankAmtVO;
import com.cfpamf.ms.insur.report.service.diagnosis.BizHelperDiagnosisService;
import com.cfpamf.ms.insur.report.service.diagnosis.DiagnosisRequest;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.function.BiFunction;


/**
 * 保险助手-管理端服务
 *
 * <AUTHOR>
 * @since 2024/2/21 13:44
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AssistantAdminService {

    /**
     * Dao
     */
    AssistantAdminMapper assistantAdminMapper;

    /**
     * 数据权限
     */
    DataAuthService dataAuthService;

    /**
     * 业务诊断
     */
    BizHelperDiagnosisService bizHelperDiagnosisService;


    UserPostMapper userPostMapper;

    AssistantConfig assistantConfig;


    /**
     * 获取趋势图表数据
     *
     * @param query 查询条件
     * @return 趋势图表数据列表
     */
    public List<AssistantBchVO> tendencyChart(AssistantAdminQuery query) {
        // 根据查询条件检查数据权限
        checkDataAuth(query);
        // 根据查询的区域维度来获取趋势图表数据
        switch (query.getAreaDim()) {
            case ALL:
                // 全部区域
                return getTendencyChart(query, assistantAdminMapper::listAllData);
            case AREA:
                // 区域维度
                return getTendencyChart(query, assistantAdminMapper::listAreaData);
            case DISTRICT:
                // 片区维度
                return getTendencyChart(query, assistantAdminMapper::listDistrictData);
            case BRANCH:
                // 分支维度
                return getTendencyChart(query, assistantAdminMapper::listBchData);
            default:
                // 不支持的区域维度
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "区域维度不支持：" + query.getAreaDim());
        }

    }


    /**
     * 根据指定的查询条件进行智能排名查询。该方法会根据查询的时间维度自动替换查询字段，以适应不同的时间粒度（年或月）。
     *
     * @param query 查询条件，包括选择的列、时间维度等信息。需要确保查询条件合法。
     * @return 返回查询结果的分页信息，包含每页的记录数、总记录数等。
     */
    public PageInfo<Map<String, Object>> rankSmart(@Valid AssistantAdminRankQuery query) {
        // 检查数据权限
        checkDataAuth(query);
        List<String> queryCols = Lists.newArrayListWithCapacity(query.getSelectCols().size());
        List<String> inputCols = query.getSelectCols();
        Map<String, String> colMapper = Maps.newHashMap();
        // 根据查询的时间维度，自动替换查询列的前缀，以适配不同的时间粒度的数据
        if (query.getTimeDim() == EnumAssistantTimeDim.MONTH) {
            for (String col : inputCols) {
                String newCol = col;
                // 如果列名以"sy_"开头，则替换为"sm_"，适配月度数据
                if (StringUtils.startsWith(col, "sy_")) {
                    newCol = col.replace("sy_", "sm_");
                }
                queryCols.add(newCol);
                colMapper.put(newCol, col);
            }
            // 同时调整排序列的名称，以匹配月度数据的列名
            for (OrderByClause col : query.getOrderByClauses()) {
                if (StringUtils.startsWith(col.getColName(), "sy_")) {
                    col.setColName(col.getColName().replace("sy_", "sm_"));
                }
            }
        }

        // 如果查询的时间维度为年，则对查询列和排序列做相反的替换
        if (query.getTimeDim() == EnumAssistantTimeDim.YEAR) {
            for (String col : query.getSelectCols()) {
                String newCol = col;
                // 如果列名以"sm_"开头，则替换为"sy_"，适配年度数据
                if (StringUtils.startsWith(col, "sm_")) {
                    newCol = col.replace("sm_", "sy_");
                }
                queryCols.add(newCol);
                colMapper.put(newCol, col);
            }
            // 同时调整排序列的名称，以匹配年度数据的列名
            for (OrderByClause col : query.getOrderByClauses()) {
                if (StringUtils.startsWith(col.getColName(), "sm_")) {
                    col.setColName(col.getColName().replace("sm_", "sy_"));
                }
            }
        }
        // 更新查询条件中的选择列，以使用替换后的新列名
        query.setSelectCols(queryCols);
        // 执行排名查询
        PageInfo<Map<String, Object>> rank = rank(query);
        // 将查询结果中的列名还原为原始名称
        for (Map<String, Object> dataMap : rank.getList()) {
            colMapper.forEach((key, val) -> {
                dataMap.put(val, dataMap.get(key));
            });
        }
        return rank;
    }


    /**
     * 排名
     *
     * @param query 查询条件
     * @return 分页信息
     */
    public PageInfo<Map<String, Object>> rank(AssistantAdminRankQuery query) {
        // 检查数据权限
        checkDataAuth(query);
        // 获取表名
        String areaFlag = null;
        // 根据查询的区域维度设置区域标志
        switch (query.getAreaDim()) {
            case ALL:
                areaFlag = "area";
                break;
            case AREA:
                //该区域下有片区则展示片区数据
                if(query.isExistDistrict()){
                    areaFlag = "district";
                }else{
                    //该区域下有片区则展示分支数据
                    areaFlag = "bch";
                }
                break;
            case DISTRICT:
                areaFlag = "bch";
                break;
            case BRANCH:
                areaFlag = "emp";
                break;
            default:
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "区域维度不支持：" + query.getAreaDim());
        }

        if (CollectionUtils.isEmpty(query.getOrderByClauses())) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "排序字段不能为空");
        }

        if (CollectionUtils.isEmpty(query.getSelectCols())) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "查询字段不能为空");
        }

        query.setDimCol(areaFlag + "_name");
        //判断查询哪张表
        String tableName =
                query.getSelectCols().stream().anyMatch(AssistantConstants.VALID_COL_NAME_DW::contains) ?
                        "ads_insurance_" + areaFlag + "_marketing_progress_dfp" : "dwa_safes_phoenix_todo_" + areaFlag;
        // 根据区域标志设置列名
        String nameCol = areaFlag + "_name as dim_name," + areaFlag + "_code as dim_code";
        // SQL语句
        String colSql = nameCol + " ," + query.toColSql();
        String orderSql = query.toOrderBySql();
        // SQL语句变量
        PageInfo<Map<String, Object>> info = PageHelper.startPage(query.getPage(), query.getSize(), query.isCount()).doSelectPageInfo(() -> assistantAdminMapper.listRank(query.getPt(), tableName, colSql, orderSql, query));


        return info;
    }


    /**
     * 进行业务诊断的函数。
     *
     * @param query 包含诊断查询条件的AssistantDiagnosisQuery对象。
     * @return 返回诊断结果的字符串表示。
     */
    public String diagnosis(@Valid AssistantDiagnosisQuery query) {
        // 检查数据权限
        checkDataAuth(query);
        // 创建诊断请求对象，并设置区域名称和分支名称
        DiagnosisRequest request = new DiagnosisRequest();
        request.setPt(query.getPt());
        request.setAreaName(query.getAreaName());
        request.setBranchName(query.getBchName());
        request.setDiagnosisType(query.getDiagnosisType());
        request.setAreaCodeDataAuth(query.getAreaCodeDataAuth());
        request.setBchCodeDataAuth(query.getBchCodeDataAuth());
        request.setBlackBchList(this.getBlackBchList());
        request.setExistDistrict(query.isExistDistrict());
        // 根据查询的区域维度设置组织级别和组织代码
        switch (query.getAreaDim()) {
            case ALL:
                request.setOrgLevel(OrgLevelEnum.COUNTRY);
                break;
            case AREA:
                request.setOrgLevel(OrgLevelEnum.AREA);
                // 当区域维度为AREA时，设置组织级别为AREA并指定区域代码
                request.setOrgCodes(Collections.singletonList(query.getAreaCode()));
                break;
            case DISTRICT:
                request.setOrgLevel(OrgLevelEnum.DISTRICT);
                // 当区域维度为DISTRICT时，设置组织级别为DISTRICT并指定区域代码列表
                request.setOrgCodes(query.getDistrictCodes());
                break;
            case BRANCH:
                request.setOrgLevel(OrgLevelEnum.BRANCH);
                // 当区域维度为BRANCH时，设置组织级别为BRANCH并指定分支代码
                request.setOrgCodes(Collections.singletonList(query.getBchCode()));
                break;
            default:
                // 若传入的区域维度不支持，则抛出异常
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "区域维度不支持：" + query.getAreaDim());
        }
        // 调用诊断服务
        return bizHelperDiagnosisService.diagnosis(request);
    }

    private List<String> getBlackBchList() {
        if(StringUtils.isNotBlank(assistantConfig.getBlackBchs())){
            return Arrays.asList(assistantConfig.getBlackBchs().split(","));
        }
        return null;
    }


    /**
     * 根据查询条件获取保费排名信息
     *
     * @param query 查询条件
     * @return 分页信息
     */
    public PageInfo<AssistantRankAmtVO> rankAmt(AssistantAdminRankQuery query) {

        // 获取表名
        String areaFlag = null;
        // 根据查询的区域维度设置区域标志
        switch (query.getAreaDim()) {
            case ALL:
                areaFlag = "area";
                break;
            case AREA:
                areaFlag = "bch";
                break;
            case DISTRICT:
                areaFlag = "bch";
                break;
            case BRANCH:
                areaFlag = "emp";
                break;
            default:
                break;
        }

        if (CollectionUtils.isEmpty(query.getOrderByClauses())) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "排序字段不能为空");
        }
        //校验数据
        // 根据区域标志设置表名
        String tableName = "ads_insurance_" + areaFlag + "_marketing_progress_dfp";
        // 根据区域标志设置列名
        String nameCol = areaFlag + "_name as dim_name,"
                + areaFlag + "_code as dim_code";
        // SQL语句
        String colSql;
        String orderSql = null;
        // 根据查询的时间维度设置SQL语句
        if (Objects.equals(query.getTimeDim(), EnumAssistantTimeDim.MONTH)) {
            // -- 标准保费
            colSql = "       sm_assess_convert_insurance_amt            AS assess_convert_insurance_amt,       " +
                    // -- 标准保费目标
                    "       sm_assess_convert_insurance_amt_target     AS assess_convert_insurance_amt_target, " +
                    // -- 标准保费完成率
                    "       sm_assess_convert_insurance_amt_achieve_rate AS assess_convert_insurance_amt_achieve_rate";
            orderSql = " order by sm_assess_convert_insurance_amt_achieve_rate desc, sm_assess_convert_insurance_amt desc";
        } else {
            //  -- 标准保费
            colSql = "       sy_assess_convert_insurance_amt            AS assess_convert_insurance_amt,      " +
                    //-- 标准保费目标
                    "       sy_assess_convert_insurance_amt_target     AS assess_convert_insurance_amt_target, " +
                    // -- 标准保费完成率
                    "       sy_assess_convert_insurance_amt_achieve_rate AS assess_convert_insurance_amt_achieve_rate";
            orderSql = " order by sy_assess_convert_insurance_amt_achieve_rate desc, sy_assess_convert_insurance_amt desc";
        }
        // SQL语句变量
        final String nsql = nameCol;
        final String osql = orderSql;
        final String csql = colSql;
        return PageHelper.startPage(query.getPage(), query.getSize(), query.isCount()).doSelectPageInfo(() -> assistantAdminMapper.listAmtRank(query.getPt(), tableName, nsql, csql, osql, query));
    }


    /**
     * 根据查询条件获取留存率排名信息
     *
     * @param query 查询条件
     * @return 分页信息
     */
    public PageInfo<AssistantRankAmtVO> rankRetentionRate(AssistantAdminQuery query) {

        // 获取表名
        String areaFlag = null;
        // 根据查询的区域维度设置区域标志
        switch (query.getAreaDim()) {
            case ALL:
                areaFlag = "area";
                break;
            case AREA:
                areaFlag = "bch";
                break;
            case DISTRICT:
                areaFlag = "bch";
                break;
            case BRANCH:
                areaFlag = "emp";
                break;
            default:
                break;
        }
        // 根据区域标志设置表名
        String tableName = "ads_insurance_" + areaFlag + "_marketing_progress_dfp";
        // 根据区域标志设置列名
        String nameCol = areaFlag + "_name as dim_name,"
                + areaFlag + "_code as dim_code";
        // SQL语句
        String colSql;
        String orderSql;
        // 根据查询的时间维度设置SQL语句
        if (Objects.equals(query.getTimeDim(), EnumAssistantTimeDim.MONTH)) {
            colSql = "sm_insurance_retention_rate as insurance_retention_rate";
            orderSql = " order by sm_insurance_retention_rate desc";
        } else {
            colSql = "sy_insurance_retention_rate as insurance_retention_rate";
            orderSql = " order by sy_insurance_retention_rate desc";
        }
        // SQL语句变量
        final String nsql = nameCol;
        final String osql = orderSql;
        final String csql = colSql;
        return PageHelper.startPage(query.getPage(), query.getSize(), query.isCount()).doSelectPageInfo(() -> assistantAdminMapper.listAmtRank(query.getPt(), tableName, nsql, csql, osql, query));
    }


    /**
     * 获取数据
     *
     * @param query 查询条件
     * @return 数据对象
     */
    public AssistantBchVO singleData(AssistantAdminQuery query) {

        // 检查数据权限
        checkDataAuth(query);

        // 根据查询对象的区域维度进行数据获取
        switch (query.getAreaDim()) {
            case ALL:
                return getSingleData(query, assistantAdminMapper::listAllData);
            case AREA:
                return getSingleData(query, assistantAdminMapper::listAreaData);
            case DISTRICT:
                return getSingleData(query, assistantAdminMapper::listDistrictData);
            case BRANCH:
                return getSingleData(query, assistantAdminMapper::listBchData);
            default:
                return null;
        }
    }


    /**
     * 获取当前年份的每个月的月末日期
     *
     * @return 当前年份的每个月的月末日期列表
     */
    public List<String> getEndDatesOfMonthsInCurrentYearAndFmt(String pt) {
        //  相对日期
        LocalDate yesterday = LocalDate.parse(pt, BaseConstants.FMT_YYYYMMDD);

        List<String> pts = Lists.newArrayListWithCapacity(12);
        pts.add(BaseConstants.FMT_YYYYMMDD.format(yesterday));
        // 获取截止到昨天的所有月末日期   从今年的1月1日开始
        LocalDate start = LocalDate.of(yesterday.getYear(), 1, 1);

        while (start.isBefore(yesterday)) {
            YearMonth yearMonth = YearMonth.from(start);
            LocalDate endOfMonth = yearMonth.atEndOfMonth();
            if (endOfMonth.isBefore(yesterday) || endOfMonth.equals(yesterday)) {
                pts.add(BaseConstants.FMT_YYYYMMDD.format(endOfMonth));
            }
            // 移动到下一个月份
            start = start.plusMonths(1);
        }
        return pts;
    }

    /**
     * 获取单条数据
     *
     * @param query   查询条件
     * @param daoCall 数据访问对象调用方法
     * @return 单条数据
     */
    private AssistantBchVO getSingleData(AssistantAdminQuery query, BiFunction<List<String>, AssistantAdminQuery, List<AssistantBchVO>> daoCall) {
        // 获取pt
        String pt = query.getPt();
        // 调用数据访问对象方法获取数据
        List<AssistantBchVO> datas = daoCall.apply(Collections.singletonList(pt), query);
        // 如果数据为空
        if (CollectionUtils.isEmpty(datas)) {
            return null;
        }
        AssistantBchVO vo = datas.get(0);
        //设置每个月多少天按每天平均1条分享作为目标值
        vo.setSmShareCntRateTarget(getMonthDays());
        return vo;
    }

    /**
     * 获取趋势数据
     *
     * @param query   查询条件
     * @param daoCall 数据访问对象调用方法
     * @return 单条数据
     */
    private List<AssistantBchVO> getTendencyChart(AssistantAdminQuery query, BiFunction<List<String>, AssistantAdminQuery, List<AssistantBchVO>> daoCall) {

        List<String> pts = getEndDatesOfMonthsInCurrentYearAndFmt(query.getPt());
        // 调用数据访问对象方法获取数据
        List<AssistantBchVO> datas = daoCall.apply(pts, query);
        // 如果数据为空
        if (CollectionUtils.isEmpty(datas)) {

            return Collections.emptyList();

        }
        if (datas.size() != pts.size()) {
            log.warn("数据不全:" + query);
        }
        //针对造数顺序问题  兼容
        datas.sort(Comparator.comparing(AssistantBchVO::getPt));
        return datas;
    }

    /**
     * 检查数据权限
     *
     * @param query 查询对象
     */
    private void checkDataAuth(AssistantAdminQuery query) {
        // 获取数据权限信息
        DataAuthDTO dataAuth = dataAuthService.dataAuth();

        // 如果区域名称不为空，则设置查询对象的区域名称
        if (StringUtils.isNotBlank(dataAuth.getRegionName())) {
            if (query.getAreaDim().ordinal() < EnumAssistantAreaDim.AREA.ordinal()) {
                throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
            }
            query.setAreaName(dataAuth.getRegionName());
            query.setAreaCodeDataAuth(dataAuth.getRegionCode());
        }

        // 如果组织名称不为空，则设置查询对象的组织名称
        if (StringUtils.isNotBlank(dataAuth.getOrgName())) {
            //如果是分支角色 但是查询分支以上的数据 报错
            if (query.getAreaDim().ordinal() < EnumAssistantAreaDim.BRANCH.ordinal()) {
                throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
            }
            query.setBchName(dataAuth.getOrgName());
            query.setBchCodeDataAuth(dataAuth.getOrgCode());
        }

        // 如果用户ID不为空，则抛出无数据权限异常
        if (StringUtils.isNotBlank(dataAuth.getUserId())) {
            throw new MSBizNormalException(ExcptEnum.NO_DATA_PERMISSION_801010);
        }
    }

    /**
     * 获取当前月份的天数
     * @return
     */
    private static int getMonthDays(){
        Calendar calendar = Calendar.getInstance();
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }
}
