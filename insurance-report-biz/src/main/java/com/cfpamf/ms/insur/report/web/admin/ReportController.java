package com.cfpamf.ms.insur.report.web.admin;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.annotation.AdminAutoAuthQuery;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.enums.ReportType;
import com.cfpamf.ms.insur.report.pojo.query.*;
import com.cfpamf.ms.insur.report.pojo.vo.*;
import com.cfpamf.ms.insur.report.pojo.vo.branch.BranchStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.personnel.PersonnelStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.personnel.PersonnelTransferRateVo;
import com.cfpamf.ms.insur.report.pojo.vo.region.RegionStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.total.TotalStatisticsVo;
import com.cfpamf.ms.insur.report.service.admin.AdminReportService;
import com.cfpamf.ms.insur.report.service.admin.PerformanceStatisticsService;
import com.cfpamf.ms.insur.report.service.admin.TransferRateService;
import com.cfpamf.ms.insur.report.util.ExcelBuilderUtil;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@Api(value = "保险业务报表接口", tags = {"保险业务报表接口"})
@RequestMapping(BaseConstants.ADMIN_VERSION + "/report/2021")
public class ReportController {

    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyyMMdd";
    public static final String PERSONAL_TRANSFER_RATE_EXCEL_TITLE = "个人转换率报表统计";
    public final String XLSX_SUFFIX = ".xlsx";
    public final String PERFORMANCE_EXCEL_NAME = "业绩报表";

    @Autowired
    private AdminReportService adminReportService;

    @Autowired
    private PerformanceStatisticsService performanceStatisticsService;

    @Autowired
    private TransferRateService transferRateService;

    /**
     * 查询保险个人业务明细报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询保险个人业绩报表2021")
    @PostMapping("/businessDetails/personal")
    @AdminAutoAuthQuery
    public PageInfo<PersonnelStatisticsVo> listSmPersonalBusinessDetailsByPage(@RequestBody PerformanceReportQuery query) {
        return performanceStatisticsService.searchPersonalPerformance(query);
    }

    /**
     * 查询保险机构业绩报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询保险机构业绩报表2021")
    @PostMapping("/businessDetails/org")
    @AdminAutoAuthQuery
    public PageInfo<BranchStatisticsVo> listSmOrgBusinessDetailsByPage(@RequestBody PerformanceReportQuery query) {
        return performanceStatisticsService.searchBranchPerformance(query);
    }

    /**
     * 查询保险区域业绩报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询保险区域业绩报表2021")
    @PostMapping("/businessDetails/region")
    @AdminAutoAuthQuery
    public PageInfo<RegionStatisticsVo> listSmRegionBusinessDetailsByPage(@RequestBody PerformanceReportQuery query) {
        return performanceStatisticsService.searchRegionPerformance(query);
    }

    /**
     * 查询合计业绩报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询合计业绩报表2021")
    @PostMapping("/businessDetails/total")
    @AdminAutoAuthQuery
    public PageInfo<TotalStatisticsVo> searchTotalPerformance(@RequestBody PerformanceReportQuery query) {
        return performanceStatisticsService.searchTotalPerformance(query);
    }

    /**
     * 下载业绩报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "下载业绩报表2021")
    @GetMapping("/businessDetails/download")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportType", value = "报表类型 personal个人、org机构、region区域、total汇总", dataType = "string", paramType = "query")
    })
    @AdminAutoAuthQuery
    public void downloadBusinessDetails(@ModelAttribute PerformanceReportQuery query, @RequestParam("reportType") String reportType, HttpServletResponse response) {
        try (OutputStream os = response.getOutputStream()) {
            //获取导出数据
            List excelVoList = performanceStatisticsService.getExcelVo(query, reportType);
            ReportType reportTypeByCode = ReportType.getReportTypeByCode(reportType);
            //配置响应头信息
            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(reportTypeByCode.getDesc() + PERFORMANCE_EXCEL_NAME, StandardCharsets.UTF_8.name())
                    + new SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD).format(new Date()) + XLSX_SUFFIX);
            response.setContentType("application/octet-stream");
            //构建导出
            ExcelBuilderUtil.newInstance()
                    .initExcelTemplate(reportTypeByCode.getExcelTemplatePath())
                    .initSheetHead(reportTypeByCode.getExcelVoClass())
                    .addSheetData(excelVoList)
                    .write(os);
        } catch (Exception e) {
            log.warn("文件下载失败", e);
            throw new MSBizNormalException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006);
        }
    }


    /**
     * 查询个人续保率报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询个人续保率报表2021")
    @PostMapping("/renewalRate/personal")
    @AdminAutoAuthQuery
    public PageInfo<PersonalRenewalRateVO> listPersonalRenewalRateByPage(@RequestBody RenewalRateQuery query) {
        return adminReportService.listPersonalRenewalRateByPage(query);
    }

    /**
     * 查询机构续保率报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询机构续保率报表2021")
    @PostMapping("/renewalRate/org")
    @AdminAutoAuthQuery
    public PageInfo<OrgRenewalRateVO> listOrgRenewalRateByPage(@RequestBody RenewalRateQuery query) {
        log.info("RenewalRateQuery={}", query);
        return adminReportService.listOrgRenewalRateByPage(query);
    }

    /**
     * 查询区域续保率报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询区域续保率报表2021")
    @PostMapping("/renewalRate/region")
    @AdminAutoAuthQuery
    public PageInfo<RegionRenewalRateVO> listRegionRenewalRateByPage(@RequestBody RenewalRateQuery query) {
        return adminReportService.listRegionRenewalRateByPage(query);
    }

    /**
     * 查询续保率汇总报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询续保率汇总报表2021")
    @PostMapping("/renewalRate/total")
    @AdminAutoAuthQuery
    public PageInfo<RenewalRateVO> listRenewalRateByPage(@RequestBody RenewalRateQuery query) {
        return adminReportService.listRenewalRateByPage(query);
    }


    /**
     * 下载续保率报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "下载续保率报表2021")
    @GetMapping("/renewalRate/download")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportType", value = "报表类型 personal个人、org机构、region区域、total汇总", dataType = "string", paramType = "query")
    })
    @AdminAutoAuthQuery
    public void downloadRenewalRate(@ModelAttribute RenewalRateQuery query, @RequestParam("reportType") String reportType, HttpServletResponse response) {
        if (Objects.equals(reportType, ReportType.ORG.getCode())) {
            adminReportService.downloadOrgRenewalRate(query, response);
        } else if (Objects.equals(reportType, ReportType.PERSONAL.getCode())) {
            adminReportService.downloadPersonalRenewalRate(query, response);
        } else if (Objects.equals(reportType, ReportType.REGION.getCode())) {
            adminReportService.downloadRegionRenewalRate(query, response);
        } else if (Objects.equals(reportType, ReportType.TOTAL.getCode())) {
            adminReportService.downloadRenewalRate(query, response);
        } else {
            throw new MSBizNormalException("9999999", "续保率报表没有该类型的报表" + reportType);
        }
    }


    /**
     * 查询个人推广费报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询个人推广费报表2021")
    @PostMapping("/promoFee/personal")
    @AdminAutoAuthQuery
    public PageInfo<PersonalPromoFeeVO> listPersonalPromoFeeByPage(@RequestBody PromoFeeQuery query) {
        return adminReportService.searchPersonalPromoFeeByPage(query);
    }

    /**
     * 查询个人转换率报表
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询个人转换率报表,如果查询时间为空返回当月数据")
    @PostMapping("/transferRate/personal")
    @AdminAutoAuthQuery
    public PageInfo<PersonnelTransferRateVo> searchPersonnelTransferRate(@RequestBody WxReportQuery query) {
        return transferRateService.searchPersonnelTransferRate(query);
    }


    /**
     * 下载个人转换率报表
     *
     * @param
     * @return
     */
    @ApiOperation(value = "下载个人转换率报表")
    @GetMapping("/transferRate/personal/download")
    @AdminAutoAuthQuery
    public void downloadPersonnelTransferRate(@ModelAttribute WxReportQuery query, HttpServletResponse response) {
        try (OutputStream os = response.getOutputStream()) {
            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(PERSONAL_TRANSFER_RATE_EXCEL_TITLE, StandardCharsets.UTF_8.name())
                    + new SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD).format(new Date()) + XLSX_SUFFIX);
            response.setContentType("application/octet-stream");
            query.setAll(true);
            ExcelBuilderUtil.newInstance()
                    .createSheet(PERSONAL_TRANSFER_RATE_EXCEL_TITLE)
                    .initSheetHead(PersonnelTransferRateVo.class, true)
                    .addSheetData(transferRateService.searchPersonnelTransferRate(query).getList())
                    .write(os);
        } catch (Exception e) {
            log.warn("文件下载失败", e);
            throw new MSBizNormalException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006);
        }
    }

    /**
     * 查询机构绩效报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询机构绩效报表2021")
    @PostMapping("/promoFee/org")
    @AdminAutoAuthQuery
    public PageInfo<OrgPromoFeeVO> listOrgPromoFeeByPage(@RequestBody PromoFeeQuery query) {
        return adminReportService.listOrgPromoFeeByPage(query);
    }


    /**
     * 下载绩效报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "下载绩效报表2021")
    @GetMapping("/promoFee/download")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportType", value = "报表类型 personal个人、org机构、region区域、total汇总", dataType = "string", paramType = "query")
    })
    @AdminAutoAuthQuery
    public void downloadPromoFeeReport(@ModelAttribute PromoFeeQuery query, @RequestParam("reportType") String reportType, HttpServletResponse response) {
        if (Objects.equals(reportType, ReportType.ORG.getCode())) {
            adminReportService.downloadOrgPromoFee(query, response);
        } else if (Objects.equals(reportType, ReportType.PERSONAL.getCode())) {
            adminReportService.downloadPersonalPromoFee(query, response);
        } else {
            throw new MSBizNormalException("9999999", "绩效报表没有该类型的报表" + reportType);
        }
    }

    @ApiOperation(value = "查询机构业务排名报表2021")
    @PostMapping("/businessRank/org")
    @AdminAutoAuthQuery
    public PageInfo<BusinessRankVO> listOrgBusinessRankByPage(@RequestBody ReportQuery query) {
        return adminReportService.listOrgBusinessRankByPage(query);
    }

    /**
     * 下载绩效报表2021
     *
     * @param
     * @return
     */
    @ApiOperation(value = "下载机构业务排名报表2021")
    @GetMapping("/businessRank/download")
    @AdminAutoAuthQuery
    public void downloadOrgBusinessRank(@ModelAttribute ReportQuery query, HttpServletResponse response) {
        adminReportService.downloadOrgBusinessRank(query, response);
    }


    @GetMapping("/dashboard/sales/smy")
    public SmSmyVO getSmSmSmy() {
        return adminReportService.getSmSmSmy();
    }

    /**
     * 下载机构佣金报表2021
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "机构佣金报表2021")
    @PostMapping("/commission/org")
    @AdminAutoAuthQuery
    public PageInfo<SmOrgCommissionReportVO> getSmOrgCommissionReportByPage(@RequestBody ReportQuery query) {
        return adminReportService.listOrgCommissionByPage(query);
    }

    /**
     * 下载机构佣金报表2021
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "下载机构佣金报表2021")
    @GetMapping("/commission/org/download")
    @AdminAutoAuthQuery
    public void downloadOrgCommissionReport(@ModelAttribute ReportQuery query, HttpServletResponse response) {
        adminReportService.downloadOrgCommission(query, response);
    }

}
