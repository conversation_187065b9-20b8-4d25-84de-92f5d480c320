package com.cfpamf.ms.insur.report.config;

import com.cfpamf.ms.insur.report.service.diagnosis.AbstractBizHelperDiagnosisService;
import freemarker.cache.ClassTemplateLoader;
import freemarker.template.Configuration;

public class FreemarkerConfig {

    private static final Configuration CONFIGURATION = new Configuration(Configuration.VERSION_2_3_28);

    static {
        CONFIGURATION.setTemplateLoader(new ClassTemplateLoader(AbstractBizHelperDiagnosisService.class, "/freemarker"));
        CONFIGURATION.setDefaultEncoding("UTF-8");
        CONFIGURATION.setOutputEncoding("UTF-8");
    }

    public static Configuration getConfiguration(){
        return CONFIGURATION;
    }
}
