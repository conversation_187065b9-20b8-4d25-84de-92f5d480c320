package com.cfpamf.ms.insur.report.service.diagnosis.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class RetentionRateSummaryModel extends AbstractDiagnosisModel {

    /**
     * 小结
     */
    private Summary summary;

    @Data
    public static class Summary {
        /**
         * 组织类型，取值可以参见{@link com.cfpamf.ms.insur.report.enums.OrgLevelEnum}
         */
        private String type;
        /**
         * 客户留存率目标是否完成
         */
        private boolean finished;
        /**
         * 当年客户留存率
         */
        private String retentionYearRate;
        /**
         * 客户留存率的TOP信息
         */
        private List<RankInfo> rankInfos;

        @Data
        public static class RankInfo {
            /**
             * 排名名称
             */
            private String name;
            /**
             * 当月客户留存率
             */
            private String retentionYearRate;
        }
    }
}
