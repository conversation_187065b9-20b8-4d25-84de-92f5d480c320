package com.cfpamf.ms.insur.report.pojo.vo.branch;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 保费目标
 *
 * <AUTHOR>
 * @date 2021/3/30 17:53
 */
@Data
public class PremiumTargetVo {
    /**
     * 当月保费目标
     */
    @ApiModelProperty(value = "当月保费目标")
    private BigDecimal currentMonth;

    /**
     * 截至当月保费目标
     */
    @ApiModelProperty(value = "截至当月保费目标")
    private BigDecimal presentCurrentMonth;

    /**
     * 当年保费目标
     */
    @ApiModelProperty(value = "当年保费目标")
    private BigDecimal currentYear;


}
