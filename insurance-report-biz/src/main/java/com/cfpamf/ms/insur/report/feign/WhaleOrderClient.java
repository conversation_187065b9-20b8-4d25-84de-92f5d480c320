package com.cfpamf.ms.insur.report.feign;

import com.cfpamf.ms.insur.report.feign.req.CustVisitCountQueryInput;
import com.cfpamf.ms.insur.report.feign.req.CustVisitListQueryInput;
import com.cfpamf.ms.insur.report.feign.resp.WhaleResp;
import com.cfpamf.ms.insur.report.feign.resp.vo.CustVisitCountQueryOutput;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> 2020/4/10 15:36
 */
@FeignClient(url = "${whale.domain}", value = "whaleOrderClient")
public interface WhaleOrderClient {

    @PostMapping(value = "/channel/api/customer/onlinePromotion/queryCurrentDayCustVisitInfoCount")
    WhaleResp<List<CustVisitCountQueryOutput>> queryCurrentDayCustVisitInfoCount(
                                                          @RequestParam("aid") @ApiParam(name = "aid", value = "渠道编号") String aid,
                                                          @RequestParam("version") @ApiParam(name = "version", value = "版本号") String version,
                                                          @RequestBody CustVisitCountQueryInput custVisitCountQueryInput);

    @PostMapping(value = "/channel/api/customer/onlinePromotion/queryCustVisitCount")
    WhaleResp<Integer> queryCustVisitCount(
            @RequestParam("aid") @ApiParam(name = "aid", value = "渠道编号") String aid,
            @RequestParam("version") @ApiParam(name = "version", value = "版本号") String version,
            @RequestBody CustVisitListQueryInput custVisitListQueryInput);
}
