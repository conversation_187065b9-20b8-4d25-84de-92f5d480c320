package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description 客户经理异业保费转化情况统计
 * @Date 16:48 2024/4/8
 * @Param 
 * <AUTHOR>
 * @return 
 **/
@Data
public class DailyRetrospectiveFinanceStatisticsDTO implements Serializable {
    private static final long serialVersionUID = -5894136616471899527L;

    @ApiModelProperty(value = "客户异业保费转化列表")
    private List<DailyRetrospectiveFinanceDataDTO> list;

    @ApiModelProperty(value = "申请/放款金额(总和)")
    private String applAmountSum;

    @ApiModelProperty(value = "转化保费(总和)")
    private BigDecimal conversionAmtSum;

    @ApiModelProperty(value = "转化保费配比(总和)")
    private BigDecimal conversionRateSum;

    @ApiModelProperty(value = "申请/放款时间(总和)")
    private String applTimeSum;

    @ApiModelProperty(value = "借款人转化数(总和)")
    private Integer borrowerConvertSum;

    @ApiModelProperty(value = "共借人转化数(总和)")
    private Integer coborrowerConvertSum;

    @ApiModelProperty(value = "担保人转化数(总和)")
    private Integer bondsmanConvertSum;

    @ApiModelProperty(value = "业务类型，授信：CREDIT，放款：LOAN")
    private String businessTypeSum;

    @ApiModelProperty(value = "当月异业保费配比目标")
    private Double smOfflineLoanInsuranceRateTarget ;

    public Double getSmOfflineLoanInsuranceRateTarget() {
        if(smOfflineLoanInsuranceRateTarget == null){
            return 0D;
        }
        return smOfflineLoanInsuranceRateTarget;
    }

    public BigDecimal getConversionAmtSum() {
        if(conversionAmtSum == null){
            return BigDecimal.ZERO;
        }
        return conversionAmtSum;
    }

    public BigDecimal getConversionRateSum() {
        if(conversionRateSum == null){
            return BigDecimal.ZERO;
        }
        return conversionRateSum;
    }

    public Integer getBorrowerConvertSum() {
        if(borrowerConvertSum == null){
            return 0;
        }
        return borrowerConvertSum;
    }

    public Integer getCoborrowerConvertSum() {
        if(coborrowerConvertSum == null){
            return 0;
        }
        return coborrowerConvertSum;
    }

    public Integer getBondsmanConvertSum() {
        if(bondsmanConvertSum == null){
            return 0;
        }
        return bondsmanConvertSum;
    }
}
