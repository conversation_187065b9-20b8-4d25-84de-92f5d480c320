package com.cfpamf.ms.insur.report.pojo.query;

import com.cfpamf.ms.insur.report.constant.AssistantConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 * @since 2024/2/22 14:51
 */
@Data
@ApiModel("排序字段")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderByClause {

    static final String LAST = "desc";
    static final String FIRST = "asc";
    @ApiModelProperty("排序的列名")
    String colName;

    @ApiModelProperty("排序的规则 desc 倒序 asc 正序")
    String clause = "desc";

    /**
     * 将排序条件转换为SQL语句
     *
     * @return SQL语句
     */
    public String toSql() {
        // 检查排序条件是否合法
        checkVal();
        // 返回排序SQL语句
        return colName + " " + nullsSql();
    }

    public void checkVal() {
        if (!(AssistantConstants.VALID_COL_NAME_DW.contains(colName) || AssistantConstants.VALID_COL_NAME_TDO.contains(colName))) {
            throw new IllegalArgumentException("排序的列名不合法：" + colName);
        }

        if (!AssistantConstants.VALID_ORDER_BY_CLAUSE.contains(StringUtils.toRootLowerCase(clause))) {
            throw new IllegalArgumentException("排序的规则不合法不合法:" + clause);
        }
    }

    private String nullsSql(){
        if(LAST.equalsIgnoreCase(clause)){
            return clause + " nulls last";
        }
        return clause + " nulls first";
    }

}

