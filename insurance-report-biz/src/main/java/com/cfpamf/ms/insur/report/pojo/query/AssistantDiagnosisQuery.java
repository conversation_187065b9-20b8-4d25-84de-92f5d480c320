package com.cfpamf.ms.insur.report.pojo.query;

import com.cfpamf.ms.insur.report.service.diagnosis.DiagnosisTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @since 2024/3/4 11:14
 */
@Data
@ApiModel("业务诊断查询")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantDiagnosisQuery extends AssistantAdminQuery {

    @NotNull(message = "业务诊断类型不能为空")
    @ApiModelProperty("业务诊断类型")
    DiagnosisTypeEnum diagnosisType;

    @ApiModelProperty("区域下是否有片区")
    boolean existDistrict;
}
