package com.cfpamf.ms.insur.report.pojo.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;


/**
 * 保险助手 区域维度
 *
 * <AUTHOR>
 * @since 2024/2/21 14:11
 */
@Data
@ApiModel("保险助手-区域维度")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantDistrictVO extends AssistantBasicVO {

    @ApiModelProperty("区域名字")
    String areaName;

    @ApiModelProperty("区域编码")
    String areaCode;


    @ApiModelProperty("片区名字")
    String districtName;

    @ApiModelProperty("片区编码")
    String districtCode;
}
