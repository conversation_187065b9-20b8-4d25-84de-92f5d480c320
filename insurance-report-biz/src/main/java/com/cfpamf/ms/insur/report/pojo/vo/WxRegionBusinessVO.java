package com.cfpamf.ms.insur.report.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WxRegionBusinessVO extends WxBuisnessDetailsVO implements Serializable {

    @ApiModelProperty("人均保费")
    private BigDecimal avgInsuranceAmtMonthly;

    @ApiModelProperty("达成率")
    private BigDecimal achievementRate;

    @ApiModelProperty("编制人均保费")
    private BigDecimal planEmpAverageAmt;
}
