package com.cfpamf.ms.insur.report.service.wx;

import com.cfpamf.ms.insur.report.dao.safepg.SmSafepgReportMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CommonToolService {
    @Autowired
    private SmSafepgReportMapper smSafepgReportMapper;

    public String getMaxAvailablePtForTables(List<String> tableNameList) {
        String maxPt = smSafepgReportMapper.getMaxAvailablePtForTables(tableNameList);
        if (maxPt == null) {
            throw new RuntimeException("获取最大PT失败");
        }
        return maxPt;
    }
}
