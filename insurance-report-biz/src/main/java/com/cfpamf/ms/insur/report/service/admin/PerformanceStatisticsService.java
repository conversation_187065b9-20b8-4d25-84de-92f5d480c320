package com.cfpamf.ms.insur.report.service.admin;

import com.cfpamf.ms.insur.report.pojo.query.PerformanceReportQuery;
import com.cfpamf.ms.insur.report.pojo.vo.branch.BranchStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.personnel.PersonnelStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.region.RegionStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.total.TotalStatisticsVo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/31 14:34
 */
public interface PerformanceStatisticsService {
    /**
     * 搜索个人明细
     *
     * @param performanceReportQuery
     * @return
     */
    PageInfo<PersonnelStatisticsVo> searchPersonalPerformance(PerformanceReportQuery performanceReportQuery);


    /**
     * 区域机构明细
     *
     * @param performanceReportQuery
     * @return
     */
    PageInfo<BranchStatisticsVo> searchBranchPerformance(PerformanceReportQuery performanceReportQuery);

    /**
     * 搜索区域业绩明细
     *
     * @param performanceReportQuery
     * @return
     */
    PageInfo<RegionStatisticsVo> searchRegionPerformance(PerformanceReportQuery performanceReportQuery);

    /**
     * 搜索区域业绩明细
     *
     * @param performanceReportQuery
     * @return
     */
    PageInfo<TotalStatisticsVo> searchTotalPerformance(PerformanceReportQuery performanceReportQuery);

    /**
     * 获取报表excel数据
     *
     * @param performanceReportQuery
     * @param reportType
     * @return
     */
    List getExcelVo(PerformanceReportQuery performanceReportQuery, String reportType);

}
