package com.cfpamf.ms.insur.report.feign.resp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserDetailVO extends UserListVO implements Serializable {

    private static final long serialVersionUID = 5622314103421260487L;

    @ApiModelProperty(value = "是否总部")
    private Boolean isHeadOrg;

    @ApiModelProperty(value = "兼职信息")
    private List<UserPartTimerVO> userPartTimerList;

    @ApiModelProperty(value = "岗位信息")
    private List<UserPostVO> postList;

    @ApiModelProperty(value = "角色信息")
    private List<UserRoleVO> roleList;

    @Data
    public static class UserPostVO {

        @ApiModelProperty(value = "北森用户Id")
        private Integer hrUserId;

        @ApiModelProperty(value = "行政上级/直线经理Id")
        private String userAdminId;

        @ApiModelProperty(value = "行政上级/直线经理")
        private String userAdminName;

        @ApiModelProperty(value = "业务上级/虚线经理Id")
        private String userMasterId;

        @ApiModelProperty(value = "业务上级/虚线经理")
        private String userMasterName;

        @ApiModelProperty(value = "所属岗位postId")
        private Integer postId;

        @ApiModelProperty(value = "所属岗位(职务)")
        private String postName;

        @ApiModelProperty(value = "北森岗位Id")
        private Integer hrPostId;

        @ApiModelProperty("所属岗位(职务)状态：0:停用,1:启用)")
        private Integer postStatus;

        @ApiModelProperty("所属组织(Id)")
        private Integer orgId;

        @ApiModelProperty("所属组织(Code)")
        private String orgCode;

        @ApiModelProperty("所属组织(名称)")
        private String orgName;

        @ApiModelProperty("机构表的hrOrgId")
        private Integer hrOrgId;

        @ApiModelProperty("所属组织层级结构")
        private String hrOrgTreePath;

        @ApiModelProperty("所属组织兼职工号，如果没有就是主职工号")
        private String jobNumber;

        @ApiModelProperty("任职类型（0:正职、1:兼职）")
        private Integer serviceType;
    }

    /**
     * 用户角色VO
     */
    @Data
    public static class UserRoleVO {

        @ApiModelProperty(value = "系统Id")
        private String systemId;

        @ApiModelProperty(value = "系统名称")
        private String systemShortName;

        @ApiModelProperty(value = "角色Id")
        private Integer roleId;

        @ApiModelProperty(value = "角色编码")
        private String roleCode;

        @ApiModelProperty(value = "角色名称")
        private String roleName;

        @ApiModelProperty(value = "用户角色类型(1、用户角色，2、岗位角色)")
        private Integer userRoleType;

        @ApiModelProperty(value = "岗位Id")
        private Integer postId;

        @ApiModelProperty(value = "人力岗位Id")
        private Integer hrPostId;

        @ApiModelProperty(value = "岗位名称")
        private String postName;

        @ApiModelProperty(value = "HR任职记录code,任职唯一识别编码")
        private String jobCode;

        @ApiModelProperty(value = "所属组织Id")
        private Integer orgId;

        @ApiModelProperty("所属HR组织机构ID")
        private Integer hrOrgId;

        @ApiModelProperty("树结构路径")
        private String hrOrgTreePath;

        @ApiModelProperty(value = "所属组织名称")
        private String orgName;
    }
}
