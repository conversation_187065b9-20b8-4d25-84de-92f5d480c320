package com.cfpamf.ms.insur.report.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.feign.OmsFacade;
import com.cfpamf.ms.insur.report.feign.req.GetEffectiveShareInfoQueryDTO;
import com.cfpamf.ms.insur.report.feign.resp.OmsBaseResponse;
import com.cfpamf.ms.insur.report.feign.resp.vo.MarketingForwardEffectiveInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@Slf4j
@Service("omsBaseService")
public class OmsBaseService{

    @Autowired
    private OmsFacade omsFacade;

    @RequestMapping(value = "/getEffectiveShareInfo", method = RequestMethod.POST)
    public List<MarketingForwardEffectiveInfo> getEffectiveShareInfo(@RequestBody GetEffectiveShareInfoQueryDTO getEffectiveShareInfoQueryDTO){
        log.info("getEffectiveShareInfo req= {}",JSON.toJSONString(getEffectiveShareInfoQueryDTO));
        OmsBaseResponse<List<MarketingForwardEffectiveInfo>> response = null;
        try{
            response = omsFacade.getEffectiveShareInfo(getEffectiveShareInfoQueryDTO);
            log.info("getEffectiveShareInfo res= {}",JSON.toJSONString(response));
        }catch(Exception e){
            log.warn("getEffectiveShareInfo异常 req= {}", JSON.toJSONString(getEffectiveShareInfoQueryDTO),e);
            throw new MSBizNormalException("", "创建跳转链接异常");
        }
        if (!response.isSuccess()){
            log.warn("getEffectiveShareInfo失败 req= {}", JSON.toJSONString(getEffectiveShareInfoQueryDTO));
            throw new MSBizNormalException("", "创建跳转链接失败");
        }
        return response.getData();
    }
}
