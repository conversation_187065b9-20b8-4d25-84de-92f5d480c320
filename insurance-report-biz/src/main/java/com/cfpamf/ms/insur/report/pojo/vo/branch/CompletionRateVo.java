package com.cfpamf.ms.insur.report.pojo.vo.branch;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/3/30 18:07
 */
@Data
public class CompletionRateVo {

    /**
     * 当月保费目标完成率
     */
    @ApiModelProperty(value = "当月保费目标完成率")
    private BigDecimal currentMonth;

    /**
     * 截至当月保费目标完成率
     */
    @ApiModelProperty(value = "截至当月保费目标完成率")
    private BigDecimal presentCurrentMonth;

    /**
     * 全年保费目标完成率
     */
    @ApiModelProperty(value = "全年保费目标完成率")
    private BigDecimal currentYear;
}
