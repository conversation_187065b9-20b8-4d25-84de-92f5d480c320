package com.cfpamf.ms.insur.report.util;

import com.cfpamf.ms.insur.report.constant.BaseConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 获取http request 参数信息
 *
 * <AUTHOR>
 */
public class HttpRequestUtil {

    //private static JwtHelper jwtHelper = SpringFactoryUtil.getBean(JwtHelper.class);

    private HttpRequestUtil() {
    }

    /**
     * 获取用户Id
     *
     * @return
     */
    /*@SuppressWarnings({"unchecked", "rawtypes"})
    public static String getUserId() {
        String token = getToken();
        if (token == null) {
            return null;
        }
        JwtUserInfo jwtUserInfo = jwtHelper.getUserFromToken(token);
        return jwtUserInfo.getJobNumber();
    }

    *//**
     * 获取当前用户信息
     *
     * @return
     *//*
    public static JwtUserInfo getUser() {
        String token = getToken();
        if (token == null) {
            return null;
        }
        JwtUserInfo jwtUserInfo = jwtHelper.getUserFromToken(token);
        return jwtUserInfo;
    }

    *//**
     * 获取当前用户信息
     *
     * @return
     *//*
    public static JwtUserInfo getUserOrThrowExp() {
        String token = getToken();
        if (token == null) {
            throw new MSBizNormalException("", "登录信息丢失");
        }
        JwtUserInfo jwtUserInfo = jwtHelper.getUserFromToken(token);
        return jwtUserInfo;
    }


    *//**
     * 获取用户Id
     *
     * @return
     *//*
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static String getUserName() {
        String token = getToken();
        if (token == null) {
            return null;
        }
        JwtUserInfo jwtUserInfo = jwtHelper.getUserFromToken(token);
        return jwtUserInfo.getUserName();
    }*/

    /**
     * 获取token
     *
     * @return
     */
    public static String getToken() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return null;
        }
        String token = request.getHeader(BaseConstants.API_AUTH_NAME);
        if (StringUtils.isEmpty(token)) {
            token = request.getParameter(BaseConstants.API_AUTH_NAME);
        }
        return token;
    }
    //

    /**
     * 获取微信openid
     *
     * @return
     */
    public static String getWxOpenId() {
        HttpServletRequest request = getRequest();
        String openId = request.getHeader(BaseConstants.WX_HTTP_HEAD_OPENID);
        if (StringUtils.isEmpty(openId)) {
            openId = request.getParameter(BaseConstants.WX_HTTP_HEAD_OPENID);
        }
        return openId;
    }

    /**
     * 获取http 请求头 user-agent
     *
     * @return
     */
    public static String getUserAgent() {
        return getRequest().getHeader("user-agent");
    }

    /**
     * 获取http 请求头 referer
     *
     * @return
     */
    public static String getReferer() {
        return getRequest().getHeader("referer");
    }

    /**
     * 获取requestUrl
     *
     * @return
     */
    public static String getRequestURI() {
        HttpServletRequest request = getRequest();
        if (request != null) {
            return getRequest().getRequestURI();
        }
        return "";
    }

    /**
     * 获取request请求所有参数
     *
     * @return
     */
    public static Map<String, String> getParameterMap() {
        Map<String, String> params = new HashMap<>();
        HttpServletRequest request = getRequest();
        if (request == null) {
            return params;
        }
        Enumeration<String> enums = getRequest().getParameterNames();
        while (enums.hasMoreElements()) {
            String paraName = enums.nextElement();
            params.put(paraName, getRequest().getParameter(paraName));
        }
        return params;
    }

    /**
     * 获取request请求所有参数
     *
     * @param request
     * @return
     */
    public static Map<String, String> getParameterMap(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();
        Enumeration<String> enums = request.getParameterNames();
        while (enums.hasMoreElements()) {
            String paraName = enums.nextElement();
            params.put(paraName, request.getParameter(paraName));
        }
        return params;
    }

    /**
     * 获取客户端Id
     *
     * @param request
     * @return
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        String[] ips = ip.split(",");
        if (ips.length > 0) {
            ip = ips[ips.length - 1];
        }
        return org.apache.commons.lang3.StringUtils.trim(ip);
    }

    /**
     * 获取当前请求主体
     *
     * @return
     */
    public static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        }
        return null;

    }

    /**
     * 获取工号
     *
     * @return
     */
   /* public static String getUserIdOrThrowExp() {
        return getUserOrThrowExp().getJobNumber();
    }*/
}
