package com.cfpamf.ms.insur.report.pojo.vo.assistant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;


/**
 * 保险助手 区域维度
 *
 * <AUTHOR>
 * @since 2024/2/21 14:11
 */
@Data
@ApiModel("业绩排行")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantRankAmtVO {

    @ApiModelProperty("维度名称")
    String dimName;

    @ApiModelProperty("维度编码")
    String dimCode;

    @ApiModelProperty("排名")
    Integer rankNum;

    @ApiModelProperty("标准保费")
    BigDecimal assessConvertInsuranceAmt;

    @ApiModelProperty("标准保费目标")
    BigDecimal assessConvertInsuranceAmtTarget;

    @ApiModelProperty("标准保费完成率")
    BigDecimal assessConvertInsuranceAmtAchieveRate;
}
