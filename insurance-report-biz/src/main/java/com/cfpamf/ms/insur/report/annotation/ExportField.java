package com.cfpamf.ms.insur.report.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 下载字段注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface ExportField {

    /**
     * 字段名称
     *
     * @return
     */
    String name() default "";

    /**
     * 排序
     *
     * @return
     */
    int order() default 0;

    /**
     * 类型
     *
     * @return
     */
    String type() default "string";

    int width() default 0;
}
