package com.cfpamf.ms.insur.report.service.diagnosis;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DiagnosisTypeEnum {
    /**
     * 诊断与优化
     */
    DIAGNOSIS_AND_CONCLUSION,
    /**
     * 标准保费小结
     */
    CLASSIC_INSURANCE_AMOUNT_SUMMARY,
    /**
     * 异业保费配比小结
     */
    INSURANCE_AMOUNT_RATE_SUMMARY,
    /**
     * 客户留存率小结
     */
    RETENTION_RATE_SUMMARY,
    ;
}
