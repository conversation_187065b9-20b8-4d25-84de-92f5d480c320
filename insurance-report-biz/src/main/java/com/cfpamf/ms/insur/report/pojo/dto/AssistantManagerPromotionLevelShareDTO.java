package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssistantManagerPromotionLevelShareDTO {

    public AssistantManagerPromotionLevelShareDTO(Integer rank){
        this.rank = rank;
    }

    @ApiModelProperty(value = "层级名称", notes = "")
    String name="名称";

    @ApiModelProperty(value = "当月分享数", notes = "")
    String smShareCnt="1万";

    @ApiModelProperty(value = "当月人均分享数", notes = "")
    String smShareAvg="123";

    @ApiModelProperty(value = "排名", notes = "")
    Integer rank;
}
