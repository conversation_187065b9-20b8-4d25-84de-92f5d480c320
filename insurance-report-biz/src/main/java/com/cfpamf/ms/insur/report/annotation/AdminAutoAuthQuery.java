package com.cfpamf.ms.insur.report.annotation;



import com.cfpamf.ms.insur.report.aspect.SelfAuthAbstractHandler;
import com.cfpamf.ms.insur.report.enums.EnumBmsRole;

import java.lang.annotation.Documented;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 权限的优先级
 * 如果是角色匹配 则不做任何处理 return
 * 如果是机构管理员并且需要判断 添加区域机构权限 return
 * 如果binduserType 添加userId或agentId return
 * allRole > orgAdminArea > bindUserType
 * Create By zhengjing on 2020/2/17 15:02
 */
@Documented
@Inherited
@Retention(RUNTIME)
@Target({METHOD})
public @interface AdminAutoAuthQuery {

    /**
     * 查看所有权限的角色 优先级 1
     *
     * @return
     */
    EnumBmsRole[] allRole() default {};

    /**
     * 如果是机构对接人是否需要添加区域权限 优先级2
     *
     * @return
     */
    boolean orgAdminArea() default true;

    /**
     *
     * @return
     */
    Class<? extends SelfAuthAbstractHandler>[] selfAuth() default {};

}
