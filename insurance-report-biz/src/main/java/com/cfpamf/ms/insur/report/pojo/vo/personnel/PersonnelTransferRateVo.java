package com.cfpamf.ms.insur.report.pojo.vo.personnel;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 个人转化率vo
 *
 * <AUTHOR>
 * @date 2021/4/7 15:28
 */
@Data
public class PersonnelTransferRateVo {

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @ExportField(name = "工号", order = 2)
    private String jobNumber;

    /**
     * 推荐人
     */
    @ApiModelProperty(value = "推荐人")
    @ExportField(name = "推荐人", order = 3)
    private String recommendUserName;
    /**
     * 区域
     */
    @ExportField(name = "区域", order = 0)
    @ApiModelProperty("区域")
    private String regionName;

    /**
     * 分支
     */
    @ExportField(name = "分支", order = 1)
    @ApiModelProperty("分支")
    private String orgName;

    /**
     * 保险客户数
     */
    @ExportField(name = "保险客户数", order = 4, type = "Integer")
    @ApiModelProperty("保险客户数")
    private Integer safesPerQty;

    /**
     * 线下同期贷款客户数
     */
    @ExportField(name = "线下同期贷款客户数", order = 5, type = "Integer")
    @ApiModelProperty("线下同期贷款客户数")
    private Integer loanPerQty;

    /**
     * 线下同期贷款且是保险客户数
     */
    @ExportField(name = "线下同期贷款且是保险客户数", order = 6, type = "Integer")
    @ApiModelProperty("线下同期贷款且是保险客户数")
    private Integer safesBorrowerPerQty;

    /**
     * 线下同期共借人且是保险客户数
     */
    @ExportField(name = "线下同期共借人且是保险客户数", order = 7, type = "Integer")
    @ApiModelProperty("线下同期共借人且是保险客户数")
    private Integer safesCoBorrowerPerQty;

    /**
     * 线下同期担保人且是保险客户数
     */
    @ExportField(name = "线下同期担保人且是保险客户数", order = 8, type = "Integer")
    @ApiModelProperty("线下同期担保人且是保险客户数")
    private Integer safesGuarantorPerQty;

    /**
     * 线下同期贷款相关客户数
     */
    @ExportField(name = "线下同期贷款相关客户数", order = 9, type = "Integer")
    @ApiModelProperty("线下同期贷款相关客户数")
    private Integer safesRelevanterPerQty;

    /**
     * 保险非信贷客户数
     */
    @ExportField(name = "保险非信贷客户数", order = 10, type = "Integer")
    @ApiModelProperty("保险非信贷客户数")
    private Integer safesNoLoanPerQty;


    /**
     * 信贷客户转化率
     */
    @ExportField(name = "信贷客户转化率", order = 11, type = "percentage")
    @ApiModelProperty("信贷客户转化率")
    private BigDecimal safesBorrowerTransferRatio;
    /**
     * 信贷相关客户转化率
     */
    @ExportField(name = "信贷相关客户转化率", order = 12, type = "percentage")
    @ApiModelProperty("信贷相关客户转化率")
    private BigDecimal safesOtherTransferRatio;

    /**
     * 保险客户中贷款客户占比
     */
    @ExportField(name = "保险客户中贷款客户占比", order = 13, type = "percentage")
    @ApiModelProperty("保险客户中贷款客户占比")
    private BigDecimal safesBorrowerRatio;

    /**
     * 保险客户中信贷相关客户占比
     */
    @ExportField(name = "保险客户中信贷相关客户占比", order = 14, type = "percentage")
    @ApiModelProperty("保险客户中信贷相关客户占比")
    private BigDecimal safesLoanRatio;
    /**
     * 非信贷客户占比
     */
    @ExportField(name = "非信贷客户占比", order = 15, type = "percentage")
    @ApiModelProperty("非信贷客户占比")
    private BigDecimal safesNoLoanRatio;
}
