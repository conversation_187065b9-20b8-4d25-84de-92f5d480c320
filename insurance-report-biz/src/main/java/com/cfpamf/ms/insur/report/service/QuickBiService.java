package com.cfpamf.ms.insur.report.service;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.feign.DataAuthFacade;
import com.cfpamf.ms.insur.report.pojo.CommonResult;
import com.cfpamf.ms.insur.report.pojo.dto.DataAuthDTO;
import com.cfpamf.ms.insur.report.util.QuickBiUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
@Slf4j
public class QuickBiService {

    @Value("${qbi.accessKeyId}")
    private String accessKeyId;

    @Value("${qbi.accessKeySecret}")
    private String accessKeySecret;

    @Value("${qbi.host}")
    private String host;

    @Value("${qbi.userId}")
    private String userId;

    @Autowired
    DataAuthFacade dataAuthFacade;
    @Autowired
    private BmsService bmsService;
    /**
     * 通用权限处理
     *
     * @param
     * @param
     * @param isDashboard
     * @return
     */

    public String generateQbiUrl(String pageId, Integer isDashboard) {
        JSONObject param = dataAuthProcess();
        return generateUrl(pageId, param ,isDashboard);
    }

    public JSONObject dataAuthProcess() {
        JSONObject o = new JSONObject();
        String token = bmsService.getToken();
        CommonResult<DataAuthDTO> dataAuthDTOCommonResult = dataAuthFacade.dataAuth(token);
        if(dataAuthDTOCommonResult.isSuccess()){
            DataAuthDTO data = dataAuthDTOCommonResult.getData();
            if(data.getUserId()!=null){
                log.info("所查员工可查看数据-客户经理:{}", data.getUserId());
                o.put("emp_id",data.getUserId());
            }
            if(data.getOrgName()!=null&&data.getUserId()==null){
                log.info("所查员工可查看数据-督导/分支负责人:{}", data.getOrgName());
                o.put("bch_name",data.getOrgName());
            }
            if(data.getOrgName()==null&&data.getUserId()==null&&data.getRegionName()!=null){
                log.info("所查员工可查看数据-区域负责人:{}", data.getRegionName());
                o.put("area_name",data.getRegionName());
            }
            if(data.getOrgName()==null&&data.getUserId()==null&&data.getRegionName()==null){
                log.info("所查员工可查看数据-所有:");
            }
        }
        return o;
    }

    public String generateUrl(String pageId, JSONObject param, Integer isDashboard) {
        try {
            return QuickBiUtils.getReportUri(pageId,isDashboard, "180", accessKeyId, accessKeySecret, host, userId, param);
        } catch (Exception e) {
            log.warn("生成url异常", e);
        }
        return null;
    }

    public String generateQbiUrl1(String pageId, Integer isDashboard) {
        JSONObject param = new JSONObject();
        return generateUrl(pageId, param ,isDashboard);
    }
}
