package com.cfpamf.ms.insur.report.service.wx;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.enums.ExcptEnum;
import com.cfpamf.ms.insur.report.feign.resp.vo.UserDetailVO;
import com.cfpamf.ms.insur.report.pojo.query.WxReportQuery;
import com.cfpamf.ms.insur.report.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.report.service.BmsService;
import com.cfpamf.ms.insur.report.util.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import static com.cfpamf.ms.insur.report.util.HttpRequestUtil.*;

@Slf4j
public abstract class WxAbstractService {
    /**
     * redis 缓存
     */
    @Autowired
    protected RedisUtil<String, String> redisUtil;

    @Autowired
    private PermissionUtil permissionUtil;

    @Autowired
    private BmsService bmsService;

    /**
     * 验证微信用户权限是从微信菜单过来
     */
    public WxSessionVO checkAuthority() {
        return checkAuthority(getWxOpenId(), getToken());
    }

    /**
     * 验证微信用户权限是从微信菜单过来
     *
     * @param wxOpenId
     * @param authorization
     */
    public WxSessionVO checkAuthority(String wxOpenId, String authorization) {
        if (StringUtils.isEmpty(wxOpenId)) {
            log.warn("微信用户token失效 wxOpenId={}, authorization={}", wxOpenId, authorization);
            log.warn("微信用户token失效 user-agent={}, referer={}", getUserAgent(), getReferer());
            throw new MSBizNormalException(ExcptEnum.INVALID_TOKEN_501019);
        }
        WxSessionVO session = getWxSession(wxOpenId);

        if (session == null || !Objects.equals(session.getAuthorization(), authorization)) {
            log.warn("微信用户token失效 wxOpenId={}, authorization={}, session={}", wxOpenId, authorization, JSON.toJSONString(session));
            log.warn("微信用户token失效 user-agent= {}, referer={}", getUserAgent(), getReferer());
            throw new MSBizNormalException(ExcptEnum.INVALID_TOKEN_501019);
        }
        return session;
    }
    /**
     * 获取微信session
     *
     * @param wxOpenId
     * @return
     */
    public WxSessionVO getWxSession(String wxOpenId) {
        String json = redisUtil.get(wxOpenId);
        log.info("json={}",json);
        if (!StringUtils.isEmpty(json)) {
            return JSON.parseObject(json, WxSessionVO.class);
        }
        return null;
    }

    /**
     * 查询缓存公共类
     *
     * @param queryFun
     * @param query
     * @param <T>
     * @return
     */
    @SuppressWarnings("all")
    public <T> PageInfo<T> commonQuery(Function<WxReportQuery, List<T>> queryFun, WxReportQuery query) {
        buildDataPermissionPageQuery(query);
        PageHelper.startPage(query.getPage(), query.getSize());
        PageInfo<T> pageInfo = new PageInfo<>(queryFun.apply(query));
        return pageInfo;
    }

    /**
     * 构造数据权限查询条件
     *
     * @param query
     */
    private void buildDataPermissionPageQuery(WxReportQuery query) {
        //验证session
        WxSessionVO session = checkAuthorityEmployee();

        CommonUtil.validateQueryDate(query);

        // 为了复用ContextUserUtil 必须默认后端用户session到threadLocal
        String token = session.getBmsToken();
        if(StringUtils.isEmpty(token)){
            token = session.getAuthorization();
        }
        UserDetailVO userDetail = SpringFactoryUtil.getBean(BmsService.class).getUserDetailByToken(token);
        ThreadUserUtil.userDetailTL.set(userDetail);
        Date s = new Date();
        //log.info("buildDataPermissionPageQuery开始时间：{}",s);
        SpringFactoryUtil.getBean(PermissionUtil.class).buildDataPermissionPageQuery(query);
        //log.info("查询参数：{}",query);
        //log.info("buildDataPermissionPageQuery花费时间:{}",new Date().getTime() - s.getTime());
        ThreadUserUtil.userDetailTL.remove();
    }

    /**
     *  验证微信用户权限(公司员工)
     */
    public WxSessionVO checkAuthorityEmployee() {
        String wxOpenId = getWxOpenId();
        String authorization = getToken();
        WxSessionVO session = checkAuthority(wxOpenId,authorization);
        log.info("wxOpenId={}, authorization={}, session={}",wxOpenId,authorization, JSON.toJSONString(session));
        if (!Objects.equals(session.getUserType(), BaseConstants.USER_TYPE_EMPLOYEE)) {
            log.warn("微信用户没有公司员工模块权限wxOpenId={}, authorization={}, session={}", wxOpenId, authorization, JSON.toJSONString(session));
            throw new MSBizNormalException(ExcptEnum.INVALID_USER_AUTH_844448);
        }
        return session;
    }


}
