package com.cfpamf.ms.insur.report.service.wx;


import com.cfpamf.ms.insur.report.dao.dw.SmDcReportMapper;
import com.cfpamf.ms.insur.report.dao.dw.WxDcReportMapper;
import com.cfpamf.ms.insur.report.pojo.query.WxReportQuery;
import com.cfpamf.ms.insur.report.pojo.vo.WxOrgBusinessVO;
import com.cfpamf.ms.insur.report.pojo.vo.WxPersonalBusinessVO;
import com.cfpamf.ms.insur.report.pojo.vo.WxRegionBusinessVO;
import com.cfpamf.ms.insur.report.util.CommonUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;

@Service
@Slf4j
public class WxReportService extends WxAbstractService{

    @Autowired
    private WxDcReportMapper wxDcReportMapper;

    public PageInfo<WxPersonalBusinessVO> listWxPersonalBusinessDetailsByPage(WxReportQuery query){

        return  commonQuery(wxDcReportMapper::listWxPersonalBusinessDetails, query);
    }

    public PageInfo<WxOrgBusinessVO> listWxOrgBusinessDetailsByPage(WxReportQuery query){

        return  commonQuery(wxDcReportMapper::listWxOrgBusinessDetails, query);
    }

    public PageInfo<WxRegionBusinessVO> listWxRegionBusinessDetailsByPage(WxReportQuery query){

        return  commonQuery(wxDcReportMapper::listWxRegionBusinessDetails, query);
    }


}
