package com.cfpamf.ms.insur.report.util;


import com.cfpamf.ms.insur.report.constant.CacheKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.cache.RedisCacheManager;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @description: redis 缓存工具类
 * @author: zhangnayi
 * @create: 2018-07-11 11:51
 **/
@Slf4j
@Component
public class RedisUtil<K, V> {


    @Autowired
    private RedisTemplate<K, V> redisTemplate;

    /*@SuppressWarnings("unchecked")
    @PostConstruct
    public void init() {
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        GenericJackson2JsonRedisSerializer valueSerializer = new GenericJackson2JsonRedisSerializer();
        redisTemplate.setValueSerializer(valueSerializer);
        redisTemplate.setHashValueSerializer(valueSerializer);
    }

    @Bean
    public CacheManager cacheManager(RedisTemplate redisTemplate) {
        RedisCacheManager cacheManager = new RedisCacheManager(redisTemplate);
       // cacheManager.setUsePrefix(true);
        Map<String, Long> expired = new HashMap<>();
        expired.put(CacheKeyConstants.WX_LBT_IMAGE, 43200L);
        expired.put(CacheKeyConstants.PRODUCT_LIST, 86400L);
        expired.put(CacheKeyConstants.OTHER_PREFIX, 86400L);
        expired.put(CacheKeyConstants.HOUR_PREFIX, 60 * 60L);
       // cacheManager.setExpires(expired);
        return cacheManager;
    }*/

    /**
     * 写入缓存
     *
     * @param key
     * @param value
     * @return
     */
    public void set(final K key, V value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 写入hash缓存
     *
     * @param key
     * @param value
     * @return
     */
    public void hashSet(final K key, K hashKey, V value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }

    /**
     * 写入hash缓存
     *
     * @param key
     * @param hashKey
     * @return
     */
    public void hashRemove(final K key, K hashKey) {
        redisTemplate.opsForHash().delete(key, hashKey);
    }

    /**
     * 写入缓存设置时效时间
     *
     * @param key
     * @param value
     * @return
     */
    public void set(final K key, V value, Long expireTime) {
        redisTemplate.opsForValue().set(key, value, expireTime, TimeUnit.SECONDS);
    }

    /**
     * 写入缓存设置时效时间
     *
     * @param key
     * @return
     */
    public boolean expire(final K key, Long expireTime) {
        return redisTemplate.expire(key, expireTime, TimeUnit.SECONDS);
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(K key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 如果不存在写入缓存
     *
     * @param key
     * @param value
     * @return
     */
    public boolean setnx(final K key, V value) {
        ValueOperations<K, V> operations = redisTemplate.opsForValue();
        return operations.setIfAbsent(key, value);
    }

    /**
     * Set and return its old value.
     *
     * @param key
     * @param value
     * @return
     */
    public V getAndSet(final K key, V value) {
        ValueOperations<K, V> operations = redisTemplate.opsForValue();
        return operations.getAndSet(key, value);
    }

    /**
     * 批量删除对应的value
     *
     * @param keys
     */
    public void remove(final K... keys) {
        for (K key : keys) {
            remove(key);
        }
    }

    /**
     * 批量删除key
     *
     * @param pattern
     */
    public void removePattern(final K pattern) {
        Set<K> keys = redisTemplate.keys(pattern);
        if (keys.size() > 0) {
            redisTemplate.delete(keys);
        }
    }

    /**
     * 删除对应的value
     *
     * @param key
     */
    public void remove(final K key) {
        if (exists(key)) {
            redisTemplate.delete(key);
        }
    }

    /**
     * 清空redis
     */
    @SuppressWarnings("unchecked")
    public void flush() {
        Set<K> keys = redisTemplate.keys((K) "*");
        redisTemplate.delete(keys);
    }

    /**
     * redis 原子增加值  返回增加后的值
     */
    public long increment(final K key, long delta) {
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 判断缓存中是否有对应的value
     *
     * @param key
     * @return
     */
    public boolean exists(final K key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 读取缓存
     *
     * @param key
     * @return
     */
    public V get(final K key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 模糊检索获取key
     *
     * @param pattern
     * @return
     */
    public Set<K> keys(K pattern) {
        return redisTemplate.keys(pattern);
    }

    /**
     * 哈希 添加
     *
     * @param key
     * @param hashKey
     * @param value
     */
    public boolean hmPutIfAbsent(K key, K hashKey, V value) {
        return redisTemplate.opsForHash().putIfAbsent(key, hashKey, value);
    }

    /**
     * 哈希 添加
     *
     * @param key
     * @param hashKey
     * @param value
     */
    public void hmSet(K key, K hashKey, V value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }

    /**
     * 哈希获取数据
     *
     * @param key
     * @param hashKey
     * @return
     */
    public Object hmGet(K key, K hashKey) {
        return redisTemplate.opsForHash().get(key, hashKey);
    }

    /**
     * 哈希获取key
     *
     * @param key
     * @return
     */
    public Set<Object> hmKeys(K key) {
        return redisTemplate.opsForHash().keys(key);
    }

    /**
     * 列表添加
     *
     * @param k
     * @param v
     */
    public void lPush(K k, V v) {
        redisTemplate.opsForList().rightPush(k, v);
    }

    /**
     * 列表获取
     *
     * @param k
     * @param l
     * @param l1
     * @return
     */
    public List<V> lRange(K k, long l, long l1) {
        return redisTemplate.opsForList().range(k, l, l1);
    }

    /**
     * 集合添加
     *
     * @param key
     * @param value
     */
    public void add(K key, V value) {
        redisTemplate.opsForSet().add(key, value);
    }

    /**
     * 集合获取
     *
     * @param key
     * @return
     */
    public Set<V> setMembers(K key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 有序集合添加
     *
     * @param key
     * @param value
     * @param scoure
     */
    public void zAdd(K key, V value, double scoure) {
        redisTemplate.opsForZSet().add(key, value, scoure);
    }

    /**
     * 有序集合获取
     *
     * @param key
     * @param scoure
     * @param scoure1
     * @return
     */
    public Set<V> rangeByScore(K key, double scoure, double scoure1) {
        return redisTemplate.opsForZSet().rangeByScore(key, scoure, scoure1);
    }

    /*public class MyRedisCachePrefix implements RedisCachePrefix {
        private final RedisSerializer serializer = new StringRedisSerializer();
        private final String delimiter;

        public MyRedisCachePrefix() {
            this(":");
        }

        public MyRedisCachePrefix(String delimiter) {
            this.delimiter = delimiter;
        }
        public byte[] prefix(String cacheName) {
            return serializer.serialize((delimiter != null ? "safe:".concat(cacheName).concat(delimiter) : "safe:".concat(cacheName).concat(":")));
        }
    }*/

    /**
     * redis序列化改造
     *
     * <AUTHOR>
     **/
    public class StringRedisSerializer implements RedisSerializer<Object> {

        private final Charset charset;

        public StringRedisSerializer() {
            this(Charset.forName(StandardCharsets.UTF_8.name()));
        }

        public StringRedisSerializer(Charset charset) {
            Assert.notNull(charset, "Charset must not be null!");
            this.charset = charset;
        }

        @Override
        public Object deserialize(byte[] bytes) {
            return (bytes == null ? null : new String(bytes, charset));
        }

        @Override
        public byte[] serialize(Object object) {
            return (object == null ? null : object.toString().getBytes(charset));
        }
    }
}
