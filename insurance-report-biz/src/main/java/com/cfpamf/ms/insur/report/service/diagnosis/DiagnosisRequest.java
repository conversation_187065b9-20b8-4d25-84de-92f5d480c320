package com.cfpamf.ms.insur.report.service.diagnosis;

import com.cfpamf.ms.insur.report.enums.OrgLevelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 诊断请求参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/02/27
 */
@Data
public class DiagnosisRequest implements Serializable {

    /*=============== 业务参数 start===================*/
    /**
     * 组织编码，这里是抽象的组织编码，区域、片区、分支的编码都往这里面塞，这是前端传入的。
     * 当需要查看全部区域，则orgCodes=null，orgLevel=COUNTRY
     * 当需要查看A区域的全部数据，则orgCodes = A区域的orgCode，orgLevel=AREA
     * 当需要查看A区域的A1、A2片区的全部数据，则orgCodes = [A1的orgCode,A2的orgCode]，orgLevel=DISTRICT
     * 当需要查看A12分支的数据，则orgCode = A12分支的orgCode，orgLevel = BRANCH
     */
    private List<String> orgCodes;
    /**
     * 机构级别
     */
    private OrgLevelEnum orgLevel;
    /**
     * 诊断类型
     */
    private DiagnosisTypeEnum diagnosisType;
    /**
     * 分区参数
     */
    private String pt;
    /*=============== 业务参数 end===================*/
    /*=============== 数据权限参数 start===================*/
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 分支名称
     */
    private String branchName;
    /**
     * 账号授权区域编码
     */
    private String areaCodeDataAuth;
    /**
     * 账号授权分支编码
     */
    private String bchCodeDataAuth;

    /**
     * 黑名单分支，不参与业绩诊断
     */
    private List<String> blackBchList;
    /*=============== 数据权限参数 end===================*/


    @ApiModelProperty("区域下是否有片区 true 有 , false 无")
    private boolean existDistrict;
}
