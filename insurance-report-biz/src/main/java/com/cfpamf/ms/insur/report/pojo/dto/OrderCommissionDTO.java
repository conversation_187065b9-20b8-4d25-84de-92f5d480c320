package com.cfpamf.ms.insur.report.pojo.dto;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

/**
 * 时间段内佣金信息
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderCommissionDTO {
    /**
     * 保单数
     */
    private Integer insuredCnt;
    /**
     * 佣金
     */
    private BigDecimal paymentAmt;
    /**
     * 保费金额
     */
    private BigDecimal insuredAmt;
}
