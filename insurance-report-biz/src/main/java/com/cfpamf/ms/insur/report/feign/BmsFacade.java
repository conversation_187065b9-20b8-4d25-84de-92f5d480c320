package com.cfpamf.ms.insur.report.feign;


import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.report.feign.constants.BmsConstantCore;
import com.cfpamf.ms.insur.report.feign.req.FdOrganizationQuery;
import com.cfpamf.ms.insur.report.feign.resp.dto.ElementTreeNodeDTO;
import com.cfpamf.ms.insur.report.feign.resp.vo.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "bms-service-biz", url = "${bms.api.url}")
public interface BmsFacade {
    /**
     * 获取当前登录用户基本信息、任职信息、岗位和角色信息（保险专用 勿动）
     */
    @ApiOperation(value = "获取当前登录用户基本信息、任职信息、岗位和角色信息（保险专用 勿动）", notes = "/user/detail/safes")
    @GetMapping(value = "/user/detail/safes")
    Result<UserDetailVO> getContextUserDetailForSafes(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization);

    /**
     * 获取登录用户的按钮权限列表
     */
    @GetMapping(value = "/user/permissions")
    Result<List<ModuleVO>> getAuthPermissions(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("systemId") Integer systemId, @RequestParam("postIds") int[] postIds);

    @ApiOperation(value = "获取区域分支树")
    @GetMapping(value = "/org/getAreaBranchTree")
    Result<List<ElementTreeNodeDTO>> getAreaBranchTree(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam("includeHeadOrg") boolean includeHeadOrg);

    @ApiOperation("获取组织机构管理人员信息")
    @PostMapping(value = "/org/list/detail")
    Result<List<FdOrgEmployeeVO>> getOrganizationInfos(@RequestBody FdOrganizationQuery dto);

    /**
     * 获取区域分支树信息
     *
     * @return
     */
    @GetMapping(value = "/org/regions")
    Result<List<FdOrgVO>> getAllRegionBranches(@RequestParam(value = "includeHeadOrg") boolean includeHeadOrg);

    /**
     * 获取整个组织机构树
     */
    @ApiOperation(value = "获取整个组织机构树", notes = "/org/getOrgTree")
    @GetMapping(value = "/org/getOrgTree")
    Result<List<ElementTreeNodeDTO>> getOrganizationTree();

    /**
     * 根据hrOrgId获取组织机构详情
     *
     * @param hrOrgId
     * @return
     */
    @GetMapping(value = "/org/getOrganizationByHrOrgId/{hrOrgId}")
    Result<OrganizationVO> getOrganizationByHrOrgId(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @PathVariable(value = "hrOrgId") int hrOrgId);

    /**
     * 判断是否是区域办公室
     */
    @ApiOperation(value = "判断是否是区域办公室", notes = "/org/isAreaOffice?orgId=xxx")
    @GetMapping(value = "/org/isAreaOffice")
    Result<Boolean> checkOrgIsAreaOffice(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam(value = "orgId") Integer orgId);

    @ApiOperation(value = "根据orgCode获取当前机构信息,如果当前机构是区域管理部则返回对应的区域信息，其他不变")
    @GetMapping(value = "/org/getBizOrgBaseVOByOrgCode")
    Result<OrganizationBaseVO> getBizOrgBaseVOByOrgCode(@RequestParam("orgCode") String orgCode);


    @ApiOperation(value = "根据hrOrgId所对应的级别获取对应下的所有子孙的分支信息", notes = "/org/listBranchesByHrOrgIds?hrOrgIds=xxx,xxx")
    @GetMapping(value = "/org/listBranchesByHrOrgIds")
    Result<List<OrganizationBaseVO>> listBranchesByHrOrgIds(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @RequestParam(value = "hrOrgIds") List<Integer> hrOrgIds);

    @ApiOperation(value = "根据hrParentId获取对应下的所有子孙的分支信息", notes = "/org/{hrParentId}/branches")
    @GetMapping(value = "/org/{hrParentId}/branches")
    Result<List<OrganizationBaseVO>> listBranchesByHrParentId(@RequestHeader(BmsConstantCore.JWT_KEY_AUTH) String authorization, @PathVariable(value = "hrParentId") Integer hrParentId);

    @ApiOperation(value = "根据orgCode获取所有上级组织机构详情")
    @PostMapping(value = "/org/getParentOrganizationsByOrgCodes", headers = {
            "Content-Type: application/json",
            "Accept: application/json"
    })
    Result<List<OrganizationParentVo>> getParentOrganizationsByOrgCodes(@RequestBody List<String> orgCodes);

}
