package com.cfpamf.ms.insur.report.feign.resp.vo;

import com.github.pagehelper.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JwtUserInfo implements Serializable {

    private static final long serialVersionUID = 3646927206154766695L;

    /**
     * 运营平台用户id
     */
    private Integer userId;

    /**
     * 运营平台员工id
     */
    private Integer employeeId;

    /**
     * 用户对应的HR中的Id
     */
    private Integer hrUserId;

    /**
     * 用户手机号
     */
    private String account;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户当前的主职/兼职工号
     */
    private String jobNumber;

    /**
     * 用户主职工号
     */
    private String masterJobNumber;

    /**
     * 运营平台机构id
     */
    private Integer orgId;

    /**
     * 机构对应的HR中的Id
     */
    private Integer hrOrgId;

    /**
     * 机构在HR中的Code
     */
    private String hrOrgCode;

    /**
     * 机构在HR中的Name(首取全名，为空则取简称)
     */
    private String hrOrgName;

    /**
     * 机构在HR中的hrOrgId层级树
     */
    private String hrOrgTreePath;

    /**
     * 当前用户的用户类别（1：平台管理员，2：平台员工用户，3：各子系统管理员）
     */
    private Integer userType;

    /**
     * token随机码,防止每次生成的token都一样
     */
    private String randomCode;

    /**
     * 是否为sso登录token
     */
    private Boolean useSso;

    /**
     * 灰度用户标识
     */
    private Integer gray;

    /**
     * 为哪个系统生成的token，sso改造新增字段
     */
    private Integer systemId;

    /**
     * 生成时间，用于最长有效期控制，sso改造新增字段
     */
    private Date createTime;

    /**
     * 用户名为空时取用户手机号
     *
     */
    public String getUserName() {
        if (StringUtil.isEmpty(this.userName)) {
            return this.account;
        }
        return this.userName;
    }

    /**
     * 工号为空时取用户手机号
     *
     */
    public String getJobNumber() {
        if (StringUtil.isEmpty(this.jobNumber)) {
            return this.account;
        }
        return this.jobNumber;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        JwtUserInfo jwtUser = (JwtUserInfo) o;

        return Objects.equals(userId, jwtUser.userId) &&
                Objects.equals(employeeId, jwtUser.employeeId) &&
                Objects.equals(hrUserId, jwtUser.hrUserId) &&
                Objects.equals(account, jwtUser.account) &&
                Objects.equals(orgId, jwtUser.orgId) &&
                Objects.equals(hrOrgId, jwtUser.hrOrgId) &&
                Objects.equals(gray, jwtUser.gray) &&
                Objects.equals(systemId, jwtUser.getSystemId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), userId, employeeId, hrUserId, account, orgId, hrOrgId, gray, systemId);
    }
}
