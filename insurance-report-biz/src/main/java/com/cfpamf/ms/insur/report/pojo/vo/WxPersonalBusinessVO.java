package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class WxPersonalBusinessVO extends WxBuisnessDetailsVO implements Serializable {

    @ApiModelProperty("分支机构")
    private String orgName;


    @ApiModelProperty("员工姓名")
    private String userName;


    @ApiModelProperty("员工工号")
    private String userId;
}
