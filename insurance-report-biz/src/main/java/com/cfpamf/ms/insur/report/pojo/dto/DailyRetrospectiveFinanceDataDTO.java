package com.cfpamf.ms.insur.report.pojo.dto;

import com.cfpamf.ms.insur.report.pojo.vo.BorrowerInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DailyRetrospectiveFinanceDataDTO {

    @ApiModelProperty(value = "客户经理工号")
    private String empCode;

    @ApiModelProperty(value = "业务类型，授信：CREDIT，放款：LOAN")
    private String businessType;

    @ApiModelProperty(value = "贷款产品类型")
    private String loanType;

    @ApiModelProperty(value = "申请单号")
    private String applSeq;

    @ApiModelProperty(value = "申请/放款金额")
    private String applAmount;

    @ApiModelProperty(value = "申请/放款时间")
    private String applTime;

    @ApiModelProperty(value = "借款人信息")
    private BorrowerInfoVo borrowerInfoVo;

    @ApiModelProperty(value = "借款人转化数")
    private Integer borrowerConvertNum;

    @ApiModelProperty(value = "共借人转化数")
    private Integer coborrowerConvertNum;

    @ApiModelProperty(value = "担保人转化数")
    private Integer bondsmanConvertNum;

    @ApiModelProperty(value = "转化保费")
    private BigDecimal conversionAmt;

    @ApiModelProperty(value = "转化保费配比")
    private BigDecimal conversionRate;

    public Integer getBorrowerConvertNum() {
        if(borrowerConvertNum == null){
            return 0;
        }
        return borrowerConvertNum;
    }

    public Integer getCoborrowerConvertNum() {
        if(coborrowerConvertNum == null){
            return 0;
        }
        return coborrowerConvertNum;
    }

    public Integer getBondsmanConvertNum() {
        if(bondsmanConvertNum == null){
            return 0;
        }
        return bondsmanConvertNum;
    }
}
