package com.cfpamf.ms.insur.report.pojo.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RenewalRateQuery extends ReportQuery implements Serializable {
    /**
     * 记录类型
     */
    @ApiModelProperty("记录类型 year 年数据 month 月数据")
    private String recordType;
}
