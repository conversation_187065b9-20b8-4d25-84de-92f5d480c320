package com.cfpamf.ms.insur.report.util;

import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.common.ms.util.DateUtil;
import com.google.common.collect.Lists;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 新旧日期类型处理
 *
 * <AUTHOR>
 * @create 2020-02-12
 */
public class LocalDateUtil {
    public static final String EN_LONG_S_TRANSVERSE_FORMAT = "yyyyMMddHHmmss";

    public static void main(String[] args) {
        System.out.println(getCurrMonthStartDate());
        System.out.println(getCurrMonthEndDate());

        System.out.println(getMonthStartDate(DateUtil.parseDate("2020-12-12")));
        System.out.println(getMonthEndDate(DateUtil.parseDate("2020-12-12")));

        System.out.println(getPreMonthEndDate());
    }

    private LocalDateUtil() {
    }

    public static LocalDate getCurrMonthStartDate() {
        return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
    }

    public static LocalDate getCurrMonthEndDate() {
        return LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
    }

    public static LocalDate getMonthStartDate(Date date) {
        return dateToLocalDate(date).with(TemporalAdjusters.firstDayOfMonth());
    }

    public static LocalDate getMonthEndDate(Date date) {
        return dateToLocalDate(date).with(TemporalAdjusters.lastDayOfMonth());
    }

    public static LocalDate getMonthStartDate(LocalDate date) {
        return date.with(TemporalAdjusters.firstDayOfMonth());
    }

    public static LocalDate getYearStartDate(LocalDate date) {
        return date.with(TemporalAdjusters.firstDayOfYear());
    }

    public static LocalDate getPreMonthEndDate() {
        return LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
    }


    public static LocalDate getMonthEndDate(LocalDate date) {
        return date.with(TemporalAdjusters.lastDayOfMonth());
    }

    public static LocalDateTime dateToLocaldatetime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    public static LocalDate dateToLocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.toLocalDate();
    }

    public static LocalTime dateToLocalTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.toLocalTime();
    }


    public static Date localDateTimeToUdate(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * 源时间是否在目标时间之后
     *
     * @param sourceTime
     * @param targetTime 格式(yyyy-MM-dd HH:mm:ss)
     * @return
     */
    public static boolean isAfter(LocalDateTime sourceTime, String targetTime) {
        return isAfter(sourceTime, targetTime, DateUtil.CN_LONG_FORMAT);
    }

    public static boolean isAfter(LocalDate sourceTime, Date date) {
        return sourceTime.isAfter(dateToLocalDate(date));
    }

    public static boolean isAfter(LocalDateTime sourceTime, String targetTime, String format) {
        LocalDateTime dateTime = LocalDateUtil.dateToLocaldatetime(stringToUdate(targetTime, format));
        return sourceTime.isAfter(dateTime);
    }

    /**
     * 判断日期是否相等
     *
     * @param sourceTime
     * @param targetDate
     * @return
     */
    public static boolean isEqual(LocalDate sourceTime, Date targetDate) {
        LocalDate dateTime = LocalDateUtil.dateToLocalDate(targetDate);
        return sourceTime.isEqual(dateTime);
    }

    /**
     * 两日期相隔
     *
     * @param endDate
     * @param startDate
     * @return
     */
    public static Period getPeriod(LocalDate endDate, Date startDate) {
        LocalDate dateTime = LocalDateUtil.dateToLocalDate(startDate);
        return Period.between(dateTime, endDate);
    }


    public static Date localDateToUdate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    public static Date localTimeToUdate(LocalTime localTime) {
        LocalDate localDate = LocalDate.now();
        LocalDateTime localDateTime = LocalDateTime.of(localDate, localTime);
        return localDateTimeToUdate(localDateTime);
    }

    public static Date stringToUdate(String sDate) {
        SimpleDateFormat simpledateformat = new SimpleDateFormat(EN_LONG_S_TRANSVERSE_FORMAT);
        return stringToUdate(sDate, simpledateformat);
    }

    public static Date stringToUdate(String sDate, String format) {
        SimpleDateFormat simpledateformat = new SimpleDateFormat(format);
        return stringToUdate(sDate, simpledateformat);
    }

    public static Date stringToUdate(String sDate, SimpleDateFormat simpledateformat) {
        if (StringUtils.isEmpty(sDate)) {
            throw new NullPointerException();
        }
        Date date;
        try {
            date = simpledateformat.parse(sDate);
        } catch (ParseException e) {
            throw new MSException("0000", "时间转换错误" + sDate, e);
        }
        return date;
    }

    /**
     * 日期格式化
     *
     * @param localDateTime
     * @param formatStyle
     * @return
     */
    public static String formatter(LocalDateTime localDateTime, String formatStyle) {
        return DateTimeFormatter.ofPattern(formatStyle).format(localDateTime);
    }

    /**
     * 相隔天数,跟但钱时间比较相差（比当前时间大就是负数）
     *
     * @param smdate
     * @return
     * @throws MSException
     */
    public static long daysBetween(Date smdate) {
        return daysBetween(smdate, new Date());
    }

    /**
     * 相隔天数（相反就是负数）
     *
     * @param smdate 开始时间
     * @param bdate  结束时间
     * @return
     * @throws MSException
     */
    public static long daysBetween(Date smdate, Date bdate) {
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        try {
            smdate = sdf.parse(sdf.format(smdate));
            bdate = sdf.parse(sdf.format(bdate));
        } catch (Exception e) {
            throw new MSException("0000", "时间转换错误" + smdate + "#" + bdate, e);
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(smdate);
        long time1 = cal.getTimeInMillis();
        cal.setTime(bdate);
        long time2 = cal.getTimeInMillis();
        return (time2 - time1) / (1000 * 3600 * 24);
    }

    /**
     * 获取两个日期间的月末集合
     * example:startDate 03-02 endDate 07-10 return[3.31,4.30,5.31,6.30]
     */
    public static void getMonthEndBetween(LocalDate startDate, LocalDate endDate, List<LocalDate> localDateList) {
        LocalDate monthEndDate = getMonthEndDate(startDate);
        if (endDate.isAfter(monthEndDate)) {
            localDateList.add(monthEndDate);
            getMonthEndBetween(startDate.plusMonths(1), endDate, localDateList);
        }
    }

    /**
     * 获取两个日期间的月末集合
     * example:startDate 03-02 endDate 07-10 return[3.31,4.30,5.31,6.30]
     */
    public static List<LocalDate> getMonthEndBetween(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> localDateList = Lists.newArrayList();
        getMonthEndBetween(startDate, endDate, localDateList);
        return localDateList;
    }

}
