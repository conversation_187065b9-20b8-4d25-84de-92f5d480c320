package com.cfpamf.ms.insur.report.web;

import com.cfpamf.ms.insur.report.pojo.CommonResult;
import com.cfpamf.ms.insur.report.pojo.query.AssistantAdminQuery;
import com.cfpamf.ms.insur.report.pojo.query.AssistantAdminRankQuery;
import com.cfpamf.ms.insur.report.pojo.query.AssistantDiagnosisQuery;
import com.cfpamf.ms.insur.report.pojo.vo.assistant.AssistantBchVO;
import com.cfpamf.ms.insur.report.service.AssistantAdminService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2024/2/22 17:27
 */
@RestController
@RequestMapping("assistant/admin")
@Api(tags = "AssistantAdmin 接口")
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AssistantAdminController {

    AssistantAdminService assistantAdminService;

    /**
     * 获取所有指标的信息
     *
     * @param assistantAdminQuery 包含查询条件的对象，通过RequestBody接收前端传来的JSON数据
     * @return AssistantBchVO 返回一个包含指标信息的视图对象
     */
    @ApiOperation(value = "获取所有指标")
    @PostMapping("single")
    public AssistantBchVO getSingleData(@RequestBody AssistantAdminQuery assistantAdminQuery) {
        // 调用服务层方法，根据查询条件获取指标信息
        return assistantAdminService.singleData(assistantAdminQuery);
    }

    @ApiOperation(value = "获取趋势图数据")
    @PostMapping("tendency")
    public List<AssistantBchVO> getTendencyChart(@RequestBody AssistantAdminQuery assistantAdminQuery) {
        return assistantAdminService.tendencyChart(assistantAdminQuery);
    }


    @ApiOperation(value = "表格相关接口 只有第一次传入 count = true")
    @PostMapping("rank")
    public PageInfo<Map<String,Object>> rankDw(@RequestBody AssistantAdminRankQuery assistantAdminQuery) {
        return assistantAdminService.rank(assistantAdminQuery);
    }

    @ApiOperation(value = "表格相关接口 会自动根据时间维度替换参数 目前只有保费排名用得上")
    @PostMapping("rankSmart")
    public PageInfo<Map<String,Object>> rankSmart(@RequestBody AssistantAdminRankQuery assistantAdminQuery) {
        return assistantAdminService.rankSmart(assistantAdminQuery);
    }

    @ApiOperation(value = "诊断相关接口")
    @PostMapping("diagnosis")
    public CommonResult<String> diagnosis(@RequestBody AssistantDiagnosisQuery query) {
        return CommonResult.successResult(assistantAdminService.diagnosis(query));
    }


}
