package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.Mask;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Create By zhengjing on 2020/9/15 17:34
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderDetailVO {

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    String channel;

    @ApiModelProperty("订单来源")
    String subChannel;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    String fhOrderId;

    /**
     * 投保产品名称
     */
    @ApiModelProperty(value = "投保产品名称")
    String productName;

    /**
     * 投保计划名称
     */
    @ApiModelProperty(value = "投保计划名称")
    String planName;

    @ApiModelProperty("保险公司ID")
    Integer companyId;

    /**
     * 投保人姓名
     */
    @ApiModelProperty(value = "投保人姓名")
    String applicantPersonName;

    @Mask(dataType = Mask.DataType.ID_CARD)
    @ApiModelProperty(value = "投保人证件号")
    String applicantIdNumber;

    @ApiModelProperty(value = "投保人手机号")
    String applicantMobile;

    @ApiModelProperty(value = "被保人姓名")
    String insuredPersonName;

    @Mask(dataType = Mask.DataType.ID_CARD)
    @ApiModelProperty(value = "被保人证件号")
    String insuredIdNumber;

    @ApiModelProperty(value = "被保人手机号")
    String insuredMobile;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    String policyNo;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    BigDecimal totalAmount;

    @ApiModelProperty("购买分数")
    Integer qty;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    String payStatus;

    /**
     * 保单状态
     */
    @ApiModelProperty(value = "保单状态")
    String appStatus;

    /**
     * 订单创建日期
     */
    @ApiModelProperty(value = "订单创建日期")
    LocalDateTime createTime;

    /**
     * 保险开始时间
     */
    @ApiModelProperty(value = "保险开始时间")
    LocalDate startTime;

    /**
     * 保险结束时间
     */
    @ApiModelProperty(value = "保险结束时间")
    LocalDate endTime;

    @ApiModelProperty("支付日期")
    LocalDate paymentTime;

    /**
     * 代理人名称
     */
    @ApiModelProperty(value = "代理人名称")
    String agentName;

    /**
     * 负责人姓名
     */
    @ApiModelProperty(value = "推荐人")
    private String recommendUserName;

    /**
     * 负责人手机号
     */
    @ApiModelProperty(value = "推荐人工号")
    private String recommendUserId;


    @ApiModelProperty(value = "当前负责人")
    private String adminUserName;

    @ApiModelProperty(value = "当前负责人工号")
    private String adminUserId;

    @ApiModelProperty(value = "被保人性别")
    String insuredPersonGender;

    @ApiModelProperty(value = "投保人性别")
    String applicantPersonGender;

    String regionName;
    String organizationName;
}
