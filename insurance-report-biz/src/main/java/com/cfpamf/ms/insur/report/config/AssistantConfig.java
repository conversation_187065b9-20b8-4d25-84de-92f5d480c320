package com.cfpamf.ms.insur.report.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties
@Data
public class AssistantConfig {
    @Value("${assistant.black.bchs:''}")
    String blackBchs;

}
