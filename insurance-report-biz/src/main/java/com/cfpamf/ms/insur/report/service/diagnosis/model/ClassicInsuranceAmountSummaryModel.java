package com.cfpamf.ms.insur.report.service.diagnosis.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 标准保费完成情况的小结数据模型类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/02/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ClassicInsuranceAmountSummaryModel extends AbstractDiagnosisModel {

    /**
     * 小结
     */
    private Summary summary;

    @Data
    public static class Summary {
        /**
         * 组织类型，取值可以参见{@link com.cfpamf.ms.insur.report.enums.OrgLevelEnum}
         */
        private String type; // 区域类型：COUNTRY,AREA,DISTRICT,BRANCH
        /**
         * 标准保费的目标是否完成
         */
        private boolean finished;
        /**
         * 标准保费月度TOP3的排名信息
         */
        private List<String> classicInsuranceAmountMonthRankNames;
        /**
         * 标准保费年度TOP3的排名信息
         */
        private List<String> classicInsuranceAmountYearRankNames;
    }
}
