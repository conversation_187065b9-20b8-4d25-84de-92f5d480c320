package com.cfpamf.ms.insur.report.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "保险组手员工指标单一趋势",description = "")
public class AssistantSalerSingleTrendDTO {
    @ApiModelProperty(name = "X轴，时间维度",notes = "")
    private List<String> ptList;
    @ApiModelProperty(name = "指标值",notes = "")
    private List<Double> valueList;
    @ApiModelProperty(name = "标准值",notes = "")
    private List<Double> standradList;
    @ApiModelProperty(name = "趋势图名称",notes = "")
    private String trendName;
}
