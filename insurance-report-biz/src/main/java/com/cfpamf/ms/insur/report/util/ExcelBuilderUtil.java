package com.cfpamf.ms.insur.report.util;


import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.annotation.ExportField;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.ReflectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * excel下载工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelBuilderUtil {

    public static final String LINE_String = "-";
    private static DateTimeFormatter DEFAULT_FMT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * workbook
     */
    private Workbook workbook = null;

    /**
     * sheet
     */
    private Sheet sheet = null;

    /**
     * 表头字段名称
     */
    private List<String> fieldNames = new ArrayList<>();

    /**
     * 字段名称->字段类型Map
     */
    private Map<String, String> ftMap = new HashMap<>();

    /**
     * 字段名称->字段长度Map
     */
    private Map<String, Integer> lenMap = new HashMap<>();

    private int startRow = 1;

    private ExcelBuilderUtil() {
    }

    /**
     * 构造实例
     */
    public static ExcelBuilderUtil newInstance() {
        return new ExcelBuilderUtil();
    }

    /**
     * 新建Excel sheet
     *
     * @param title
     * @return
     */
    public ExcelBuilderUtil createSheet(String title) {
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
        workbook = new SXSSFWorkbook(xssfWorkbook, 1);
        sheet = workbook.createSheet(title);
        return this;
    }

    /**
     * 初始化excel模板数据
     *
     * @param excelTemplateResourcePath excel模板源路径   for example：”xlsx/PersonnelPerformanceExcelTemplate.xlsx“
     * @return
     */
    public ExcelBuilderUtil initExcelTemplate(String excelTemplateResourcePath) {
        try {
            // 获取模板文件流
            ClassPathResource classPathResource = new ClassPathResource(excelTemplateResourcePath);
            InputStream inputStream = classPathResource.getInputStream();
            //初始化excel相关对象
            workbook = WorkbookFactory.create(inputStream);
            sheet = workbook.getSheetAt(0);
            startRow = sheet.getLastRowNum() + 1;
        } catch (Exception e) {
            log.error("init excel template fail ", e);
            throw new MSBizNormalException("", "init excel template fail ");
        }
        return this;
    }

    /**
     * 创建Excel表头
     *
     * @param clazz
     * @return
     */
    public ExcelBuilderUtil initSheetHead(Class clazz, boolean createSheetHead) {
        List<ExportField> exportFields = new ArrayList<>();
        Map<Integer, String> nameAliasMap = new HashMap<>(50);
        //解析导出字段信息
        parseExportFields(clazz, exportFields, nameAliasMap);
        //导出字段排序
        exportFields = exportFields.stream().sorted(Comparator.comparingInt(ExportField::order)).collect(Collectors.toList());
        //创建表头
        if (createSheetHead) {
            Row row = sheet.createRow(0);
            buildFieldNames(exportFields, nameAliasMap, row);
        } else {
            for (int i = 0, len = exportFields.size(); i < len; i++) {
                ExportField exportField = exportFields.get(i);
                //添加字段信息
                fieldNames.add(nameAliasMap.get(exportField.hashCode()));
            }
        }
        return this;
    }


    /**
     * 初始化sheet信息
     *
     * @param clazz
     * @return
     */
    public ExcelBuilderUtil initSheetHead(Class clazz) {
        return initSheetHead(clazz, false);
    }

    /**
     * 解析导出的class 扫描字段上的ExportField注解 获取导出信息
     * 不支持多级读取  不能读取类型的字段上有ExportField注解
     *
     * @param clazz
     * @param exportFields
     * @param nameAliasMap
     */
    private void parseExportFields(Class clazz, List<ExportField> exportFields, Map<Integer, String> nameAliasMap) {
        List<Class> clazzs = new ArrayList<>();
        clazzs.add(clazz);
        while (clazz.getSuperclass() != Object.class) {
            clazz = clazz.getSuperclass();
            clazzs.add(clazz);
        }
        for (int i = 0, len = clazzs.size(); i < len; i++) {
            clazz = clazzs.get(i);
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                ExportField exportField = field.getAnnotation(ExportField.class);
                if (exportField != null) {
                    exportFields.add(exportField);
                    nameAliasMap.put(exportField.hashCode(), field.getName());
                    ftMap.put(field.getName(), exportField.type());
                    lenMap.put(field.getName(), field.getName().length());
                }
            }
        }
    }

    /**
     * 创建Excel表头
     *
     * @param clazz
     * @return
     */
    public ExcelBuilderUtil initSheetHead(Class clazz, List<String> excludeFileds) {

        List<Class> clazzs = new ArrayList<>();
        clazzs.add(clazz);
        while (clazz.getSuperclass() != Object.class) {
            clazz = clazz.getSuperclass();
            clazzs.add(clazz);
        }
        List<ExportField> exportFields = new ArrayList<>();
        Map<Integer, String> nameAliasMap = new HashMap<>(50);
        lenMap = new HashMap<>(50);
        for (int i = 0, len = clazzs.size(); i < len; i++) {
            clazz = clazzs.get(i);
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                ExportField exportField = field.getAnnotation(ExportField.class);
                if (exportField != null && !excludeFileds.contains(field.getName())) {
                    exportFields.add(exportField);
                    nameAliasMap.put(exportField.hashCode(), field.getName());
                    ftMap.put(field.getName(), exportField.type());
                    lenMap.put(field.getName(), field.getName().length());
                }
            }
        }

        exportFields = exportFields.stream().sorted(Comparator.comparingInt(ExportField::order)).collect(Collectors.toList());
        Row row = sheet.createRow(0);
        buildFieldNames(exportFields, nameAliasMap, row);
        return this;
    }


    /**
     * 加入列表数据
     *
     * @param datas
     * @return
     * @throws NoSuchMethodException
     */
    public ExcelBuilderUtil addSheetData(List datas) throws NoSuchMethodException {
        if (CollectionUtils.isEmpty(datas)) {
            return this;
        }
        if (sheet == null) {
            sheet = workbook.createSheet("");
        }
        CellStyle moneyCellStyle = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        moneyCellStyle.setDataFormat(format.getFormat("0.00"));

        Row row = null;
        Cell cell = null;
        for (int i = 0, ilen = datas.size(); i < ilen; i++) {
            row = sheet.createRow(i + startRow);
            Object data = datas.get(i);
            for (int j = 0, jlen = fieldNames.size(); j < jlen; j++) {
                cell = row.createCell(j);
                String fieldName = fieldNames.get(j);
                int cellLen = 0;
                Method method = data.getClass().getMethod("get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1));
                Object result = ReflectionUtils.invokeMethod(method, data);
                if (result != null) {
                    String resultStr = result.toString();
                    if (Objects.equals(ftMap.get(fieldName), "money")) {
                        BigDecimal bigDecimal = new BigDecimal("0.00");
                        if (StringUtil.isNotEmpty(resultStr)) {
                            try {
                                bigDecimal = new BigDecimal(resultStr);
                            } catch (Exception e) {
                                //todo
                                throw new MSBizNormalException("", "文件下载失败", e);
                            }
                        }
                        cell.setCellStyle(moneyCellStyle);
                        cell.setCellValue(bigDecimal.doubleValue());
                        cellLen = resultStr.length();
                    } else if (Objects.equals(ftMap.get(fieldName), "percentage")) {
                        cell.setCellValue(resultStr + "%");
                        cellLen = resultStr.length() + 1;
                    } else if (Objects.equals(ftMap.get(fieldName), "integer")) {
                        if (StringUtil.isNotEmpty(resultStr) && !"null".equalsIgnoreCase(resultStr)) {
                            cell.setCellValue(Integer.valueOf(resultStr));
                        } else {
                            cell.setCellValue(0);
                        }
                        cellLen = resultStr.length();
                    } else {
                        if (result instanceof Date && Objects.equals(ftMap.get(fieldName), "dateTime")) {
                            cell.setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(result));
                            cellLen = 20;
                        } else if (result instanceof Date && Objects.equals(ftMap.get(fieldName), "yearMonth")) {
                            cell.setCellValue(new SimpleDateFormat("yyyy-MM").format(result));
                            cellLen = 10;
                        } else if (result instanceof Date) {
                            cell.setCellValue(new SimpleDateFormat("yyyy-MM-dd").format(result));
                            cellLen = 10;
                        } else if (result instanceof LocalDateTime) {
                            cell.setCellValue(DEFAULT_FMT.format((LocalDateTime) result));
                            cellLen = 20;
                        } else {
                            cell.setCellValue(resultStr);
                            cellLen = cell.getStringCellValue().length();
                        }
                    }
                    if (lenMap.get(fieldName) < cellLen) {
                        lenMap.put(fieldName, cellLen);
                    }
                } else {
                    cell.setCellValue(LINE_String);
                }
            }
        }
        for (int j = 0, jlen = fieldNames.size(); j < jlen; j++) {
            int wordLength = (lenMap.get(fieldNames.get(j)) + 2);
            if (wordLength > 255) {
                wordLength = 255;
            }
            sheet.setColumnWidth(j, wordLength * 256);
        }
        this.startRow = startRow + datas.size();
        return this;
    }

    /**
     * 输出OutputStream
     *
     * @param os
     * @throws IOException
     */
    public void write(OutputStream os) throws IOException {
        workbook.write(os);
        workbook.close();
        os.flush();
        os.close();
    }

    /**
     * 构建表头
     *
     * @param exportFields
     * @param nameAliasMap
     * @param row
     */
    private void buildFieldNames(List<ExportField> exportFields, Map<Integer, String> nameAliasMap, Row row) {
        for (int i = 0, len = exportFields.size(); i < len; i++) {
            Cell cell = row.createCell(i);
            ExportField exportField = exportFields.get(i);
            cell.setCellValue(exportField.name());
            fieldNames.add(nameAliasMap.get(exportField.hashCode()));
        }
    }

    public Sheet getSheet() {
        return sheet;
    }

    public Workbook getWorkbook() {
        return workbook;
    }
}
