package com.cfpamf.ms.insur.report.pojo.vo;

import com.cfpamf.ms.insur.report.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OrgRenewalRateVO extends RenewalRateVO implements Serializable {
    @ExportField(name = "区域", order = 2)
    @ApiModelProperty("区域")
    private String regionName;
    @ExportField(name = "分支机构" , order = 3)
    @ApiModelProperty("分支机构")
    private String orgName;

    @ExportField(name = "续保率排名", order = 9,type = "integer")
    @ApiModelProperty("续保率排名(全国)")
    private Integer renewalRateRankAll;


    @ApiModelProperty("续保率排名(区域内)")
    private Integer renewalRateRankArea;
}
