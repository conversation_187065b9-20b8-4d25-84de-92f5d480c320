package com.cfpamf.ms.insur.report.dao.dw;

import com.cfpamf.ms.insur.report.pojo.query.ReportQuery;
import com.cfpamf.ms.insur.report.pojo.vo.WxOrgBusinessVO;
import com.cfpamf.ms.insur.report.pojo.vo.WxPersonalBusinessVO;
import com.cfpamf.ms.insur.report.pojo.vo.WxRegionBusinessVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface WxDcReportMapper {
    /**
     * 微信端个人业绩报表
     * @param reportQuery
     * @return
     */
    List<WxPersonalBusinessVO> listWxPersonalBusinessDetails(ReportQuery reportQuery);
    /**
     * 微信端机构业绩报表
     * @param reportQuery
     * @return
     */
    List<WxOrgBusinessVO> listWxOrgBusinessDetails(ReportQuery reportQuery);
    /**
     * 微信端区域业绩报表
     * @param reportQuery
     * @return
     */
    List<WxRegionBusinessVO> listWxRegionBusinessDetails(ReportQuery reportQuery);
}
