package com.cfpamf.ms.insur.report.service.diagnosis;

import com.cfpamf.ms.insur.report.dao.safepg.*;
import com.cfpamf.ms.insur.report.enums.OrgLevelEnum;
import com.cfpamf.ms.insur.report.pojo.vo.assistant.DiagnosisRankInfoVo;
import com.cfpamf.ms.insur.report.service.diagnosis.model.AbstractDiagnosisModel;
import com.cfpamf.ms.insur.report.service.diagnosis.model.InsuranceAmountRateSummaryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 异业保费配比小结的诊断实现服务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/02/27
 */
@Service
public class InsuranceAmountRateSummaryService extends AbstractBizHelperDiagnosisService {

    @Resource
    private AdsInsuranceCfpamfMarketingProgressDfpMapper cfpamfMapper;

    @Resource
    private AdsInsuranceAreaMarketingProgressDfpMapper areaMapper;

    @Resource
    private AdsInsuranceDistrictMarketingProgressDfpMapper districtMapper;

    @Resource
    private AdsInsuranceBchMarketingProgressDfpMapper branchMapper;

    @Resource
    private AdsInsuranceEmpMarketingProgressDfpMapper personMapper;

    /**
     * 获取受支持的诊断类型
     *
     * @return 诊断类型的枚举类
     */
    @Override
    public DiagnosisTypeEnum getSupportDiagnosisType() {
        return DiagnosisTypeEnum.INSURANCE_AMOUNT_RATE_SUMMARY;
    }

    /**
     * 根据请求参数，创建诊断的数据模型
     *
     * @param request 请求参数
     * @return 诊断的数据模型
     */
    @Override
    protected AbstractDiagnosisModel createModel(DiagnosisRequest request) {
        InsuranceAmountRateSummaryModel model = new InsuranceAmountRateSummaryModel();
        String pt = request.getPt();
        OrgLevelEnum orgLevel = request.getOrgLevel();
        switch (orgLevel) {
            case COUNTRY: {
                InsuranceAmountRateSummaryVo insuranceAmountRateSummaryVo = cfpamfMapper.queryInsuranceAmountRateSummary(pt);
                if (insuranceAmountRateSummaryVo == null) {
                    return null;
                }
                boolean insuranceAmountRateTag = Optional.ofNullable(insuranceAmountRateSummaryVo.getSyOfflineLoanInsuranceRate()).orElse(0d) >= Optional.ofNullable(insuranceAmountRateSummaryVo.getSmOfflineLoanInsuranceRateTarget()).orElse(0d);
                List<DiagnosisRankInfoVo> rankInfoVos = areaMapper.queryInsuranceAmountRateSummaryRankInfo(pt, insuranceAmountRateTag);
                constructModel(rankInfoVos, model, orgLevel, insuranceAmountRateTag, insuranceAmountRateSummaryVo);
                break;
            }
            case AREA: {
                InsuranceAmountRateSummaryVo insuranceAmountRateSummaryVo = areaMapper.queryInsuranceAmountRateSummary(pt, request.getOrgCodes());
                if (insuranceAmountRateSummaryVo == null) {
                    return null;
                }
                boolean insuranceAmountRateTag = Optional.ofNullable(insuranceAmountRateSummaryVo.getSyOfflineLoanInsuranceRate()).orElse(0d) >= Optional.ofNullable(insuranceAmountRateSummaryVo.getSmOfflineLoanInsuranceRateTarget()).orElse(0d);
                List<DiagnosisRankInfoVo> rankInfoVos = null;
                if(request.isExistDistrict()){
                    //区域下有片区则获取片区数据
                    rankInfoVos = districtMapper.queryInsuranceAmountRateSummaryRankInfo(pt, insuranceAmountRateTag, request.getOrgCodes(), orgLevel.getLevel());
                }else{
                    //区域下无片区则获取分支数据
                    rankInfoVos = branchMapper.queryInsuranceAmountRateSummaryRankInfo(pt, insuranceAmountRateTag, request.getOrgCodes(), orgLevel.getLevel(),request.getBlackBchList());
                }
                //设置区域下是否有片区,后续freemaker模板中逻辑判断用
                model.setExistDistrict(request.isExistDistrict());
                constructModel(rankInfoVos, model, orgLevel, insuranceAmountRateTag, insuranceAmountRateSummaryVo);
                break;
            }
            case DISTRICT: {
                InsuranceAmountRateSummaryVo insuranceAmountRateSummaryVo = districtMapper.queryInsuranceAmountRateSummary(pt, request.getOrgCodes());
                if (insuranceAmountRateSummaryVo == null) {
                    return null;
                }
                boolean insuranceAmountRateTag = Optional.ofNullable(insuranceAmountRateSummaryVo.getSyOfflineLoanInsuranceRate()).orElse(0d) >= Optional.ofNullable(insuranceAmountRateSummaryVo.getSmOfflineLoanInsuranceRateTarget()).orElse(0d);
                List<DiagnosisRankInfoVo> rankInfoVos = branchMapper.queryInsuranceAmountRateSummaryRankInfo(pt, insuranceAmountRateTag, request.getOrgCodes(), orgLevel.getLevel(),request.getBlackBchList());
                constructModel(rankInfoVos, model, orgLevel, insuranceAmountRateTag, insuranceAmountRateSummaryVo);
                break;
            }
            case BRANCH: {
                InsuranceAmountRateSummaryVo insuranceAmountRateSummaryVo = branchMapper.queryInsuranceAmountRateSummary(pt, request.getOrgCodes());
                if (insuranceAmountRateSummaryVo == null) {
                    return null;
                }
                boolean insuranceAmountRateTag = Optional.ofNullable(insuranceAmountRateSummaryVo.getSyOfflineLoanInsuranceRate()).orElse(0d) >= Optional.ofNullable(insuranceAmountRateSummaryVo.getSmOfflineLoanInsuranceRateTarget()).orElse(0d);
                List<DiagnosisRankInfoVo> rankInfoVos = personMapper.queryInsuranceAmountRateSummaryRankInfo(pt, insuranceAmountRateTag, request.getOrgCodes());
                constructModel(rankInfoVos, model, orgLevel, insuranceAmountRateTag, insuranceAmountRateSummaryVo);
                break;
            }
            default:
                break;
        }
        return model;
    }

    /**
     * 构建模型
     *
     * @param rankInfoVos                  排名信息
     * @param model                        数据模型
     * @param orgLevel                     机构层级
     * @param insuranceAmountRateTag       异业保费配比是否达标
     * @param insuranceAmountRateSummaryVo 保费配比小结汇总
     */
    private static void constructModel(List<DiagnosisRankInfoVo> rankInfoVos, InsuranceAmountRateSummaryModel model, OrgLevelEnum orgLevel, boolean insuranceAmountRateTag, InsuranceAmountRateSummaryVo insuranceAmountRateSummaryVo) {
        List<InsuranceAmountRateSummaryModel.Summary.RankInfo> rankInfos = new ArrayList<>(3);
        if (CollectionUtils.isNotEmpty(rankInfoVos)) {
            for (DiagnosisRankInfoVo rankInfoVo : rankInfoVos) {
                if (StringUtils.isNotBlank(rankInfoVo.getSyOfflineLoanInsuranceRateRankName())) {
                    InsuranceAmountRateSummaryModel.Summary.RankInfo rankInfo = new InsuranceAmountRateSummaryModel.Summary.RankInfo();
                    rankInfos.add(rankInfo);
                    rankInfo.setName(rankInfoVo.getSyOfflineLoanInsuranceRateRankName());
                    rankInfo.setInsuranceAmountYearRate(DECIMAL_FORMAT.format(BigDecimal.valueOf(Optional.ofNullable(rankInfoVo.getSyOfflineLoanInsuranceRate()).orElse(0d))
                            .setScale(4, RoundingMode.HALF_UP)));
                }
            }
        }
        InsuranceAmountRateSummaryModel.Summary summary = new InsuranceAmountRateSummaryModel.Summary();
        model.setSummary(summary);
        summary.setType(orgLevel.getLevel());
        summary.setFinished(insuranceAmountRateTag);
        summary.setInsuranceAmountYearRate(DECIMAL_FORMAT.format(BigDecimal.valueOf(Optional.ofNullable(insuranceAmountRateSummaryVo.getSyOfflineLoanInsuranceRate()).orElse(0d))
                .setScale(4, RoundingMode.HALF_UP)));
        summary.setRankInfos(rankInfos);
    }

    /**
     * 获取诊断模板文件的地址
     *
     * @return 模板文件的地址
     */
    @Override
    protected String getTemplateFilePath() {
        return "insurance_amount_rate_summary.ftl";
    }

    @Data
    public static class InsuranceAmountRateSummaryVo {

        /**
         * 当年异业保费配比
         */
        private Double syOfflineLoanInsuranceRate;
        /**
         * 当月年累计异业保费配比目标
         */
        private Double smOfflineLoanInsuranceRateTarget;
    }
}
