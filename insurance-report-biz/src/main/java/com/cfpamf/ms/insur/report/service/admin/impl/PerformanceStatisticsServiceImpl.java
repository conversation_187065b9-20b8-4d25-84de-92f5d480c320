package com.cfpamf.ms.insur.report.service.admin.impl;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.report.dao.dw.SmDcReportMapper;
import com.cfpamf.ms.insur.report.enums.ReportType;
import com.cfpamf.ms.insur.report.pojo.query.PerformanceReportQuery;
import com.cfpamf.ms.insur.report.pojo.vo.branch.BranchStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.excel.ExcelVoConvert;
import com.cfpamf.ms.insur.report.pojo.vo.personnel.PersonnelStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.region.RegionStatisticsVo;
import com.cfpamf.ms.insur.report.pojo.vo.total.TotalStatisticsVo;
import com.cfpamf.ms.insur.report.service.admin.BaseService;
import com.cfpamf.ms.insur.report.service.admin.PerformanceStatisticsService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 明细报表查询/导出
 *
 * <AUTHOR>
 * @date 2021/3/31 14:35
 */
@Service
public class PerformanceStatisticsServiceImpl extends BaseService implements PerformanceStatisticsService {

    private SmDcReportMapper smDcReportMapper;

    public PerformanceStatisticsServiceImpl(SmDcReportMapper smDcReportMapper) {
        this.smDcReportMapper = smDcReportMapper;
    }

    @Override
    public PageInfo<PersonnelStatisticsVo> searchPersonalPerformance(PerformanceReportQuery performanceReportQuery) {
        //查询数据返回
        handlePage(performanceReportQuery);
        //查询返回
        return new PageInfo<>(smDcReportMapper.searchPersonalPerformance(performanceReportQuery));
    }

    @Override
    public PageInfo<BranchStatisticsVo> searchBranchPerformance(PerformanceReportQuery performanceReportQuery) {
        //处理分页信息
        handlePage(performanceReportQuery);
        //查询数据返回
        return new PageInfo<>(smDcReportMapper.searchBranchPerformance(performanceReportQuery));
    }

    @Override
    public PageInfo<RegionStatisticsVo> searchRegionPerformance(PerformanceReportQuery performanceReportQuery) {
        //处理分页信息
        handlePage(performanceReportQuery);
        //查询数据返回
        return new PageInfo<>(smDcReportMapper.searchRegionPerformance(performanceReportQuery));
    }

    @Override
    public PageInfo<TotalStatisticsVo> searchTotalPerformance(PerformanceReportQuery performanceReportQuery) {
        //处理分页信息
        handlePage(performanceReportQuery);
        //查询数据返回
        return new PageInfo<>(smDcReportMapper.searchTotalPerformance(performanceReportQuery));
    }

    @Override
    public List getExcelVo(PerformanceReportQuery performanceReportQuery, String code) {
        //获取导出类型的枚举类型
        ReportType reportType = ReportType.getReportTypeByCode(code);
        if (Objects.isNull(reportType)) {
            throw new MSBizNormalException("9999999", "业绩报表没有该类型的报表" + code);
        }
        performanceReportQuery.setAll(true);
        //获取统计到的数据集合
        List statisticsList;
        switch (reportType) {
            case PERSONAL:
                statisticsList = searchPersonalPerformance(performanceReportQuery).getList();
                break;
            case ORG:
                statisticsList = searchBranchPerformance(performanceReportQuery).getList();
                break;
            case REGION:
                statisticsList = searchRegionPerformance(performanceReportQuery).getList();
                break;
            case TOTAL:
                statisticsList = searchTotalPerformance(performanceReportQuery).getList();
                break;
            default:
                throw new MSBizNormalException("9999999", "业绩报表没有该类型的报表" + code);
        }
        //转换成excelVo返回
        return convertStatisticsVo(statisticsList);
    }

    /**
     * 统计的明细信息集合转换成ExcelVo集合
     *
     * @param statisticsList
     * @return
     */
    public List convertStatisticsVo(List<ExcelVoConvert> statisticsList) {
        if (CollectionUtils.isEmpty(statisticsList)) {
            return Lists.newArrayList();
        }
        //查询返回
        return statisticsList
                .stream()
                .map(ExcelVoConvert::convert)
                .collect(Collectors.toList());
    }

}
