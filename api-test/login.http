### 登录-获取bms-token 客户经理
POST http://bms-test.cdfinance.com.cn/api/bms/biz/oauth/token?systemId=3
Content-Type: application/json

{
  "account": "CNBJ0621",
  "password": "ZQ3nEQbyyyiGPnh9+2WVSP6mB/IUUhLy58KFzGsrJ3I0+Ob9xvr3M0f2C7WoUSLUh5vPe9kaLofDcT3SKhOGQQkrbpng2aZJjy6prCZcopWZvl9iHBu9COZKfPrp0sWbv1VQRgD3JuiDXchXqrO8MbxOeQSwMU6yiKt8oxMNzkI=",
  "publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZj0GMFgkmV/IllPJyKJIdrSKzkuacr3VDpTieYcBNPnTxOR3CNeXTiYh/BWXNkyMNoLJ3LIZz8Iz3iPofBVgsbXFNgs909Np+52+KpjUuMrFAHO4Z3LNjzMem6EcJ6aD8x2bKYoegGlHDPH2092EViVFfViUQFBoDiMdG+y/FDQIDAQAB",
  "useSso": true
}

> {%

    client.test("修改token", function () {
        client.assert(response.body.data && response.body.data.authorization)
        client.global.set("bms29-token", response.body.data.authorization)
        // client.global.set("bms29-token", "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI2NTM1IiwiZW1wbG95ZWVJZCI6IjY2ODEiLCJoclVzZXJJZCI6IjEyMTQxNjk4NiIsImFjY291bnQiOiIxNTU3NTE0MjM3NyIsInVzZXJOYW1lIjoi6YOR6Z2WIiwiam9iTnVtYmVyIjoiWkhOWDA5NzYwIiwibWFzdGVySm9iTnVtYmVyIjoiWkhOWDA5NzYwIiwib3JnSWQiOiI1NSIsImhyT3JnSWQiOiIyODQ1MTYiLCJock9yZ0NvZGUiOiJDTlNEIiwiaHJPcmdOYW1lIjoi5bGx5Lic5Yy65Z-fIiwiYXJlYU9yZ0NvZGUiOiIiLCJock9yZ1RyZWVQYXRoIjoiOTAwMTA1MTUzLzI4MjczMi8yODQ1MTYiLCJ1c2VyVHlwZSI6IjMiLCJyYW5kb21Db2RlIjoiRUE4MDI5MjAtRUZERS00ODE1LTkyQjUtMTQwODA0MUJEM0I4IiwidXNlU3NvIjoidHJ1ZSIsInN5c3RlbUlkIjoiMyIsImNoYW5uZWwiOiIiLCJjcmVhdGVUaW1lIjoiMTY3NzIxODk3NTg2MSIsImdyYXkiOjB9.nYjZrB1blayUgesmJx-arJbhgCIanW_ZIubvmvl5z3cRPZF6eQV5Mvdf_jO4rGAgDGGXSpCfXYgk6Z4zqZMaFg")
    })
%}
