### 获取 token 总部 o9F_AwzxkEvQfrlfLhxO5GBMg9V4 区域总 HBGP0001 主任  HBTH0002
GET http://insurance-biz.tsg.cfpamf.com/wx/cancel/_reload_user_token?openId=o9F_Aw5GLgq72cwjMK_Qxd0rYM3U
Content-Type: application/json

> {%

    client.test("修改token", function () {
        client.assert(response.body.data && response.body.data.authorization)
        client.global.set("bmsToken", response.body.data.authorization)
    })
%}


### 获取单行数据 分支
POST {{host}}/assistant/admin/single
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "BRANCH",
  "bchCode": "HNYG"
}

### 获取趋势图数据 - 分支
POST {{host}}/assistant/admin/tendency
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "BRANCH",
  "bchCode": "GSYD"
}


### 获取保费排名 - 分支 （员工数据） - 本月
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "BRANCH",
  "bchCode": "GSYD",
  "selectCols": [
    "sm_assess_convert_insurance_amt",
    "sm_assess_convert_insurance_amt_target",
    "sm_assess_convert_insurance_amt_achieve_rate"
  ],
  "orderByClauses": [
    {
      "colName": "sm_assess_convert_insurance_amt_achieve_rate",
      "clause": "desc"
    },
    {
      "colName": "sm_assess_convert_insurance_amt",
      "clause": "desc"
    }
  ]
}



### 获取异业保费配比 - 分支 （员工数据） - 本年
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "BRANCH",
  "selectCols": [
    "sy_offline_loan_insurance_rate"
  ],
  "orderByClauses": [
    {
      "colName": "sy_offline_loan_insurance_rate",
      "clause": "desc"
    }
  ]
}

### 获取排名-留存率 - 分支 （员工数据） - 本年
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "BRANCH",
  "selectCols": [
    "sy_insurance_retention_rate"
  ],
  "orderByClauses": [
    {
      "colName": "sy_insurance_retention_rate",
      "clause": "desc"
    }
  ]
}

### 获取排名-信贷非贷留存率 - 分支 （员工数据） - 本年
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "BRANCH",
  "selectCols": [
    "sy_loan_insurance_retention_rate","sy_unloan_insurance_retention_rate"
  ],
  "orderByClauses": [
    {
      "colName": "sy_loan_insurance_retention_rate",
      "clause": "desc"
    },
    {
      "colName": "sy_unloan_insurance_retention_rate",
      "clause": "desc"
    }
  ]
}

### 获取排名-断保跟进率- 分支 （员工数据）
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "BRANCH",
  "selectCols": [
    "sy_interruption_todo_cnt","sy_interruption_todo_follow_rate","sy_interruption_todo_conversion_rate","sy_interruption_todo_conversion_amt"
  ],
  "orderByClauses": [
    {
      "colName": "sy_interruption_todo_conversion_rate",
      "clause": "desc"
    },
    {
      "colName": "sy_interruption_todo_follow_rate",
      "clause": "desc"
    }
  ]
}


### 获取排名-续保- 分支 （员工数据）
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "BRANCH",
  "selectCols": [
    "sy_renew_short_todo_cnt","sy_renew_short_todo_follow_rate","sy_renew_short_todo_conversion_rate","sy_renew_short_todo_conversion_amt"
  ],
  "orderByClauses": [
    {
      "colName": "sy_interruption_todo_conversion_rate",
      "clause": "desc"
    },
    {
      "colName": "sy_interruption_todo_follow_rate",
      "clause": "desc"
    }
  ]
}


