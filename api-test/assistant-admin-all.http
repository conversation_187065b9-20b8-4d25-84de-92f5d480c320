### 获取 token 总部 o9F_AwzxkEvQfrlfLhxO5GBMg9V4 区域总 HBGP0001 主任  HBTH0002
GET http://insurance-biz.tsg.cfpamf.com/wx/cancel/_reload_user_token?openId=o9F_AwzxkEvQfrlfLhxO5GBMg9V4
Content-Type: application/json

> {%

    client.test("修改token", function () {
        client.assert(response.body.data && response.body.data.authorization)
        client.global.set("bmsToken", response.body.data.authorization)
    })
%}

### 获取单行数据 -全国
POST {{host}}/assistant/admin/single
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "ALL"
}


### 获取趋势图数据 - 全国
POST {{host}}/assistant/admin/single
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "ALL",
  "areaCode": "",
  "pt": "20240313"
}


### 获取排名-断保跟进率- 全国 （区域数据）
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "AREA",
  "dimName": "定",
  "areaCode": "HBQYJG",
  "size": 400,
  "pt": "20240320",
  "selectCols": [
    "sm_assess_convert_insurance_amt_target"
  ],
  "orderByClauses": [
    {
      "colName": "sm_assess_convert_insurance_amt_target",
      "clause": "desc"
    }
  ]
}


### 获取排名-续保- 全国 （区域数据）
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "ALL",
  "areaCode": "",
  "pt": "20240408",
  "selectCols": [
    "sy_interruption_todo_cnt",
    "sy_interruption_todo_follow_rate",
    "sy_interruption_todo_conversion_rate",
    "sy_interruption_todo_conversion_amt",
    "sy_tdo_short_follow_policy"
  ],
  "orderByClauses": [
    {
      "colName": "sy_interruption_todo_conversion_rate",
      "clause": "desc"
    },
    {
      "colName": "sy_interruption_todo_follow_rate",
      "clause": "desc"
    }
  ]
}



### 获取保费排名 - 区域 （分支数据） - 本年
POST {{host}}/assistant/admin/rankSmart
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "areaDim": "ALL",
  "areaCode": "",
  "districtCodes": [],
  "bchCode": "",
  "timeDim": "MONTH",
  "pt": "20240408",
  "page": 1,
  "size": 1000,
  "count": true,
  "selectCols": [
    "sy_offline_loan_insurance_rate",
    "sy_assess_convert_insurance_amt",
    "sy_assess_convert_insurance_amt_target",
    "sy_assess_convert_insurance_amt_avg"
  ],
  "orderByClauses": [
    {
      "colName": "sy_assess_convert_insurance_amt",
      "clause": "asc"
    },
    {
      "colName": "sy_assess_convert_insurance_amt_target",
      "clause": "asc"
    }
  ]
}