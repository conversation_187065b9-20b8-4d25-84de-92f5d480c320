POST http://insurance-report.tsg.cfpamf.com//assistant/admin/diagnosis
Authorization: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDEzIiwiZW1wbG95ZWVJZCI6IjU4IiwiaHJVc2VySWQiOiIxMDkyMjUxNzgiLCJhY2NvdW50IjoiMTM5NTQ0MTAzNDAiLCJ1c2VyTmFtZSI6IueOi-W-t-S4gCIsImpvYk51bWJlciI6IkNOQkowMjExIiwibWFzdGVySm9iTnVtYmVyIjoiQ05CSjAyMTEiLCJvcmdJZCI6IjkzMSIsImhyUG9zdElkIjoiMzA0MjMyIiwicG9zdElkIjoiMzE0IiwiaHJPcmdJZCI6IjkwMDEwNTE4OSIsImhyT3JnQ29kZSI6IkhCUVlHTEIiLCJock9yZ05hbWUiOiLmsrPljJfljLrln5_nrqHnkIbpg6giLCJhcmVhT3JnQ29kZSI6IkhCUVlKRyIsImhyT3JnVHJlZVBhdGgiOiI5MDAxMDUxNTMvMjgyNzE3LzM2MDY3Mi85MDAxMDUxODkiLCJ1c2VyVHlwZSI6IjIiLCJyYW5kb21Db2RlIjoiODYzRjIwODktQUQyMy00NDc3LUFCNTAtODVGOTgzMDdGNjQxIiwidXNlU3NvIjoiZmFsc2UiLCJzeXN0ZW1JZCI6IjMiLCJjaGFubmVsIjoiIiwiY3JlYXRlVGltZSI6IjE3MDk2OTAzMjM1NjEiLCJncmF5IjowfQ.I3aoOntExibCgFywenZR4ywctrTpsb5XMONe3oOmPCg_eJvd8mq1kT7O7B_ObjictOp4ebEq4RlsKLngqwbyRg
Content-Type: application/json


{
  "timeDim": "YEAR",
  "areaDim": "AREA",
  "areaCode": "HBQYJG",
  "diagnosisType": "DIAGNOSIS_AND_CONCLUSION"
}


### 测试诊断
POST https://bmstest.cdfinance.com.cn/api/insurance/report/assistant/admin/diagnosis
Authorization: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDEzIiwiZW1wbG95ZWVJZCI6IjU4IiwiaHJVc2VySWQiOiIxMDkyMjUxNzgiLCJhY2NvdW50IjoiMTM5NTQ0MTAzNDAiLCJ1c2VyTmFtZSI6IueOi-W-t-S4gCIsImpvYk51bWJlciI6IkNOQkowMjExIiwibWFzdGVySm9iTnVtYmVyIjoiQ05CSjAyMTEiLCJvcmdJZCI6IjkzMSIsImhyUG9zdElkIjoiMzA0MjMyIiwicG9zdElkIjoiMzE0IiwiaHJPcmdJZCI6IjkwMDEwNTE4OSIsImhyT3JnQ29kZSI6IkhCUVlHTEIiLCJock9yZ05hbWUiOiLmsrPljJfljLrln5_nrqHnkIbpg6giLCJhcmVhT3JnQ29kZSI6IkhCUVlKRyIsImhyT3JnVHJlZVBhdGgiOiI5MDAxMDUxNTMvMjgyNzE3LzM2MDY3Mi85MDAxMDUxODkiLCJ1c2VyVHlwZSI6IjIiLCJyYW5kb21Db2RlIjoiODYzRjIwODktQUQyMy00NDc3LUFCNTAtODVGOTgzMDdGNjQxIiwidXNlU3NvIjoiZmFsc2UiLCJzeXN0ZW1JZCI6IjMiLCJjaGFubmVsIjoiIiwiY3JlYXRlVGltZSI6IjE3MDk2OTAzMjM1NjEiLCJncmF5IjowfQ.I3aoOntExibCgFywenZR4ywctrTpsb5XMONe3oOmPCg_eJvd8mq1kT7O7B_ObjictOp4ebEq4RlsKLngqwbyRg
Content-Type: application/json

{
  "timeDim": "YEAR",
  "areaDim": "AREA",
  "areaCode": "HBQYJG",
  "diagnosisType": "INSURANCE_AMOUNT_RATE_SUMMARY"
}


### 吃吧
POST https://bmstest.cdfinance.com.cn/api/insurance/report/assistant/admin/single
Authorization: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDEzIiwiZW1wbG95ZWVJZCI6IjU4IiwiaHJVc2VySWQiOiIxMDkyMjUxNzgiLCJhY2NvdW50IjoiMTM5NTQ0MTAzNDAiLCJ1c2VyTmFtZSI6IueOi-W-t-S4gCIsImpvYk51bWJlciI6IkNOQkowMjExIiwibWFzdGVySm9iTnVtYmVyIjoiQ05CSjAyMTEiLCJvcmdJZCI6IjkzMSIsImhyUG9zdElkIjoiMzA0MjMyIiwicG9zdElkIjoiMzE0IiwiaHJPcmdJZCI6IjkwMDEwNTE4OSIsImhyT3JnQ29kZSI6IkhCUVlHTEIiLCJock9yZ05hbWUiOiLmsrPljJfljLrln5_nrqHnkIbpg6giLCJhcmVhT3JnQ29kZSI6IkhCUVlKRyIsImhyT3JnVHJlZVBhdGgiOiI5MDAxMDUxNTMvMjgyNzE3LzM2MDY3Mi85MDAxMDUxODkiLCJ1c2VyVHlwZSI6IjIiLCJyYW5kb21Db2RlIjoiNEU1N0Y5MDktNTIyMi00RUVDLUFCRjUtMTJDQzM3MDIzNjJCIiwidXNlU3NvIjoidHJ1ZSIsInN5c3RlbUlkIjoiMzAiLCJjaGFubmVsIjoienN6aCIsImNyZWF0ZVRpbWUiOiIxNzEwMTIyMzQzMTI1IiwiZ3JheSI6MH0.44wxBnV1z0dtnlhyLwXtskrlk5x-BG3nePFiPydcEQY9rhEqVQcaLNW7oJNh7KqN6kLH_uLPQ3jpM4WIUJcCWQ
Content-Type: application/json

{
  "areaDim": "District",
  "areaCode": "HBQYJG",
  "districtCodes": [
    "ZJK"
  ],
  "bchCode": null,
  "timeDim": "MONTH",
  "pt": "20240305"
}



### bugf rank
POST {{host}}/assistant/admin/tendency
Content-Type: application/json
Authorization: {{bmsToken}}

{"areaDim":"BRANCH","areaCode":"CNHN","districtCodes":["XDPQ1"],"bchCode":"JXXG","timeDim":"MONTH","pt":"20240313"}


###户经理又是PCO（ZHNX01899
POST {{host}}/assistant/admin/single
Content-Type: application/json
Authorization: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxMjEyNSIsImVtcGxveWVlSWQiOiIxMjg2NiIsImhyVXNlcklkIjoiMTAwMDAwMDkwNyIsImFjY291bnQiOiIxNTMxMDQ1MjYxMSIsInVzZXJOYW1lIjoi6ZmI5omAIiwiam9iTnVtYmVyIjoiWkhOWDMwODI2IiwibWFzdGVySm9iTnVtYmVyIjoiWkhOWDMwODI2Iiwib3JnSWQiOiI4MDAiLCJoclBvc3RJZCI6IjEwMDU2OCIsInBvc3RJZCI6IjgzIiwiaHJPcmdJZCI6IjExOTEwMjQiLCJock9yZ0NvZGUiOiJITkxZIiwiaHJPcmdOYW1lIjoi54KO6Zm1IiwiYXJlYU9yZ0NvZGUiOiIiLCJock9yZ1RyZWVQYXRoIjoiOTAwMTA1MTUzLzI4MjczMi8yODQ1MTcvOTAwMTA1MTYyLzExOTEwMjQiLCJ1c2VyVHlwZSI6IjIiLCJyYW5kb21Db2RlIjoiMkMzODAyNzctM0RENS00RDZCLUFEMzMtMjlEQjU4NjJBNjA0IiwidXNlU3NvIjoidHJ1ZSIsInN5c3RlbUlkIjoiMzAiLCJjaGFubmVsIjoienN6aCIsImNyZWF0ZVRpbWUiOiIxNzEwNzI4Mzg5OTI1IiwiZ3JheSI6MH0.Dzdlc3Hp_QvsdMI_svxKVL8sdKZsDk14rZXB38NffVZzxTq2t1c91HOLD7vBWZz8_TOYZcGYaLMRPrGBipxbaw

{"areaDim":"BRANCH","areaCode":"CNHN","districtCodes":["XDPQ1"],"bchCode":"HNLY","timeDim":"MONTH","pt":"20240313"}




###  rank
POST {{host}}/assistant/admin/rankSmart
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "areaDim": "BRANCH",
  "areaCode": "HBQYJG",
  "districtCodes": [
    "ZJK"
  ],
  "bchCode": "HBGY",
  "timeDim": "MONTH",
  "pt": "20240408",
  "page": 1,
  "size": 5,
  "count": true,
  "selectCols": [
    "sy_assess_convert_insurance_amt",
    "sy_assess_convert_insurance_amt_target",
    "sy_assess_convert_insurance_amt_achieve_rate",
    "sy_assess_convert_insurance_amt_avg"
  ],
  "orderByClauses": [
    {
      "colName": "sy_assess_convert_insurance_amt",
      "clause": "desc"
    },
    {
      "colName": "sy_assess_convert_insurance_amt_target",
      "clause": "desc"
    },
    {
      "colName": "sy_assess_convert_insurance_amt_achieve_rate",
      "clause": "desc"
    },
    {
      "colName": "sy_assess_convert_insurance_amt_avg",
      "clause": "desc"
    }
  ]
}