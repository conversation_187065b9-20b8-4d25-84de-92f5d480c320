### 获取 token 总部 o9F_AwzxkEvQfrlfLhxO5GBMg9V4 区域总 CNBJ0211 主任  HBTH0002
GET http://insurance-biz.tsg.cfpamf.com/wx/cancel/_reload_user_token?openId=CNBJ0211
Content-Type: application/json

> {%

    client.test("修改token", function () {
        client.assert(response.body.data && response.body.data.authorization)
        client.global.set("bmsToken", response.body.data.authorization)
    })
%}

### 获取趋势图数据 - 区域（注意 token）
POST {{host}}/assistant/admin/tendency
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "MONTH",
  "areaDim": "AREA",
  "areaCode": "HBQYJG"
}


### 获取保费排名 - 区域 （分支数据） - 本年
POST {{host}}/assistant/admin/rankSmart
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "MONTH",
  "areaDim": "AREA",
  "areaCode": "HBQYJG",
  "pt": "20240305",
  "selectCols": [
    "sy_assess_convert_insurance_amt",
    "sy_assess_convert_insurance_amt_target",
    "sy_assess_convert_insurance_amt_achieve_rate"
  ],
  "orderByClauses": [
    {
      "colName": "sy_assess_convert_insurance_amt_achieve_rate",
      "clause": "desc"
    },
    {
      "colName": "sy_assess_convert_insurance_amt",
      "clause": "desc"
    }
  ]
}

### 获取保费排名 - 区域 （分支数据） - 本年
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "MONTH",
  "areaDim": "AREA",
  "areaCode": "HBQYJG",
  "selectCols": [
    "sy_assess_convert_insurance_amt",
    "sy_assess_convert_insurance_amt_target",
    "sy_assess_convert_insurance_amt_achieve_rate"
  ],
  "orderByClauses": [
    {
      "colName": "sy_assess_convert_insurance_amt_achieve_rate",
      "clause": "desc"
    },
    {
      "colName": "sy_assess_convert_insurance_amt",
      "clause": "desc"
    }
  ]
}

### 获取排名-断保- 区域 （分支数据）
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "AREA",
  "areaCode": "HBQYJG",
  "selectCols": [
    "sy_interruption_todo_cnt","sy_interruption_todo_follow_rate","sy_interruption_todo_conversion_rate","sy_interruption_todo_conversion_amt"
  ],
  "orderByClauses": [
    {
      "colName": "sy_interruption_todo_conversion_rate",
      "clause": "desc"
    },
    {
      "colName": "sy_interruption_todo_follow_rate",
      "clause": "desc"
    }
  ]
}

### 获取排名-续保-  区域 （分支数据）
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "AREA",
  "areaCode": "HBQYJG",
  "selectCols": [
    "sy_renew_short_todo_cnt","sy_renew_short_todo_follow_rate","sy_renew_short_todo_conversion_rate","sy_renew_short_todo_conversion_amt"
  ],
  "orderByClauses": [
    {
      "colName": "sy_interruption_todo_conversion_rate",
      "clause": "desc"
    },
    {
      "colName": "sy_interruption_todo_follow_rate",
      "clause": "desc"
    }
  ]
}

### 获取排名-信贷非贷留存率 - 区域 （员工数据） - 本年
POST {{host}}/assistant/admin/rank
Content-Type: application/json
Authorization: {{bmsToken}}

{
  "timeDim": "YEAR",
  "areaDim": "AREA",
  "areaCode": "HBQYJG",
  "selectCols": [
    "sy_loan_insurance_retention_rate","sy_unloan_insurance_retention_rate"
  ],
  "orderByClauses": [
    {
      "colName": "sy_loan_insurance_retention_rate",
      "clause": "desc"
    },
    {
      "colName": "sy_unloan_insurance_retention_rate",
      "clause": "desc"
    }
  ]
}